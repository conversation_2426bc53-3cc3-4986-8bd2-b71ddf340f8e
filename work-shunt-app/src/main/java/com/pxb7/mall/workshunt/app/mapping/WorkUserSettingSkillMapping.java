package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingSkillDTO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqSaveWorkUserSettingSkillBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 用户技能 -> WorkUserSettingSkill  mapstruct
*/
@Mapper
public interface WorkUserSettingSkillMapping {

    WorkUserSettingSkillMapping INSTANCE = Mappers.getMapper(WorkUserSettingSkillMapping.class);
    /**
    * 用户技能 -> Save
    */
    @Mapping(target = "skills", source = "skills")
    ReqSaveWorkUserSettingSkillBO toReqSaveWorkUserSettingSkillBOFromDto(ReqSaveWorkUserSettingSkillDTO dto);
}
