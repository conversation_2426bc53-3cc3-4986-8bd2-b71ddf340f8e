package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.workshunt.infra.enums.ShuntRuleTypeEnum;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.ImGateway.DTO;
import com.pxb7.mall.workshunt.app.model.WorkSkillLevelDTO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.infra.enums.ShuntAssignAlgorithmEnum;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntOrderVolume;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.ImGateway;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;



@Service
@Slf4j
public class ShuntAssignAlgorithmService {

    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private ImGateway imGateway;


    /**
     * 分配人员算法 0 不分配， 1 轮流分配  2 根据饱和度比例分配, 3 根据客服并发会话数分配
     *
     * @param skills
     * @param workUsers
     * @param ruleSetting
     * @param isMultiskilled
     * @param requestCount
     * @return
     */
    public List<WorkUser> assignAlgorithm(List<WorkSkillLevelDTO> skills,
                                          List<WorkUser> workUsers,
                                          WorkShuntRuleSetting ruleSetting,
                                          boolean isMultiskilled,
                                          int requestCount) {

        //如果是一个客服具备多个技能，在多个技能中随机选一个技能的规则
        String skillId = getShuffleSkillIdMultiSkillsOwenWorkerUser(isMultiskilled, skills, WorkSkillLevelDTO::getSkillId);
        if (isMultiskilled) {
            return assignAlgorithmRotation(workUsers, requestCount, skillId);
        }

        return switch (ShuntAssignAlgorithmEnum.getByShuntAlgorithmType(ruleSetting.getShuntType())) {
            case NO_ASSIGN -> new ArrayList<>();
            case ROOM_AVERAGE -> assignAlgorithmRotation(workUsers, requestCount, skillId);
            case RATIO -> assignAlgorithmRatio(workUsers, requestCount, skillId);
            case SESSION_AVERAGE -> assignAlgorithmSessionAverage(workUsers, requestCount, skillId);
        };

    }



    private static String getShuffleSkillIdMultiSkillsOwenWorkerUser(boolean isMultiSkilled, List<WorkSkillLevelDTO> skills, Function<WorkSkillLevelDTO, String> apply) {
        List<String> result = new ArrayList<>(skills.stream().map(apply).toList());
        return Option.when(isMultiSkilled, () -> {
            Collections.shuffle(result);
            return result.stream().limit(1).toList().get(0);
        }).getOrElse(() -> result.get(0));
    }

    /**
     * 如果没有设置分配规则，则使用默认的规则：轮流分配
     * @param workUsers
     * @param requestCount
     * @param skillCode
     * @return
     */
    public List<WorkUser> defaultAssignAlgorithm(List<WorkUser> workUsers, int requestCount, String skillCode){
        return assignAlgorithmRotation(workUsers, requestCount, skillCode);
    }


    /**
     * 轮流分配算法
     *
     * @param workUsers    可分配的用户列表
     * @param requestCount 请求分配的用户数量
     * @param skillId      技能ID
     * @return 分配后的用户列表
     */
    private List<WorkUser> assignAlgorithmRotation(List<WorkUser> workUsers, int requestCount, String skillId) {
        if (CollUtil.isEmpty(workUsers) || requestCount <= 0) {
            return Collections.emptyList();
        }
        if (requestCount >= workUsers.size()) {
            return new ArrayList<>(workUsers);
        }

        List<String> userIds = workUsers.stream().map(WorkUser::getUserId).toList();
        Map<String, WorkShuntOrderVolume> volumeMap = queryVolumeMap(skillId, userIds);
        List<WorkUser> unassigned = filterUnassignedUsers(workUsers, volumeMap);
        if (unassigned.size() >= requestCount) {
            return pickRandomUsers(unassigned, requestCount);
        }

        List<WorkUser> assigned = filterAssignedUsers(workUsers, volumeMap);
        return mergeRoomUsers(unassigned, assigned, volumeMap, requestCount);
    }



    private Map<String, WorkShuntOrderVolume> queryVolumeMap(String skillId, List<String> workUserIds) {
        return workShuntOrderVolumeRepository.queryBySkillIdAndUserIds(skillId, workUserIds)
                .stream()
                .collect(Collectors.toMap(WorkShuntOrderVolume::getUserId, v -> v, (v1, v2) -> v1));
    }


    private List<WorkUser> filterUnassignedUsers(List<WorkUser> workUsers, Map<String, WorkShuntOrderVolume> volumeMap) {
        List<WorkUser> unassigned = new ArrayList<>();
        for (WorkUser user : workUsers) {
            WorkShuntOrderVolume vol = volumeMap.get(user.getUserId());
            if (vol == null || vol.getAssignTime() == null) {
                unassigned.add(user);
            }
        }
        return unassigned;
    }

    private List<WorkUser> filterAssignedUsers(List<WorkUser> workUsers, Map<String, WorkShuntOrderVolume> volumeMap) {
        List<WorkUser> assigned = new ArrayList<>();
        for (WorkUser user : workUsers) {
            WorkShuntOrderVolume vol = volumeMap.get(user.getUserId());
            if (vol != null && vol.getAssignTime() != null) {
                assigned.add(user);
            }
        }
        return assigned;
    }

    private List<WorkUser> pickRandomUsers(List<WorkUser> users, int count) {
        Collections.shuffle(users);
        return users.subList(0, count);
    }

    private List<WorkUser> mergeRoomUsers(List<WorkUser> unassigned, List<WorkUser> assigned,
                                      Map<String, WorkShuntOrderVolume> volumeMap, int requestCount) {
        List<WorkUser> result = new ArrayList<>(unassigned);
        int need = requestCount - unassigned.size();
        assigned.stream()
                .sorted(Comparator.comparing(u -> volumeMap.get(u.getUserId()).getAssignTime()))
                .limit(need)
                .forEach(result::add);
        return result;
    }


    private List<WorkUser> assignAlgorithmSessionAverage(List<WorkUser> workUsers,
                                                         int requestCount,
                                                         String skillId) {

        if(CollUtil.isEmpty(workUsers) || requestCount <= 0) {
            return Collections.emptyList();
        }

        if (requestCount >= workUsers.size()) {
            return new ArrayList<>(workUsers);
        }

        final List<String> userIds = workUsers.stream().map(WorkUser::getUserId).toList();
        final List<String> workUserIds = workUsers.stream().map(WorkUser::getWorkUserId).toList();
        List<DTO> dtos = imGateway.getConsumerSessionCount(userIds);
        log.info("im dubbo getConsumerSessionCount:{}", JSON.toJSONString(dtos));
        List<WorkUserSettingSkill> settingSkill = userSettingSkillDomainService.queryByWorkUserIdsAndSkillId(workUserIds, skillId);

        Map<String, WorkUserSettingSkill> settingMap = settingSkill.stream().collect(Collectors.toMap(WorkUserSettingSkill::getWorkUserId, a -> a, (k1, k2) -> k1));
        Map<String, DTO> sessionCountMap = dtos.stream().collect(Collectors.toMap(DTO::getUserId, a -> a, (k1, k2) -> k1));
        Map<String, WorkShuntOrderVolume> volumeMap = queryVolumeMap(skillId, userIds);
        Map<String, WorkUser> workUserMap = workUsers.stream().collect(Collectors.toMap(WorkUser::getUserId, a -> a, (k1, k2) -> k1));

        List<WorkUser> unassigned = filterUnassignedUsers(workUsers, volumeMap);
        if(unassigned.size() >= requestCount) {
            return pickRandomUsers(unassigned, requestCount);
        }

        List<WorkUser> result = new ArrayList<>(unassigned);

        List<UserSession> userSessions = workUsers.stream().map(p -> buildUserSession(p, sessionCountMap, volumeMap.get(p.getUserId()), settingMap)).sorted().toList();
        log.info("user session data:{}", JSON.toJSONString(userSessions));

        userSessions.stream().map(p -> workUserMap.get(p.getUserId())).limit(requestCount - unassigned.size()).forEach(result::add);

        return result;
    }



    private UserSession buildUserSession(WorkUser workUser,
                                         Map<String, DTO> sessionCountMap,
                                         WorkShuntOrderVolume volume,
                                         Map<String, WorkUserSettingSkill> workUserSettingSkillMap) {

        DTO dto = sessionCountMap.getOrDefault(workUser.getUserId(), new DTO());
        WorkUserSettingSkill workUserSettingSkill = workUserSettingSkillMap.getOrDefault(workUser.getWorkUserId(), new WorkUserSettingSkill());
        return UserSession.builder()
                .userId(workUser.getUserId())
                .maxLimit(workUserSettingSkill.getMaxSessionLimit())
                .isOverMaxLimit(Objects.nonNull(workUserSettingSkill.getMaxSessionLimit()) && workUserSettingSkill.getMaxSessionLimit() <= dto.getSessionCount())
                .sessionCount(dto.getSessionCount())
                .assignDateTime(volume.getAssignTime())
                .build();
    }


    @Getter
    @Builder
    static class UserSession implements Comparable<UserSession> {
        private String userId;
        private Integer sessionCount;
        private boolean isOverMaxLimit;
        private LocalDateTime assignDateTime;
        private Long maxLimit;


        @Override
        public int compareTo(@NotNull UserSession other) {
            if (this.assignDateTime == null && other.assignDateTime == null) {
                return 0;
            }
            if (this.assignDateTime == null) {
                return -1;
            }
            if (other.assignDateTime == null) {
                return 1;
            }
            return this.assignDateTime.compareTo(other.assignDateTime);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            UserSession that = (UserSession) o;
            return Objects.equals(userId, that.userId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId);
        }
    }


    /**
     * 饱和度比例分配
     *
     * @param workUsers
     * @param requestCount
     * @return
     */
    @Deprecated(since = "2025-08", forRemoval = true)
    private List<WorkUser> assignAlgorithmRatio(List<WorkUser> workUsers, int requestCount, String skillId) {
        if (workUsers.size() <= requestCount) {
            return workUsers;
        }

        Map<String, WorkUser> maps = workUsers.stream()
            .collect(Collectors.toMap(WorkUser::getUserId, a -> a, (k1, k2) -> k1));
        //饱和度排序
        List<Volume> userVolumes = workUsers.stream()
            .map(wu -> {
                WorkUserSettingSkill wss = userSettingSkillDomainService.queryByWorkUserIdAndSkillId(wu.getWorkUserId(), skillId);
                WorkShuntOrderVolume volume = workShuntOrderVolumeRepository.queryBySkillIdAndUserId(skillId, wu.getUserId());
                double per = Option.when(!Objects.isNull(volume), () -> (double)volume.getVolume() / wss.getMaxLimit() * 100)
                    .getOrElse(() -> 0.0);
                return Volume.builder().per(per).userId(wu.getUserId()).build();
            })
            .toList()
            .stream()
            .sorted(Comparator.comparing(Volume::getPer))
            .limit(requestCount)
            .toList();

        return Option.when(CollUtil.isNotEmpty(userVolumes), () -> userVolumes.stream()
            .map(uv -> maps.get(uv.getUserId()))
            .toList()).getOrElse(ArrayList::new);

    }

    @Data
    @Builder
    static class Volume {
        String userId;
        double per;
    }

}
