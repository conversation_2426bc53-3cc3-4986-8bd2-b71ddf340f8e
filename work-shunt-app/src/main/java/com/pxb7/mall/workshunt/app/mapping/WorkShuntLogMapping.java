package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.client.work.GetSaveWorkShuntDubboDTO;
import com.pxb7.mall.workshunt.client.work.GetWorkShuntGroupDubboDTO;
import com.pxb7.mall.workshunt.client.work.GetWorkVerifyLogIdDubboDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.*;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 请求日志记录 -> WorkShuntLog  mapstruct
*/
@Mapper
public interface WorkShuntLogMapping {

    WorkShuntLogMapping INSTANCE = Mappers.getMapper(WorkShuntLogMapping.class);
    /**
     * 请求日志记录 -> PageList
     */
    ReqPageListWorkShuntLogBO toReqPageListWorkShuntLogBOFromDto(ReqPageListWorkShuntLogDTO dto);

    ReqMobileGetWorkShuntBO toReqMobileGetWorkShuntBOFromDto(ReqGetWorkShuntDTO dto);

    ReqAddGroupWorkShuntBO toReqAddGroupWorkShuntBOFromDto(ReqAddGroupWorkShuntDTO dto);

    ReqGroupTransferShuntShuntBO toReqGroupTransferShuntShuntBOFromDto(ReqGroupTransferShuntShuntDTO dto);

    ReqMobileVerifyLogIdBo toReqMobileVerifyLogIdBoFromDto(ReqVerifyLogIdDTO dto);

    ReqAddGroupWorkShuntBO toReqAddGroupWorkShuntBOFromDubboDTO(GetSaveWorkShuntDubboDTO dto);

    ReqMobileVerifyLogIdBo toReqMobileVerifyLogIdBoFromDubboDto(GetWorkVerifyLogIdDubboDTO dto);

    ReqMobileGetWorkShuntBO toReqMobileGetWorkShuntBOFromDubbo(GetWorkShuntGroupDubboDTO dto);
}
