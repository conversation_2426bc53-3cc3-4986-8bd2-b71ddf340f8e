package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.app.mapping.WorkSkillMapping;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.client.dto.response.SkillGameRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillRepDTO;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkill.WorkSkillPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqPageListWorkSkillBO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqSaveWorkSkillBO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillTypeDomainService;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import io.vavr.Tuple;
import io.vavr.*;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;
import static io.vavr.API.*;

@Slf4j
@Service
public class WorkSkillAppService {

    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private UserSettingSkillDomainService workUserSettingSkillDomainService;

    @Resource
    private WorkShuntOrderVolumeRepository orderVolumeRepository;

    @Resource
    private WorkSkillTypeDomainService workSkillTypeDomainService;

    private final Supplier<BizException> exitsSupperThrowable = () -> new BizException(WORK_SKILL_EXIST.getErrCode(), WORK_SKILL_EXIST.getErrDesc());
    private final Function3<String, String, Throwable, BizException> skillOperationThrowable = (code, desc, e) -> {
        log.error("{}-{}", code, e.getMessage());
        return Match(e).of(Case($(Predicates.instanceOf(DuplicateKeyException.class)), new BizException(WORK_SKILL_NAME_DUPLICATE.getErrCode(), WORK_SKILL_NAME_DUPLICATE.getErrDesc())), Case($(Predicates.instanceOf(Exception.class)), new BizException(code, desc)));
    };

    public Page<WorkSkillPageListRespDTO> listPage(ReqPageListWorkSkillDTO dto) {
        ReqPageListWorkSkillBO bo = WorkSkillMapping.INSTANCE.toReqPageListWorkSkillBOFromDto(dto);
        Page<WorkSkillPageListRespDTO> page = workSkillDomainService.listPage(bo);
        List<WorkSkillPageListRespDTO> list = page.getRecords();
        if (CollUtil.isNotEmpty(list)) {
            initLevel(list);
            page.setRecords(groupBySkillTypeName(list));
        }
        return page;
    }

    private List<WorkSkillPageListRespDTO> groupBySkillTypeName(List<WorkSkillPageListRespDTO> list) {
        List<WorkSkillType> types = workSkillTypeDomainService.getSelectList();

        Map<String, List<WorkSkillPageListRespDTO>> groupByTypeName = list.stream()
            .collect(Collectors.groupingBy(WorkSkillPageListRespDTO::getSkillTypeName));

        for (Map.Entry<String, List<WorkSkillPageListRespDTO>> entry : groupByTypeName.entrySet()) {
            List<WorkSkillPageListRespDTO> sortedGroup = entry.getValue().stream()
                .sorted(Comparator.comparing(WorkSkillPageListRespDTO::getSkillSort))
                .collect(Collectors.toList());
            entry.setValue(sortedGroup);
        }

        List<WorkSkillPageListRespDTO> results = new ArrayList<>();
        types.forEach(t->{
            List<WorkSkillPageListRespDTO> skills = groupByTypeName.getOrDefault(t.getSkillTypeName(), Collections.emptyList());
            results.addAll(skills);
        });

        return results;
    }

    private void initLevel(List<WorkSkillPageListRespDTO> list) {
        list.forEach(skill -> {
            List<WorkUserSettingSkill> userSkills = workUserSettingSkillDomainService.queryBySkillId(skill.getSkillId());
            WorkSkill workSkill = workSkillDomainService.get(skill.getSkillId());
            Tuple4<Integer, Integer, Integer, Integer> tuple4 = calculateSkillLeveUserCount(userSkills, workSkill);
            skill.setEarlyCount(tuple4._1);
            skill.setMiddleCount(tuple4._2);
            skill.setHighCount(tuple4._3);
            skill.setExpertCount(tuple4._4);
            skill.setSkillNum(tuple4._1 + tuple4._2 + tuple4._3 + tuple4._4);
        });
    }

    /**
     * Calculates the count of users for each skill level based on the given user skills and skill.
     *
     * @param userSkills A list of {@link WorkUserSettingSkill} objects representing the user skills.
     * @param skill      A {@link WorkSkill} object representing the skill for which the user count needs to be calculated.
     * @return A {@link Tuple4} containing the count of users for each skill level: early, middle, high, and expert.
     *         The first element represents the early count, the second element represents the middle count,
     *         the third element represents the high count, and the fourth element represents the expert count.
     */
    private Tuple4<Integer, Integer, Integer, Integer> calculateSkillLeveUserCount(List<WorkUserSettingSkill> userSkills,
                                                                                   WorkSkill skill) {

        Tuple8<Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer> levelSkill = Tuple.of(skill.getEarlyStart(), skill.getEarlyEnd(), skill.getMiddleStart(), skill.getMiddleEnd(), skill.getHighStart(), skill.getHighEnd(), skill.getExpertStart(), skill.getExpertEnd());

        return Tuple.of(userSkills.stream()
            .filter(uss -> levelSkill._1 <= getMaxLimit(uss.getMaxLimit()) && getMaxLimit(uss.getMaxLimit())<= levelSkill._2)
            .toList()
            .size(), userSkills.stream()
            .filter(uss -> levelSkill._3 <= getMaxLimit(uss.getMaxLimit()) && getMaxLimit(uss.getMaxLimit()) <= levelSkill._4)
            .toList()
            .size(), userSkills.stream()
            .filter(uss -> levelSkill._5 <= getMaxLimit(uss.getMaxLimit()) && getMaxLimit(uss.getMaxLimit()) <= levelSkill._6)
            .toList()
            .size(), userSkills.stream()
            .filter(uss -> levelSkill._7 <= getMaxLimit(uss.getMaxLimit()) && getMaxLimit(uss.getMaxLimit()) <= levelSkill._8)
            .toList()
            .size());
    }

    private long getMaxLimit(Long maxLimit) {
        return Objects.isNull(maxLimit) ? 0L : maxLimit;
    }

    public Boolean save(SaveWorkSkillReqDTO dto) {
        ReqSaveWorkSkillBO bo = WorkSkillMapping.INSTANCE.toReqSaveWorkSkillBOFromDto(dto);
        WorkSkill workSkill = workSkillDomainService.queryBySKillCode(dto.getSkillCode());
        if(!Objects.isNull(workSkill)){
            throw new BizException(WORK_SKILL_NAME_DUPLICATE.getErrCode(),WORK_SKILL_NAME_DUPLICATE.getErrDesc());
        }

        if (workSkillDomainService.isExitsSkillStoreInSkillType(dto.getSkillTypeId(), dto.getSkillSort())) {
            throw new BizException(WORK_SKILL_SORT_IN_SKILL_TYPE_DUPLICATE.getErrCode(),WORK_SKILL_SORT_IN_SKILL_TYPE_DUPLICATE.getErrDesc());
        }

        return Try.of(() -> workSkillDomainService.save(bo))
            .getOrElseThrow(e -> skillOperationThrowable.apply(WORK_SKILL_SAVE.getErrCode(), WORK_SKILL_SAVE.getErrDesc(), e));
    }

    public Boolean edit(ReqEditWorkSkillDTO dto) {
        final WorkSkill workSkill = workSkillDomainService.get(dto.getSkillId());

        List<WorkSkill> workSkills = workSkillDomainService.queryBySkillCodes(List.of(dto.getSkillCode()));
        if(CollUtil.isNotEmpty(workSkills) && workSkills.size() > 1){
            throw new BizException(WORK_SKILL_NAME_DUPLICATE.getErrCode(),WORK_SKILL_NAME_DUPLICATE.getErrDesc());
        }

        if (workSkillDomainService.isExitsSkillStoreInSkillType(dto.getSkillId(), dto.getSkillTypeId(), dto.getSkillSort())) {
            throw new BizException(WORK_SKILL_SORT_IN_SKILL_TYPE_DUPLICATE.getErrCode(), WORK_SKILL_SORT_IN_SKILL_TYPE_DUPLICATE.getErrDesc());
        }

        return Option.when(workSkill != null, Try.of(() -> {
                    WorkSkill skill = WorkSkillMapping.INSTANCE.toReqEditWorkSkillBOFromDto(dto);
                    assert workSkill != null;
                    skill.setId(workSkill.getId());
                    skill.setUpdateTime(LocalDateTime.now());
                    skill.setDeleted(false);
                    return workSkillDomainService.edit(skill);
                })
                .getOrElseThrow(e -> skillOperationThrowable.apply(WORK_SKILL_EDIT.getErrCode(), WORK_SKILL_EDIT.getErrDesc(), e)))
            .getOrElseThrow(exitsSupperThrowable);

    }

    public Boolean delete(String skillId) {
        final WorkSkill workSkill = workSkillDomainService.get(skillId);

        return Option.when(workSkill != null, Try.of(() -> {
                Try.run(() -> {
                    boolean b = !orderVolumeRepository.queryBySKillCode(workSkill.getSkillCode())
                        .stream()
                        .filter(o -> o.getVolume() > 0)
                        .toList()
                        .isEmpty();
                    if (b)
                        throw new BizException(WORK_SKILL_SETTING_HAVE_VOLUME.getErrCode(), WORK_SKILL_SETTING_HAVE_VOLUME.getErrDesc());
                });

                return workSkillDomainService.delete(skillId);
            }).getOrElseThrow(e -> skillOperationThrowable.apply(WORK_SKILL_DEL.getErrCode(), e.getMessage(), e)))
            .getOrElseThrow(exitsSupperThrowable);
    }

    public WorkSkillDetailRespDTO getDetail(String skillId) {
        final WorkSkill workSkill = workSkillDomainService.get(skillId);
        return Option.when(workSkill != null, Try.of(() -> WorkSkillMapping.INSTANCE.toWorkSkillDetailRespDTO(workSkill))
                .getOrElseThrow(e -> skillOperationThrowable.apply(WORK_SKILL_NAME_DETAIL.getErrCode(), WORK_SKILL_NAME_DETAIL.getErrDesc(), e)))
            .getOrElseThrow(exitsSupperThrowable);
    }

    public List<SkillDropdownRepDTO> dropdown() {
        List<WorkSkill> all = Option.of(workSkillDomainService.list()).getOrElse(ArrayList::new);
        return all.stream()
            .filter(sk -> (sk.getDeleted() != null && !sk.getDeleted()))
            .map(skill -> SkillDropdownRepDTO.builder().name(skill.getSkillName()).id(skill.getSkillId()).code(skill.getSkillCode()).build())
            .toList();
    }

    public List<SkillRepDTO> selectAllSKills() {
        List<WorkSkill> all = Option.of(workSkillDomainService.list()).getOrElse(ArrayList::new);
        return Option.when(CollUtil.isNotEmpty(all),()->all.stream()
            .filter(sk -> (sk.getDeleted() != null && !sk.getDeleted()))
            .map(skill ->
                SkillRepDTO.builder().name(skill.getSkillName()).code(skill.getSkillCode()).relateGame(skill.getSkillRelationGame() == null ? false : skill.getSkillRelationGame()).build()
            ).toList()).getOrElse(ArrayList::new);
    }

    public Long getSkillCount() {
        return workSkillDomainService.getSkillCount();
    }

    /**
     * Queries all work skills and binds them with their respective game settings.
     *
     * @return A list of {@link WorkSkillBindGameReqDTO} objects, each representing a work skill
     *         and its associated game settings.
     */
    public List<WorkSkillBindGameReqDTO> queryAllWorkSkillBindGame() {
        List<UserSkillAllPO> alls = workSkillDomainService.all();
        return Option.when(CollUtil.isNotEmpty(alls), () -> {
            List<UserSkillAllPO> filterRelGameSkill = alls.stream().filter(f -> f.isSkillRelationGame()).toList();
            if (CollUtil.isEmpty(filterRelGameSkill)) {
                return new ArrayList<WorkSkillBindGameReqDTO>();
            }
            List<String> allSkillIds = filterRelGameSkill.stream().map(UserSkillAllPO::getSkillId).toList();
            List<WorkUserSettingSkill> allUserSettingSkill = workUserSettingSkillDomainService.queryEnableSkillSettingBySkillIds(allSkillIds);
            Map<String, List<WorkUserSettingSkill>> mapSettingSkill = transWorkUserSetting(allUserSettingSkill);
            Map<String, List<WorkUserSettingSkillGameList>> mapsGames = transWorkSettingGame(allUserSettingSkill.stream().map(WorkUserSettingSkill::getSettingSkillId).toList());
            long startTime = System.currentTimeMillis();
            log.info("开始过滤游戏....{}", startTime);
            List<WorkSkillBindGameReqDTO> results = filterSettingGame(filterRelGameSkill, mapSettingSkill, mapsGames);
            log.info("结束过滤游戏....{}， 数据大小{}", System.currentTimeMillis() - startTime, results.size());
            return filterSettingGame(filterRelGameSkill, mapSettingSkill, mapsGames);

        }).getOrElse(ArrayList::new);
    }

    public List<SkillRepDTO> queryWorkSkillBindGame(List<String> gameIds) {
        List<WorkUserSettingSkillGameList> workUserSettingSkillGameLists = workUserSettingSkillDomainService.queryWorkUserSettingSkillGameListByGameId(gameIds);
        if (CollUtil.isEmpty(workUserSettingSkillGameLists)) {
            return new ArrayList<>();
        }
        List<String> settingIds = workUserSettingSkillGameLists.stream()
            .filter(f-> Objects.nonNull(f.getSetttingSkillId()))
            .map(WorkUserSettingSkillGameList::getSetttingSkillId).toList();


        List<WorkUserSettingSkill> workUserSettingSkills = workUserSettingSkillDomainService.queryEnableSkillSettingBySettingIds(settingIds);
        if (CollUtil.isEmpty(workUserSettingSkills)) {
            return new ArrayList<>();
        }
        Map<String, List<WorkUserSettingSkill>> maps = workUserSettingSkills.stream()
            .filter(f-> Objects.nonNull(f.getWorkSkillId()))
            .collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkSkillId));

        List<WorkSkill> workSkills = workSkillDomainService.queryBySkillId(maps.keySet().stream().filter(f-> Objects.nonNull(f)).toList());

        if (CollUtil.isEmpty(workUserSettingSkills)) {
            return new ArrayList<>();
        }
        return workSkills.stream().filter(f-> f.getSkillRelationGame().equals(Boolean.TRUE)).map(m-> {
            SkillRepDTO skillRepDTO = new SkillRepDTO();
            skillRepDTO.setRelateGame(true);
            skillRepDTO.setCode(m.getSkillCode());
            skillRepDTO.setName(m.getSkillName());
            return skillRepDTO;
        }).toList();
    }

    /**
     * Queries the game settings associated with a specific work skill based on the given skill code.
     *
     * @param code The skill code for which to retrieve the game settings.
     * @return A set of {@link SkillGameRepDTO} objects representing the game settings associated with the work skill.
     *         If no game settings are found for the given skill code, an empty set is returned.
     */
    public Set<SkillGameRepDTO> queryWorkSkillBindGameBySkillCode(String code) {
        WorkSkill workSKill = workSkillDomainService.queryBySKillCode(code);
        return Option.when(!Objects.isNull(workSKill), ()->{
            List<WorkUserSettingSkill> userSettingSkills = workUserSettingSkillDomainService.queryEnableSkillSettingBySkillIds(Collections.singletonList(workSKill.getSkillId()));
            Map<String, List<WorkUserSettingSkill>> mapSettingSkill = transWorkUserSetting(userSettingSkills);
            Map<String, List<WorkUserSettingSkillGameList>> mapsGames = transWorkSettingGame(userSettingSkills.stream().map(WorkUserSettingSkill::getSettingSkillId).toList());
            Set<SkillGameRepDTO> dtos = new HashSet<>();
            List<WorkUserSettingSkill> wuss = mapSettingSkill.get(workSKill.getSkillId());

            Optional.ofNullable(wuss).ifPresent(wus -> {
                wus.forEach(wu -> {
                    List<WorkUserSettingSkillGameList> wussGameList = mapsGames.get(wu.getSettingSkillId());
                    if (CollUtil.isNotEmpty(wussGameList)) {
                        dtos.addAll(wussGameList.stream()
                            .map(ugl -> SkillGameRepDTO.builder().id(ugl.getGameId()).name(ugl.getGameName()).build())
                            .collect(Collectors.toSet()));
                    }
                });
            });

            return dtos;
        }).getOrElse(HashSet::new);
    }


    /**
     * Filters and binds work skills with their respective game settings.
     *
     * @param alls            A list of {@link UserSkillAllPO} objects representing all user skills.
     * @param mapSettingSkill A map where the key is the skill ID and the value is a list of {@link WorkUserSettingSkill} objects
     *                        representing the user settings for each skill.
     * @param mapsGames       A map where the key is the setting skill ID and the value is a list of {@link WorkUserSettingSkillGameList} objects
     *                        representing the game settings for each user setting skill.
     * @return A list of {@link WorkSkillBindGameReqDTO} objects, each representing a work skill and its associated game settings.
     */
    private static List<WorkSkillBindGameReqDTO> filterSettingGame(List<UserSkillAllPO> alls,
                                                                   Map<String, List<WorkUserSettingSkill>> mapSettingSkill,
                                                                   Map<String, List<WorkUserSettingSkillGameList>> mapsGames) {
        return alls.stream().map(skill -> {
            List<WorkUserSettingSkill> filterSettingSkills = mapSettingSkill.get(skill.getSkillId());
            WorkSkillBindGameReqDTO wbg = WorkSkillBindGameReqDTO.builder()
                .skillId(skill.getSkillId())
                .code(skill.getSkillCode())
                .skillName(skill.getSkillName())
                .build();

            if (CollUtil.isNotEmpty(filterSettingSkills)) {
                Set gameDTOS = new HashSet();
                filterSettingSkills.forEach(fsk -> {
                    List<WorkUserSettingSkillGameList> filterGames = mapsGames.get(fsk.getSettingSkillId());
                    if (CollUtil.isNotEmpty(filterGames)) {
                        gameDTOS.addAll(filterGames.stream().map(fg -> {
                            GameDTO dto = new GameDTO();
                            dto.setGameId(fg.getGameId());
                            dto.setGameName(fg.getGameName());
                            return dto;
                        }).toList());
                        wbg.setRelateGame(true);
                    }
                });
                wbg.setGameDTOS(gameDTOS);
            }
            return wbg;
        }).toList();
    }

    /**
     * Transforms a list of {@link WorkUserSettingSkill} objects into a map where the key is the skill ID and the value is a list of {@link WorkUserSettingSkill} objects.
     *
     * @param allUserSettingSkill A list of {@link WorkUserSettingSkill} objects representing all user setting skills.
     * @return A map where the key is the skill ID and the value is a list of {@link WorkUserSettingSkill} objects. If the input list is empty or null, an empty map is returned.
     */
    private Map<String, List<WorkUserSettingSkill>> transWorkUserSetting(List<WorkUserSettingSkill> allUserSettingSkill) {
        return Option.when(CollUtil.isNotEmpty(allUserSettingSkill), () -> allUserSettingSkill
            .stream()
            .collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkSkillId))).getOrElse(HashMap::new);
    }

    /**
     * Transforms a list of {@link WorkUserSettingSkillGameList} objects into a map where the key is the setting skill ID and the value is a list of {@link WorkUserSettingSkillGameList} objects.
     *
     * @param settingSkillIds A list of setting skill IDs for which to retrieve game settings.
     * @return A map where the key is the setting skill ID and the value is a list of {@link WorkUserSettingSkillGameList} objects.
     *         If the input list is empty or null, an empty map is returned.
     */
    private Map<String, List<WorkUserSettingSkillGameList>> transWorkSettingGame(List<String> settingSkillIds) {
        return Option.when(CollUtil.isNotEmpty(settingSkillIds), () -> workUserSettingSkillDomainService.queryGameListBySettingSkillId(settingSkillIds)
            .stream()
            .collect(Collectors.groupingBy(WorkUserSettingSkillGameList::getSetttingSkillId))
        ).getOrElse(HashMap::new);
    }




}
