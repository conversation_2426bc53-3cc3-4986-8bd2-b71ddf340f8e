package com.pxb7.mall.workshunt.app.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class WorkSkillDetailRespDTO implements Serializable {

    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 技能名称
     */
    private String skillName;
    /**
     * 技能code
     */
    private String skillCode;
    /**
     * 技能类型ID
     */
    private String skillTypeId;
    /**
     * 是否关联游戏1：是0否
     */
    private Boolean skillRelationGame;
    /**
     * 排序值
     */
    private Integer skillSort;
    /**
     * 初开始数据
     */
    private Integer earlyStart;
    /**
     * 初结束数据
     */
    private Integer earlyEnd;
    /**
     * 中开始数据
     */
    private Integer middleStart;
    /**
     * 中结束数据
     */
    private Integer middleEnd;
    /**
     * 高开始数据
     */
    private Integer highStart;
    /**
     * 高结束数据
     */
    private Integer highEnd;
    /**
     * 专家开始数据
     */
    private Integer expertStart;
    /**
     * 专家结束数据
     */
    private Integer expertEnd;
}