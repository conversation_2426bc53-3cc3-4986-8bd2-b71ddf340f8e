package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.client.work.databoard.ReqListDataBoardDeptWorkUserDTO;
import com.pxb7.mall.workshunt.domain.model.dataBoard.ReqListDataBoardWorkUserBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 排班用户 -> WorkUser  mapstruct
*/
@Mapper
public interface WorkUserDataBoardMapping {

    WorkUserDataBoardMapping INSTANCE = Mappers.getMapper(WorkUserDataBoardMapping.class);

    ReqListDataBoardWorkUserBO toReqListWorkUserBOToFromDto(ReqListDataBoardDeptWorkUserDTO dto);
}
