package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.ListSysDeptDubboRespDTO;
import com.pxb7.mall.workshunt.app.mapping.WorkUserMapping;
import com.pxb7.mall.workshunt.app.model.ReqListDeptWorkUserDTO;
import com.pxb7.mall.workshunt.app.model.ReqWorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.QueryWorkUserRepDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ReqWorkShuntGroupDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.domain.ManualDataTransactionService;
import com.pxb7.mall.workshunt.domain.model.WorkUserPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workUser.ReqListWorkUserBO;
import com.pxb7.mall.workshunt.domain.model.workUser.WorkSkillCodeGroupShuntBO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.WORK_SKILL_ASSIGNED_SEARCH_PARAM;


@Slf4j
@Service
public class WorkUserAppService {

    @Autowired
    private UserCenterGateway userCentralDubboServiceGateway;

    @Autowired
    private WorkUserDomainService workUserDomainService;

    @Autowired
    private WorkSkillDomainService workSkillDomainService;

    @Autowired
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private ManualDataTransactionService manualDataTransactionService;

    public List<WorkUserPageListRespDTO> listWorkUser(ReqListDeptWorkUserDTO dto) {
        if (CharSequenceUtil.isBlank(dto.getDeptId()) && CollUtil.isEmpty(dto.getUserIds())) {
            throw new BizException(WORK_SKILL_ASSIGNED_SEARCH_PARAM.getErrCode(), WORK_SKILL_ASSIGNED_SEARCH_PARAM.getErrDesc());
        }
        ReqListWorkUserBO bo = WorkUserMapping.INSTANCE.toReqListWorkUserBOToFromDto(dto);
        ReqWorkUserSelectDeptUserRespDTO listDto = WorkUserMapping.INSTANCE.toReqWorkUserSelectDeptUserRespDTO(dto);
        List<WorkUserSelectDeptUserRespDTO> rpcUserUser = this.deptListUser(listDto);
        return workUserDomainService.listUserWorkUser(bo, rpcUserUser);
    }

    public List<WorkUserSelectDeptUserRespDTO> deptListUser(ReqWorkUserSelectDeptUserRespDTO dto) {
        Set<String> userIds = new HashSet<>(Option.of(dto.getUserIds()).getOrElse(ArrayList::new));
        GetUserInfoReqDTO userInfoReqDTO = GetUserInfoReqDTO.builder().deptId(dto.getDeptId()).userIds(userIds).build();
        List<GetUserRoleInfoRespDTO> dataRespDTO = userCentralDubboServiceGateway.listUserInfo(() -> userInfoReqDTO);
        return Option.when(CollUtil.isNotEmpty(dataRespDTO), () -> {
            return dataRespDTO.stream().map(data -> {
                WorkUserSelectDeptUserRespDTO respDTO = new WorkUserSelectDeptUserRespDTO();
                respDTO.setDeptId(data.getDeptId());
                respDTO.setDeptName(getDeptName(data.getDeptId()));
                respDTO.setUserId(data.getUserId());
                respDTO.setUserName(data.getUserName());
                respDTO.setMerchantId(data.getMerchantId());
                respDTO.setDeptType(String.valueOf(data.getCompanyId()));
                return respDTO;
            }).toList();
        }).getOrElse(ArrayList::new);

    }

    private String getDeptName(String deptId) {
        List<ListSysDeptDubboRespDTO> deptDubboRespDTOS = userCentralDubboServiceGateway.listSysDept();
        return Option.when(CollUtil.isNotEmpty(deptDubboRespDTOS), () -> {
            ListSysDeptDubboRespDTO deptDubboRespDTO = deptDubboRespDTOS.stream()
                    .filter(d -> d.getDeptId().equals(deptId)).toList().stream().findFirst().orElseGet(ListSysDeptDubboRespDTO::new);
            if (!Objects.isNull(deptDubboRespDTO.getCompanyName())) {
                return deptDubboRespDTO.getCompanyName() + "-" + deptDubboRespDTO.getFullName();
            }
            return deptDubboRespDTO.getFullName();
        }).getOrElse("");

    }

    /**
     * Retrieves a list of work users based on skill code and game ID.
     *
     * @param reqWorkShuntGroupDTO The request object containing skill code and game ID.
     * @return A list of {@link WorkUserDeptUserRespDTO} representing the work users.
     *
     * This function takes a {@link ReqWorkShuntGroupDTO} object as input, which contains the skill code and game ID. It
     * then maps the input to a {@link WorkSkillCodeGroupShuntBO} object using the {@link WorkUserMapping} class.
     * Finally, it calls the {@link WorkUserDomainService#getSkillWorkUserByCodeAndGameId(WorkSkillCodeGroupShuntBO)}
     * method to retrieve the list of work users based on the provided skill code and game ID. The function returns a
     * list of {@link WorkUserDeptUserRespDTO} objects representing the work users.
     */
    public List<WorkUserDeptUserRespDTO> getSkillWorkUserByCodeAndGameId(ReqWorkShuntGroupDTO reqWorkShuntGroupDTO) {
        WorkSkillCodeGroupShuntBO bo = WorkUserMapping.INSTANCE.toReqWorkShuntGroupDTO(reqWorkShuntGroupDTO);
        return workUserDomainService.getSkillWorkUserByCodeAndGameId(bo);
    }


    public List<QueryWorkUserRepDTO> queryMerchantWorkUser(QueryWorkUserReqDTO dto){
        String skillCode = dto.getSkillCode();
        String merchantId = dto.getMerchantId();

        if(CharSequenceUtil.isBlank(merchantId)) throw new IllegalArgumentException("merchant Id");

        return Option.when(CharSequenceUtil.isNotBlank(dto.getSkillCode()), ()->{
            WorkSkill skills = workSkillDomainService.queryBySKillCode(skillCode);
            List<WorkUserSettingSkill> usks  = userSettingSkillDomainService.queryBySkillId(skills.getSkillId());
            List<String> userIdsBindSkills = usks.stream().map(WorkUserSettingSkill::getWorkUserId).toList();
            List<WorkUser> users = workUserDomainService.query(userIdsBindSkills, merchantId);
            return translateWorkUser(users);
        }).getOrElse(()->
            translateWorkUser(workUserDomainService.queryByMerchantId(merchantId))
        );
    }

    private List<QueryWorkUserRepDTO> translateWorkUser(List<WorkUser> users){
        return Option.when(CollUtil.isNotEmpty(users), ()->
            users.stream().map(wu-> QueryWorkUserRepDTO.builder().userId(wu.getUserId()).merchantId(wu.getMerchantId()).build()).toList()
        ).getOrElse(ArrayList::new);
    }


    public Boolean clearWorkUserSkills(String workUserId){
        List<WorkUserSettingSkill> settingSkills = userSettingSkillDomainService.queryAllByWorkUserId(workUserId);
        return Option.when(CollUtil.isNotEmpty(settingSkills), ()->
            manualDataTransactionService.manual(Try.of(()-> {
                userSettingSkillDomainService.removeBatch(settingSkills);
                userSettingSkillDomainService.removeRelationGameBySkillIds(settingSkills.stream().map(WorkUserSettingSkill::getWorkSkillId).toList());
                return true;
            })).getOrElse(false)
        ).getOrElse(false);
    }

    public Boolean relateTemplate(String workUserId, String templateId) {
        return workUserDomainService.relateTemplate(workUserId, templateId);
    }
}
