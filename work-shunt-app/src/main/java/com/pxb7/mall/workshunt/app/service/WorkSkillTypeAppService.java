package com.pxb7.mall.workshunt.app.service;

import com.pxb7.mall.workshunt.app.mapping.WorkSkillTypeMapping;
import com.pxb7.mall.workshunt.app.model.WorkSkillTypeSelectListRespDTO;
import com.pxb7.mall.workshunt.domain.service.WorkSkillTypeDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WorkSkillTypeAppService {

    @Resource
    private WorkSkillTypeDomainService workSkillTypeDomainService;

    public List<WorkSkillTypeSelectListRespDTO> selectList() {
        return WorkSkillTypeMapping.INSTANCE.toWorkSkillTypeSelectListRespDTO(workSkillTypeDomainService.getSelectList());
    }

}
