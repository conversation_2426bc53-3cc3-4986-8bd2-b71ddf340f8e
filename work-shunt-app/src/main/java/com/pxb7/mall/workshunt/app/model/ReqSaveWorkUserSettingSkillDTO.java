package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReqSaveWorkUserSettingSkillDTO implements Serializable {


    /**
     * 设置技能 - 传入这个字段
     */
    private String workUserId;

    /**
     * 部门类型 1公司部门 2号商部门
     */
    @NotNull(message = "部门类型不能为空")
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
    /**
     * 部门ID
     */
    @NotNull(message = "部门不能为空")
    private String deptId ;
    /**
     * 用户ID
     */
    @NotNull(message = "用户不能为空")
    private String userId ;

    /**
     * 模版ID
     */
    private String templateId;

    /**
     * 设置工作技能
     */
    @NotNull(message = "技能不能为空")
    List<ReqWorkSkillDTO> skills;
}