package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workshunt.app.model.WorkSkillLevelDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserByNoOverFlowReqDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.domain.service.ShuntRuleConditionDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.infra.config.WorkShuntBlacklistConfig;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.domain.Constants.*;
import static com.pxb7.mall.workshunt.domain.Constants.IM_ONLINE;
import static com.pxb7.mall.workshunt.domain.Constants.NO_OVER_MAX_LIMIT;
import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.WORK_SKILL_NO_EXITS;

@Service
@Slf4j
public class ShuntSearchWorkUserAppService {

    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private ShuntSupportService shuntSupportService;

    @Resource
    private ShuntAppService shuntAppService;

    @Resource
    private WorkShuntBlacklistConfig workShuntBlacklistConfig;

    @Resource
    private ShuntRuleConditionDomainService shuntRuleConditionDomainService;



    public List<GetWorkShuntRespDTO> queryWorkUsersByOnOverflowCondition(QueryWorkUserByNoOverFlowReqDTO dto) {

        LocalDateTime requestTime = LocalDateTime.now();
        //转换技能
        List<WorkSkill> originSkills = workSkillDomainService.queryBySkillCodes(List.of(dto.getSkillCode()));
        List<WorkSkillLevelDTO> skills = Option.of(shuntSupportService.transformByWorkSkill(originSkills))
            .getOrElseThrow(() -> new BizException(WORK_SKILL_NO_EXITS.getErrCode(), WORK_SKILL_NO_EXITS.getErrDesc()));

        List<WorkUserSettingSkill> workUserSettingSkills = shuntSupportService.getUserSettingSkill(skills);

        List<WorkUserSettingSkill> filterSettingRelateGame = shuntSupportService.filterUserSettingRelateGame(workUserSettingSkills, skills, dto.getGameId());
        List<WorkUser> workUsers = shuntSupportService.getWorkUser(filterSettingRelateGame, dto.getMerchantId());

        //filter work user by blacklist
        String filterUsers = workShuntBlacklistConfig.getFilterUsers();
        boolean isNeedFilerCon = CharSequenceUtil.isNotBlank(filterUsers) && Objects.isNull(dto.getGameId());
        List<WorkUser> filterWorkUsers = Option.when(isNeedFilerCon, () -> {
            Set<String> filterSets = Arrays.stream(filterUsers.split(",")).collect(Collectors.toSet());
            return shuntAppService.filterSpecifiedWorkUsers(workUsers, filterSets);
        }).getOrElse(() -> workUsers);

        List<WorkUser> finalWorkUsers = queryNoOverflowCondition(skills, filterWorkUsers, dto.getPersonCount());
        shuntAppService.asynSaveLog(ReqGetWorkShuntDTO.builder()
            .personCount(dto.getPersonCount())
            .gameId(dto.getGameId())
            .domain(dto.getDomain())
            .build(), originSkills, finalWorkUsers, requestTime);

        return Option.when(CollUtil.isNotEmpty(finalWorkUsers), () -> shuntSupportService.shuntWorkUserResponse(finalWorkUsers, originSkills))
            .getOrElse(ArrayList::new);
    }



    public List<WorkUser> queryNoOverflowCondition(List<WorkSkillLevelDTO> skills,
                                                   List<WorkUser> workUsers,
                                                   int personCount) {

        List<String> skillIds = skills.stream().map(WorkSkillLevelDTO::getSkillId).collect(Collectors.toList());
        List<WorkShuntRuleSettingCondition> conditions = shuntRuleConditionDomainService.queryBy(skillIds, NO_OVERFLOW);

        List<WorkUser> filterWorkUsers  =  workUsers.stream().filter(u -> filterRuleCondition(conditions, u)).collect(Collectors.toList());

        int requestCount = personCount == NO_LIMIT ? Integer.MAX_VALUE : personCount;
        return Option.when(filterWorkUsers.size() >= requestCount || personCount == NO_LIMIT,
                () -> shuntSupportService.assignWorkUserByShuntRuleType(filterWorkUsers, skills, NO_OVERFLOW, requestCount)
        ).getOrElse(() -> filterWorkUsers);
    }


    private boolean filterRuleCondition(List<WorkShuntRuleSettingCondition> conditions, WorkUser workUser) {
        List<Boolean> result = Option.when(CollUtil.isNotEmpty(conditions), () -> conditions.stream()
                .map(c ->
                        switch (c.getConditionType()) {
                            case IM_ONLINE -> shuntSupportService.checkOnline(workUser);
                            default -> true;
                        }
                ).toList()).getOrElse(() -> List.of(false, false, false));
        return result.stream().allMatch(r -> r);
    }


}

