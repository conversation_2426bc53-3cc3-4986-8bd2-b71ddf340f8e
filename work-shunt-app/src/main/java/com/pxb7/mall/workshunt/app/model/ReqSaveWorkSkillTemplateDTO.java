package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReqSaveWorkSkillTemplateDTO implements Serializable {
    /**
     * 模版名称
     */
    @NotNull(message = "名称不能为空")
    private String templateTitle;
    /**
     * 设置工作技能
     */
    List<ReqWorkSkillDTO> skills;

    /**
     * 是否覆盖接待上限
     */
    private Boolean isOverrideMaxLimit;
}