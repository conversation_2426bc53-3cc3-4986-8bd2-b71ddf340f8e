package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workshunt.app.mapping.WorkUserMapping;
import com.pxb7.mall.workshunt.app.mapping.WorkUserSettingSkillMapping;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingSkillDTO;
import com.pxb7.mall.workshunt.app.model.ReqWorkSkillDTO;
import com.pxb7.mall.workshunt.client.dto.request.GetSkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryOnlineWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.WorkUserOnlineRespDTO;
import com.pxb7.mall.workshunt.client.work.response.GetSkillGameRespDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.domain.ManualDataTransactionService;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqSaveWorkUserSettingSkillBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.WorkUserBO;
import com.pxb7.mall.workshunt.domain.service.*;
import com.pxb7.mall.workshunt.infra.enums.ShuntAssignAlgorithmEnum;
import com.pxb7.mall.workshunt.infra.model.skill.GamePO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserSettingSkillGameListRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.ImGateway;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;
import static com.pxb7.mall.workshunt.infra.enums.ShuntRuleTypeEnum.NO_OVERFLOW;


@Slf4j
@Service
public class UserSettingSkillAppService {

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private WorkUserDomainService workUserDomainService;

    @Resource
    private WorkUserSettingSkillGameListRepository workUserSettingSkillGameListRepository;

    @Resource
    private ShuntRuleSettingDomainService shuntRuleSettingDomainService;

    @Resource
    private UserCenterGateway userCenterGateway;

    @Resource
    private ImGateway imGateway;

    @Resource
    private WorkSkillTemplateDomainService workSkillTemplateDomainService;

    private static final String MERCHANT_DEPT_TYPE = "3";

    private static final int MAX = 20;


    public Map<String, List<UserSkillAllPO>> queryUserSkillsGroupBySkillType(String userId) {

        List<UserSkillAllPO> allSkills = Option.of(workSkillDomainService.all()).getOrElse(ArrayList::new);
        WorkUser workUser = workUserDomainService.queryAllStatusByUserId(userId);
        List<WorkUserSettingSkill> userSkills = Option.when(Objects.nonNull(workUser), () -> userSettingSkillDomainService.queryAllByWorkUserId(workUser.getWorkUserId())).getOrElse(ArrayList::new);

        initAllSkillByUserSkillSetting(allSkills, userSkills);
        setSkillRule(allSkills);

        Map<String, List<UserSkillAllPO>> results = allSkills.stream().collect(Collectors.groupingBy(UserSkillAllPO::getSkillTypeName));
        results.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry-> entry.getValue().stream().sorted((c1, c2)->c1.getSort() - c2.getSort()).toList()));

        return results;
    }


    private void initAllSkillByUserSkillSetting(List<UserSkillAllPO> allSkills, List<WorkUserSettingSkill> userSkills) {
        Option.when(CollUtil.isNotEmpty(userSkills) && CollUtil.isNotEmpty(allSkills), Try.of(() -> {
            Map<String, List<UserSkillAllPO>> skillAllPOMap = allSkills.stream().collect(Collectors.groupingBy(UserSkillAllPO::getSkillId));
            userSkills.forEach(user -> {
                if (skillAllPOMap.containsKey(user.getWorkSkillId())) {
                    UserSkillAllPO po = skillAllPOMap.get(user.getWorkSkillId()).get(0);
                    po.setAble(user.isAble());
                    po.setMaxLimit(user.getMaxLimit());
                    po.setMaxSessionLimit(user.getMaxSessionLimit());
                    if (po.isSkillRelationGame()) {
                        List<WorkUserSettingSkillGameList> gameLists = userSettingSkillDomainService.queryGameListBySettingSkillId(user.getSettingSkillId());
                        List<GamePO> games = gameLists.stream().map(game -> {
                            GamePO gameDTO = new GamePO();
                            gameDTO.setGameName(game.getGameName());
                            gameDTO.setGameId(game.getGameId());
                            return gameDTO;
                        }).toList();
                        po.setGames(CollUtil.isNotEmpty(games) ? games : null);
                        po.setRelationGameCount(games.size());
                    }
                }
            });
            return true;
        }));
    }


    /**
     * 根据技能列表设置对应的分流规则标识
     *
     * @param allSkills 用户所有技能列表
     *                  如果技能列表为空，直接返回
     *                  根据技能ID查询对应的分流规则设置
     *                  如果分流规则为空，直接返回
     *                  将分流规则按技能ID分组
     *                  遍历所有技能，设置是否存在最大限制规则和最大会话限制规则
     */
    public void setSkillRule(List<UserSkillAllPO> allSkills) {
        if(CollUtil.isEmpty(allSkills)){
            return;
        }
        List<String> skillIds = allSkills.stream().map(UserSkillAllPO::getSkillId).toList();
        List<WorkShuntRuleSetting> shuntRuleSettings = shuntRuleSettingDomainService.queryBySkillIds(skillIds);

        if(CollUtil.isEmpty(shuntRuleSettings)){
            return;
        }

        Map<String, List<WorkShuntRuleSetting>> stringListMap = shuntRuleSettings.stream().collect(Collectors.groupingBy(WorkShuntRuleSetting::getSkillId));
        allSkills.forEach(skill-> {
            List<WorkShuntRuleSetting> settings = stringListMap.get(skill.getSkillId());
            if (CollUtil.isNotEmpty(settings)) {
                for (WorkShuntRuleSetting setting : settings) {
                    if (setting.getShuntType().equals(ShuntAssignAlgorithmEnum.ROOM_AVERAGE.getShuntAlgorithmType())) {
                       skill.setHasMaxLimitRule(true);
                    } else if (setting.getShuntType().equals(ShuntAssignAlgorithmEnum.SESSION_AVERAGE.getShuntAlgorithmType()))  {
                        skill.setHasMaxSessionLimitRule(true);
                    }
                }
            }
        });

    }


    public List<WorkUserSettingSkillPageListRespDTO> getWorkUserSettingSkillByPkUserId(String pkUserId) {
        return userSettingSkillDomainService.getUserSettingSkillByPkUserId(pkUserId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void save(ReqSaveWorkUserSettingSkillDTO dto) {

        checkMerchantParam(dto);
        checkParamSkillSetting(dto.getSkills());

        WorkUser pkWorkUser = workUserDomainService.queryAllStatusByUserId(dto.getUserId());
        checkWorkUserRelateTemplate(pkWorkUser);

        ReqSaveWorkUserSettingSkillBO bo = WorkUserSettingSkillMapping.INSTANCE.toReqSaveWorkUserSettingSkillBOFromDto(dto);
        WorkUserBO workUserBO = WorkUserMapping.INSTANCE.workUserBoTransformUserSettingSkillDTO(dto);

        //设置员工是否激活
        boolean isActive = userCenterGateway.isCustomer(dto.getUserId());
        workUserBO.setIsActive(isActive);

        Integer imStatus  = imGateway.getImStatus(dto.getUserId());
        workUserBO.setImStatus(imStatus);

        //保存OR更新 work user
        WorkUser workUser = Option.when(Objects.isNull(pkWorkUser), ()-> workUserDomainService.save(workUserBO)).getOrElse(() -> workUserDomainService.update(workUserBO));
        bo.setWorkUserId(Objects.isNull(pkWorkUser) ? workUser.getWorkUserId() : pkWorkUser.getWorkUserId());

        userSettingSkillDomainService.saveBatch(bo);
    }


    public void checkWorkUserRelateTemplate(WorkUser pkWorkUser) {
        if(Objects.isNull(pkWorkUser) ||
                Objects.isNull(pkWorkUser.getTemplateId())){
            return;
        }
        WorkSkillTemplate workSkillTemplate = workSkillTemplateDomainService.getTableOneDataByParamKey(pkWorkUser.getTemplateId());
        if(workSkillTemplate == null){
            return;
        }
        Assert.isFalse(workSkillTemplate.getOverrideMaxLimit(), "当前技能已被技能模版覆盖，无法修改");
    }



    private  void checkMerchantParam(ReqSaveWorkUserSettingSkillDTO dto) {
        if (dto.getDeptType().equals(MERCHANT_DEPT_TYPE)) {
            if (CharSequenceUtil.isBlank(dto.getMerchantId())) {
                throw new BizException(WORK_SKILL_SETTING_MERCHANT_USER_ERROR.getErrCode(), WORK_SKILL_SETTING_MERCHANT_USER_ERROR.getErrDesc());
            }
        }
    }


    public void checkParamSkillSetting(List<ReqWorkSkillDTO> list){

        if(CollUtil.isEmpty(list)){
            throw new BizException(WORK_SKILL_SETTING_USER_NO_DATA.getErrCode(),WORK_SKILL_SETTING_USER_NO_DATA.getErrDesc());
        }

        List<String> skillIds = list.stream().filter(ReqWorkSkillDTO::isAble).map(ReqWorkSkillDTO::getSkillId).toList();
        if(skillIds.size() > MAX){
            throw new BizException(WORK_SKILL_SETTING_SIZE_LIMIT.getErrCode(), WORK_SKILL_SETTING_SIZE_LIMIT.getErrDesc());
        }

        Map<String, List<WorkShuntRuleSetting>> maps = shuntRuleSettingDomainService.translateListToMap(shuntRuleSettingDomainService.queryBySkillIds(skillIds));
        list.stream().filter(ReqWorkSkillDTO::isAble).forEach(ll -> {
            checkRelateParam(ll);
            checkMaxLimit(maps, ll);
        });

    }


    private void checkRelateParam(ReqWorkSkillDTO dto){
        if (dto.isSkillRelationGame() && CollUtil.isEmpty(dto.getGames())) {
            throw new BizException(WORK_SKILL_SETTING_RELATION_NO_GAME.getErrCode(), WORK_SKILL_SETTING_RELATION_NO_GAME.getErrDesc());
        }
    }


    private void checkMaxLimit(Map<String, List<WorkShuntRuleSetting>> maps, ReqWorkSkillDTO dto) {
        List<WorkShuntRuleSetting> workShuntRuleSettings = maps.getOrDefault(dto.getSkillId(), List.of());
        if(CollUtil.isEmpty(workShuntRuleSettings)){
            return;
        }
        for (WorkShuntRuleSetting workShuntRuleSetting : workShuntRuleSettings) {
            if (workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.ROOM_AVERAGE.getShuntAlgorithmType()) && ( dto.getMaxLimit() == null || dto.getMaxLimit() <= 0)) {
                throw new BizException(WORK_SKILL_SETTING_NO_LIMIT_MAX.getErrCode(), WORK_SKILL_SETTING_NO_LIMIT_MAX.getErrDesc());
            }
            if (workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.SESSION_AVERAGE.getShuntAlgorithmType()) && (dto.getMaxSessionLimit() == null || dto.getMaxSessionLimit() <= 0)) {
                throw new BizException(WORK_SKILL_SETTING_NO_SESSION_LIMIT_MAX.getErrCode(), WORK_SKILL_SETTING_NO_SESSION_LIMIT_MAX.getErrDesc());
            }
        }
    }



    public List<WorkUserSelectDeptUserRespDTO> getSkillWorkUserByCode(String skillCode) {
        return userSettingSkillDomainService.getSkillWorkUserByCode(skillCode);
    }



    public List<WorkUserOnlineRespDTO> getOnlineWorkUser(QueryOnlineWorkUserReqDTO dto) {
        String skillCode = dto.getSkillCode();
        List<WorkUser> onlineUsers = userSettingSkillDomainService.getOnlineWorkUsersBySkillCode(skillCode, dto.getGameId());
        return onlineUsers.stream().map(t-> WorkUserOnlineRespDTO.builder().isOnline(t.getIsOnline()).userId(t.getUserId()).build()).collect(Collectors.toList());
    }


    public List<GetSkillGameRespDTO> getSkillRelatedGames(GetSkillGameReqDTO dto){
        WorkSkill workSkill = workSkillDomainService.queryBySKillCode(dto.getCode());
        return Option.when(!Objects.isNull(workSkill),()->{
           WorkUser workUser = workUserDomainService.queryByUserId(dto.getUserId());
            return Option.when(!Objects.isNull(workUser), () -> {
                WorkUserSettingSkill wus = userSettingSkillDomainService.queryByWorkUserIdAndSkillId(workUser.getWorkUserId(), workSkill.getSkillId());
                if (Objects.isNull(wus)) {
                    return new ArrayList<GetSkillGameRespDTO>();
                }
                List<WorkUserSettingSkillGameList> wusGames = workUserSettingSkillGameListRepository.queryBySettingSkillId(wus.getSettingSkillId());
                if (CollUtil.isNotEmpty(wusGames)) {
                    return wusGames.stream().map(g -> {
                        GetSkillGameRespDTO gameReqDTO = new GetSkillGameRespDTO();
                        gameReqDTO.setGameId(g.getGameId());
                        gameReqDTO.setName(g.getGameName());
                        return gameReqDTO;
                    }).toList();
                }
                return new ArrayList<GetSkillGameRespDTO>();
            }).getOrElse(ArrayList::new);
        }).getOrElse(ArrayList::new);
    }
}
