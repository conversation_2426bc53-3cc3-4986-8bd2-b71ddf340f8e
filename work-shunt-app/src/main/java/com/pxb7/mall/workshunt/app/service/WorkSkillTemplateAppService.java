package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workshunt.app.mapping.WorkSkillTemplateMapping;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkUserSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.domain.ManualDataTransactionService;
import com.pxb7.mall.workshunt.domain.model.workSkillTemplate.ReqSaveWorkSkillTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqSaveWorkUserSettingSkillBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqWorkSkillBO;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillTemplateDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.model.skill.GamePO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.UserSettingSkillRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillTemplateRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;


@Slf4j
@Service
public class WorkSkillTemplateAppService {

    @Resource
    private WorkSkillTemplateDomainService workSkillTemplateDomainService;

    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private WorkSkillTemplateRepository workSkillTemplateRepository;


    @Resource
    private UserSettingSkillAppService userSettingSkillAppService;

    @Resource
    private WorkUserDomainService workUserDomainService;

    @Resource
    private ManualDataTransactionService manualDataTransactionService;

    @Resource
    private UserSettingSkillRepository userSettingSkillRepository;


    public List<WorkSkillTemplateSelectListRespDTO> dropDownList() {
        return workSkillTemplateDomainService.dropDownList();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveWorkSkillTemplate(ReqSaveWorkSkillTemplateDTO dto) {

        userSettingSkillAppService.checkParamSkillSetting(dto.getSkills());

        ReqSaveWorkSkillTemplateBO bo = WorkSkillTemplateMapping.INSTANCE.toReqSaveWorkSkillTemplateBOFromDto(dto);
        List<ReqWorkSkillBO> dtos = bo.getSkills();

        boolean isNotExits = Objects.isNull(workSkillTemplateRepository.getWorkSkillTemplateByName(dto.getTemplateTitle()));
        return Option.when(isNotExits, () ->
            Option.when(CollUtil.isNotEmpty(dtos), Try.of(() -> {
                //save template
                WorkSkillTemplate template = workSkillTemplateDomainService.saveTemplate(bo);
                //save template setting data
                workSkillTemplateDomainService.doSaveTemplateSettingData(template, bo);
                return true;
            }).getOrElseThrow(e -> new BizException(WORK_SKILL_TEMPLATE_NO_SKILL.getErrCode(), WORK_SKILL_TEMPLATE_NO_SKILL.getErrDesc()))).getOrElse(false)
        ).getOrElseThrow(() -> new BizException(WORK_SKILL_TEMPLATE_NAME_EXITS.getErrCode(), WORK_SKILL_TEMPLATE_NAME_EXITS.getErrDesc()));
    }


    public Map<String, List<UserSkillAllPO>> getDetails(String skillTemplateId) {
        List<UserSkillAllPO> allSkills = Option.of(workSkillDomainService.all()).getOrElse(ArrayList::new);
        List<WorkSkillTemplateSetting> workSkillTemplateSettings = workSkillTemplateDomainService.queryByTemplateSettingByTemplateId(skillTemplateId);

        initAllSkillByTemplateSkillSetting(allSkills, workSkillTemplateSettings);

        userSettingSkillAppService.setSkillRule(allSkills);

        return allSkills.stream()
            .collect(Collectors.groupingBy(UserSkillAllPO::getSkillTypeName));
    }


    private void initAllSkillByTemplateSkillSetting(List<UserSkillAllPO> allSkills, List<WorkSkillTemplateSetting> templateSettings) {
        Option.when(CollUtil.isNotEmpty(templateSettings) && CollUtil.isNotEmpty(allSkills), Try.of(() -> {
            Map<String, List<UserSkillAllPO>> skillAllPOMap = allSkills.stream()
                .collect(Collectors.groupingBy(UserSkillAllPO::getSkillId));
            templateSettings.stream().forEach(template -> {
                if (skillAllPOMap.containsKey(template.getWorkSkillId())) {
                    UserSkillAllPO po = skillAllPOMap.get(template.getWorkSkillId()).get(0);
                    po.setAble(template.isAble());
                    List<WorkSkillTemplateSettingGameList> gameLists = workSkillTemplateDomainService.queryByTemplateSettingId(template.getTemplateSettingId());
                    po.setMaxLimit(template.getMaxLimit());
                    po.setMaxSessionLimit(template.getMaxSessionLimit());
                    if (po.isSkillRelationGame()) {
                        List<GamePO> games = gameLists.stream().map(game -> {
                            GamePO gameDTO = new GamePO();
                            gameDTO.setGameName(game.getGameName());
                            gameDTO.setGameId(game.getGameId());
                            return gameDTO;
                        }).toList();
                        po.setGames(games);
                        po.setRelationGameCount(games.size());
                    }
                }
            });
            return true;
        }));
    }



    private List<UserSkillAllPO> translateTemplateSettingsToUserSkill(List<WorkSkillTemplateSetting> templateSettings){
        if (CollUtil.isEmpty(templateSettings)) {
            return new ArrayList<UserSkillAllPO>();
        }

        return templateSettings.stream().map(t->{
            UserSkillAllPO po = new UserSkillAllPO();
            po.setSkillId(t.getWorkSkillId());
            po.setAble(t.isAble());
            po.setMaxLimit(t.getMaxLimit());
            po.setMaxSessionLimit(t.getMaxSessionLimit());
            List<WorkSkillTemplateSettingGameList> gameLists = workSkillTemplateDomainService.queryByTemplateSettingId(t.getTemplateSettingId());
            if (CollUtil.isNotEmpty(gameLists)) {
                po.setSkillRelationGame(true);
                List<GamePO> games = gameLists.stream().map(game -> {
                    GamePO gameDTO = new GamePO();
                    gameDTO.setGameName(game.getGameName());
                    gameDTO.setGameId(game.getGameId());
                    return gameDTO;
                }).toList();
                po.setGames(games);
                po.setRelationGameCount(games.size());
            }

            return po;
        }).toList();
    }



    public List<WorkUserSkillTemplateSelectListRespDTO> selectUserList(String userId) {
        return workSkillTemplateDomainService.selectUserList(userId);
    }


    public boolean deleteTemplate(String templateId){
        try {
            workSkillTemplateDomainService.deleteByTemplateId(templateId);
        } catch (Exception e) {
            log.error("delete skill template error {}", e.getMessage());
            return false;
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean edit(WorkSkillTemplateEditReqDTO dto){

        userSettingSkillAppService.checkParamSkillSetting(dto.getSkills());
        if (CharSequenceUtil.isEmpty(dto.getTemplateId())) {
            return false;
        }

        WorkSkillTemplate orginWorkSkillTemplate = workSkillTemplateRepository.getWorkSkillTemplateByName(dto.getTemplateTitle());
        if (Objects.isNull(orginWorkSkillTemplate)) {
            throw new BizException("work-shunt003","模版不存在");
        }

        ReqSaveWorkSkillTemplateBO bo = WorkSkillTemplateMapping.INSTANCE.toReqSaveWorkSkillTemplateEditDto(dto);
        List<ReqWorkSkillBO> dtos = bo.getSkills();

        if(CollUtil.isEmpty(dtos)){
            return false;
        }


        workSkillTemplateDomainService.deleteByTemplateId(orginWorkSkillTemplate.getSkillTemplateId());

        return Try.of(() -> {
            //save template
            WorkSkillTemplate template = workSkillTemplateDomainService.saveTemplate(bo);
            //save template setting data
            workSkillTemplateDomainService.doSaveTemplateSettingData(template, bo);
            dto.setTemplateId(template.getSkillTemplateId());
            List<WorkUser> workUsers = workUserDomainService.queryWorkersByTemplateId(orginWorkSkillTemplate.getSkillTemplateId());
            relateUser(dto, workUsers);
            return true;
        }).getOrElseThrow(e -> new BizException(WORK_SKILL_TEMPLATE_NO_SKILL.getErrCode(), e.getMessage()));

    }


    private void relateUser(WorkSkillTemplateEditReqDTO template, List<WorkUser> workUsers) {

        if (CollUtil.isEmpty(workUsers)) {
            return;
        }

        List<ReqWorkSkillDTO> skillDTOS = template.getSkills();

        overwriteWorkUserMaxLimit(template, workUsers, skillDTOS);

        workUsers.forEach(c->{
            ReqSaveWorkUserSettingSkillDTO builder = ReqSaveWorkUserSettingSkillDTO.builder()
                .workUserId(c.getWorkUserId())
                .userId(c.getUserId())
                .deptId(c.getDeptId())
                .deptType(c.getDeptType())
                .templateId(template.getTemplateId())
                .merchantId(c.getMerchantId())
                .skills(Option.when(CollUtil.isNotEmpty(skillDTOS), ()->
                    skillDTOS.stream().map(this::translateTo).toList()
                ).getOrElseThrow(()-> new BizException("","关联模版异常:模版技能不能为空"))).build();

            userSettingSkillAppService.save(builder);
        });
    }


    private void overwriteWorkUserMaxLimit(WorkSkillTemplateEditReqDTO template, List<WorkUser> workUsers, List<ReqWorkSkillDTO> skillDTOS) {

        if(Boolean.TRUE.equals(template.getIsOverrideMaxLimit())){
            return;
        }

        List<String> workUserIds = workUsers.stream().map(WorkUser::getWorkUserId).toList();
        List<WorkUserSettingSkill> workUserSettingSkills = userSettingSkillRepository.queryAllStatusByWorkUserIds(workUserIds);
        Map<String, List<WorkUserSettingSkill>> skillMaps = workUserSettingSkills.stream().collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkUserId));


        workUsers.forEach(workUser -> {
            List<WorkUserSettingSkill> settingSkills = skillMaps.get(workUser.getWorkUserId());
            Map<String, WorkUserSettingSkill> map = settingSkills.stream().collect(Collectors.toMap(WorkUserSettingSkill::getWorkSkillId, Function.identity()));
            skillDTOS.forEach(skillDTO -> {
                WorkUserSettingSkill workUserSettingSkill = map.getOrDefault(skillDTO.getSkillId(), null);
                if (Objects.nonNull(workUserSettingSkill)) {
                    skillDTO.setMaxLimit(workUserSettingSkill.getMaxLimit());
                    skillDTO.setMaxSessionLimit(workUserSettingSkill.getMaxSessionLimit());
                }
            });
        });
    }


    public Boolean relateUser(WorkTemplateRelateUserReqDTO dto) {

        String templateId = dto.getTemplateId();
        List<WorkSkillTemplateSetting> templateSettings =  workSkillTemplateDomainService.queryByTemplateSettingByTemplateId(templateId);
        List<UserSkillAllPO> allSkills = translateTemplateSettingsToUserSkill(templateSettings);
        if (CollUtil.isEmpty(allSkills)) {
            return false;
        }
        ReqSaveWorkUserSettingSkillDTO builder = ReqSaveWorkUserSettingSkillDTO.builder()
            .workUserId(dto.getWorkUserId())
            .userId(dto.getUserId())
            .deptId(dto.getDeptId())
            .deptType(dto.getDeptType())
            .templateId(dto.getTemplateId())
            .merchantId(dto.getMerchantId())
            .skills(Option.when(CollUtil.isNotEmpty(allSkills), ()->
                allSkills.stream().map(this::translateTo).toList()
            ).getOrElseThrow(()-> new BizException("","关联模版异常:模版技能不能为空"))).build();

        return manualDataTransactionService.manual(Try.of(() -> {
            userSettingSkillAppService.save(builder);
            return true;
        })).getOrElse(false);
    }



    private ReqWorkSkillDTO translateTo(UserSkillAllPO po) {
        return ReqWorkSkillDTO.builder()
                .skillId(po.getSkillId())
                .able(po.isAble())
                .skillRelationGame(po.isSkillRelationGame())
                .maxLimit(po.getMaxLimit())
                .maxSessionLimit(po.getMaxSessionLimit())
                .games(translateToGamePO(po.getGames())).build();
    }



    private ReqWorkSkillDTO translateTo(ReqWorkSkillDTO po) {
        return ReqWorkSkillDTO.builder()
                .skillId(po.getSkillId())
                .able(po.isAble())
                .skillRelationGame(po.isSkillRelationGame())
                .maxLimit(po.getMaxLimit())
                .maxSessionLimit(po.getMaxSessionLimit())
                .games(po.getGames()).build();
    }



    private List<GameDTO> translateToGamePO(List<GamePO> po){
        return Option.when(CollUtil.isNotEmpty(po),()-> po.stream().map(m->{
            GameDTO dto = new GameDTO();
            dto.setGameId(m.getGameId());
            dto.setGameName(m.getGameName());
            return dto;
        }).toList()).getOrElse(ArrayList::new);
    }
}
