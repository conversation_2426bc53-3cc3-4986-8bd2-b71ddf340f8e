package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqEditWorkSkillDTO;
import com.pxb7.mall.workshunt.app.model.ReqPageListWorkSkillDTO;
import com.pxb7.mall.workshunt.app.model.SaveWorkSkillReqDTO;
import com.pxb7.mall.workshunt.app.model.WorkSkillDetailRespDTO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqPageListWorkSkillBO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqSaveWorkSkillBO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WorkSkillMapping {

    WorkSkillMapping INSTANCE = Mappers.getMapper(WorkSkillMapping.class);

    ReqPageListWorkSkillBO toReqPageListWorkSkillBOFromDto(ReqPageListWorkSkillDTO dto);

    ReqSaveWorkSkillBO toReqSaveWorkSkillBOFromDto(SaveWorkSkillReqDTO dto);

    WorkSkill toReqEditWorkSkillBOFromDto(ReqEditWorkSkillDTO dto);

    WorkSkillDetailRespDTO toWorkSkillDetailRespDTO(WorkSkill workSkill);

}
