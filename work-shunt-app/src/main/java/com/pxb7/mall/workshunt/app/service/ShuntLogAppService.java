package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.pxb7.mall.workshunt.app.mapping.WorkShuntLogMapping;
import com.pxb7.mall.workshunt.client.work.GetWorkVerifyLogIdDubboDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.WorkShuntLogPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.*;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqAddGroupWorkShuntBO;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqGroupTransferShuntShuntBO;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqMobileVerifyLogIdBo;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqPageListWorkShuntLogBO;
import com.pxb7.mall.workshunt.domain.service.ShuntLogDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.pxb7.mall.workshunt.domain.Constants.NO_LIMIT;
import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.WORK_SKILL_USER_NO_MATCH_RULE;


@Slf4j
@Service
public class ShuntLogAppService {

    @Autowired
    private ShuntLogDomainService shuntLogDomainService;

    @Autowired
    private ShuntAppService shuntAppService;

    @Autowired
    private WorkUserDomainService workUserDomainService;


    public Boolean confirmWorkload(ReqVerifyLogIdDTO dto){
        ReqMobileVerifyLogIdBo bo = WorkShuntLogMapping.INSTANCE.toReqMobileVerifyLogIdBoFromDto(dto);
        return shuntLogDomainService.confirmWorkload(bo);
    }

    public Boolean closeDetailsId(ReqVerifyLogIdDTO dto){
        ReqMobileVerifyLogIdBo bo = WorkShuntLogMapping.INSTANCE.toReqMobileVerifyLogIdBoFromDto(dto);
        return shuntLogDomainService.closeDetailsId(bo);
    }

    public Page<WorkShuntLogPageListRespDTO> listPageWorkShuntLog(ReqPageListWorkShuntLogDTO dto) {
        ReqPageListWorkShuntLogBO bo = WorkShuntLogMapping.INSTANCE.toReqPageListWorkShuntLogBOFromDto(dto);
        return shuntLogDomainService.listPageWorkShuntLog(bo);
    }

    public GetWorkShuntRespDTO addWorkloadToUser(ReqAddGroupWorkShuntDTO dto) {
        ReqAddGroupWorkShuntBO bo = WorkShuntLogMapping.INSTANCE.toReqAddGroupWorkShuntBOFromDto(dto);
        String userId = dto.getUserId();

        if (dto.getFilterAddWorkload()) {
            checkWorkUser(dto.getSkillCode(), dto.getDomain(), userId);
            return GetWorkShuntRespDTO.builder().skillCode(dto.getSkillCode()).userId(dto.getUserId()).build();
        }

        return shuntLogDomainService.addGroupShunt(bo);
    }

    public GetWorkShuntRespDTO groupTransferShunt(ReqGroupTransferShuntShuntDTO dto) {
        ReqGroupTransferShuntShuntBO bo = WorkShuntLogMapping.INSTANCE.toReqGroupTransferShuntShuntBOFromDto(dto);

        checkWorkUser(dto.getSkillCode(), dto.getDomain(), dto.getTargetUserId());

        return shuntLogDomainService.groupTransferShunt(bo);
    }


        /**
     * Checks if the given user is assigned to the specified skill code and domain.
     * If the user is not found or not assigned to the skill, a BizException is thrown.
     *
     * @param skillCode The skill code to check for user assignment.
     * @param domain The domain to check for user assignment.
     * @param userId The user ID to check for skill assignment.
     * @throws BizException If the user is not assigned to the specified skill code and domain.
     */
    private void checkWorkUser(String skillCode, String domain, String userId) {
        List<GetWorkShuntRespDTO> workUserList = shuntAppService.shunt(ReqGetWorkShuntDTO.builder()
            .skillCodes(Sets.newHashSet(skillCode))
            .personCount(NO_LIMIT)
            .domain(domain)
            .build());

        if (CollUtil.isEmpty(workUserList) ||
            CollUtil.isEmpty(workUserList.stream().filter(w -> w.getUserId().equals(userId)).toList())) {
            throw new BizException(WORK_SKILL_USER_NO_MATCH_RULE.getErrCode(), WORK_SKILL_USER_NO_MATCH_RULE.getErrDesc());
        }
    }


    public Boolean dubboCloseDetailsLogId(GetWorkVerifyLogIdDubboDTO dto) {
        ReqMobileVerifyLogIdBo bo = WorkShuntLogMapping.INSTANCE.toReqMobileVerifyLogIdBoFromDubboDto(dto);
        return shuntLogDomainService.closeDetailsId(bo);
    }

    public Boolean batchMinutesWorkVolumes(List<String> skillCodes, List<String> userIds){
        return shuntLogDomainService.batchMinusWorkShuntOrderVolume(skillCodes, userIds);
    }

}
