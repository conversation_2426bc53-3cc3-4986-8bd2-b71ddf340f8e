package com.pxb7.mall.workshunt.app.service;

import com.alibaba.excel.EasyExcel;
import com.pxb7.mall.user.admin.api.SysUserServiceI;
import com.pxb7.mall.workshunt.app.model.ExcelImportData;
import com.pxb7.mall.workshunt.app.model.WorkErrorMessage;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class WorkImportAppService {

    @Resource
    private UserCenterGateway userCenterGateway;

    @DubboReference
    private SysUserServiceI adminUserService;

    @Resource
    private WorkUserExcelListener workUserExcelListener;

    @Setter
    @Getter
    private List<WorkErrorMessage> errorMessages = new ArrayList<>();

    public boolean importExcel() {
        InputStream fileName = WorkImportAppService.class.getClassLoader().getResourceAsStream("1.xlsx");
        List<ExcelImportData> datas = new ArrayList<>();
        workUserExcelListener.setDatas(datas);
        EasyExcel.read(fileName, ExcelImportData.class, workUserExcelListener).sheet().doRead();

        return true;
    }
}
