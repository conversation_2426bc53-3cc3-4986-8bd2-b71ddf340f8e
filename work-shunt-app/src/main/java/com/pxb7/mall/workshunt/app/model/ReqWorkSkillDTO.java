package com.pxb7.mall.workshunt.app.model;

import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReqWorkSkillDTO implements Serializable {

        /**
         * 技能ID
         */
        @NotNull(message = "技能ID不能为空")
        private String skillId;
        /**
         * 设置接待上线
         */
        private Long maxLimit;

        /**
         * 接待会话并发上限
         */
        private Long maxSessionLimit;

        /**
         * 是否禁用
         */
        private boolean able;

        private boolean skillRelationGame;
        /**
         * 设置游戏
         */
        private List<GameDTO> games;
}