package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.client.work.databoard.ReqListDataBoardDeptWorkUserDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ReqWorkShuntGroupDTO;
import com.pxb7.mall.workshunt.domain.model.workUser.*;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.WorkUserBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;



/**
* 排班用户 -> WorkUser  mapstruct
*/
@Mapper
public interface WorkUserMapping {

    WorkUserMapping INSTANCE = Mappers.getMapper(WorkUserMapping.class);

    /**
    * 排班用户 -> PageList
    */
    ReqPageListWorkUserBO toReqPageListWorkUserBOFromDto(ReqPageListWorkUserDTO dto);
    /**
    * 排班用户 -> Save
    */
    ReqSaveWorkUserBO toReqSaveWorkUserBOFromDto(ReqSaveWorkUserDTO dto);
    /**
    * 排班用户 -> Edit
    */
    ReqEditWorkUserBO toReqEditWorkUserBOFromDto(ReqEditWorkUserDTO dto);



    ReqListWorkUserBO toReqListWorkUserBOToFromDto(ReqListDeptWorkUserDTO dto);

    @Mappings({@Mapping(target = "userIds", source = "userIds")})
    ReqWorkUserSelectDeptUserRespDTO toReqWorkUserSelectDeptUserRespDTO(ReqListDeptWorkUserDTO dto);

    ReqWorkUserSelectDeptUserRespDTO toReqWorkUserSelectDeptUserRespDTOFromBoard(ReqListDataBoardDeptWorkUserDTO dto);

    @Mappings({@Mapping(target = "userId", source = "userId"),
        @Mapping(target = "deptId", source = "deptId"),
        @Mapping(target = "deptType", source = "deptType")})
    WorkUserBO workUserBoTransformUserSettingSkillDTO(ReqSaveWorkUserSettingSkillDTO dto);

    @Mappings({@Mapping(target = "userId", source = "userId"),
        @Mapping(target = "deptId", source = "deptId"),
        @Mapping(target = "deptType", source = "deptType")})
    WorkUserBO workUserBoTransformUserSettingTimeDTO(ReqSaveWorkUserSettingDayDTO dto);

    WorkSkillCodeGroupShuntBO toReqWorkShuntGroupDTO(ReqWorkShuntGroupDTO reqWorkShuntGroupDTO);

}
