package com.pxb7.mall.workshunt.app.model;

import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class WorkSkillGroupBySkillTypeRespDTO {

    private String skillTypeId;
    private String skillTypeName;
    private List<UserSkill> skills;

    @Data
    public static class UserSkill{
        /**
         * 技能ID
         */
        private String workSkillId;
        /**
         * 技能名称
         */
        private String workSkillName;

        /**
         * 是否关联游戏1：是0否
         */
        private Boolean skillRelationGame;

        /**
         * 设置接待上线
         */
        private Integer ruleMaxNum;

        /**
         * 初开始数据
         */
        @NotNull(message = "初开始数据不能为空")
        private int earlyStart ;
        /**
         * 初结束数据
         */
        @NotNull(message = "初结束数据不能为空")
        private int earlyEnd ;
        /**
         * 中开始数据
         */
        @NotNull(message = "中开始数据不能为空")
        private int middleStart ;
        /**
         * 中结束数据
         */
        @NotNull(message = "中结束数据不能为空")
        private int middleEnd ;
        /**
         * 高开始数据
         */
        @NotNull(message = "高开始数据不能为空")
        private int highStart ;
        /**
         * 高结束数据
         */
        @NotNull(message = "高结束数据不能为空")
        private int highEnd ;
        /**
         * 专家开始数据
         */
        @NotNull(message = "专家开始数据不能为空")
        private int expertStart ;
        /**
         * 专家结束数据
         */
        @NotNull(message = "专家结束数据不能为空")
        private int expertEnd ;

        /**
         * 设置游戏列表
         */
        private List<GameDTO> gameDTOList;
    }
}


