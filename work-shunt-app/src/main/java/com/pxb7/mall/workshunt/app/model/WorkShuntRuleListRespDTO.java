package com.pxb7.mall.workshunt.app.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkShuntRuleListRespDTO implements Serializable {

    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 技能ID
     */
    private String skillName;

    /**
     * 非溢出状态 ->1 分配条件
     */
    List<String> noOverFlowCondition;
    /**
     * 非溢出状态 ->1 分配比例
     */
    String noOverFlowRuleSetting;

    /**
     * 非溢出状态 ->2 分配条件
     */
    List<String> overFlowCondition;
    /**
     * 非溢出状态 ->2 分配比例
     */
    String overFlowRuleSetting;

}