package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.google.common.collect.Lists;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ValidationWorkerReqDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校验业务提供的客服
 * 无满足客服，自动分配客服
 */
@Service
@Slf4j
public class ShuntValidationService {

    @Resource
    private WorkUserDomainService workUserDomainService;
    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;


    @Resource
    private ShuntAppService shuntAppService;

    @Resource
    private ShuntSupportService shuntSupportService;

    /**
     * 过滤游戏，客服，技能
     * @param dto
     * @return
     */
    public List<GetWorkShuntRespDTO> validationWorkUser(ValidationWorkerReqDTO dto) {

        List<WorkUser> workUsers = Option.when(CollUtil.isNotEmpty(dto.getUserIds()), ()-> workUserDomainService.queryByUserIds(dto.getUserIds())).filter(CollUtil::isEmpty).getOrElse(ArrayList::new);

        List<WorkUserSettingSkill> settingSkills = Option.when(CollUtil.isNotEmpty(workUsers), ()->
            userSettingSkillDomainService.queryByWorkUserIds(workUsers.stream().map(WorkUser::getWorkUserId).toList())).getOrElse(ArrayList::new);

        Map<String, List<WorkUserSettingSkill>> settingMap = settingSkills.stream().collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkSkillId));
        WorkSkill skill = Option.of(workSkillDomainService.queryBySKillCode(dto.getSkillCode())).getOrElseThrow(()-> new BizException("","不存在该技能"));

        List<WorkUserSettingSkill> filterSettings = Option.when(CollUtil.isNotEmpty(settingMap), ()-> settingMap.get(skill.getSkillId())).getOrElse(ArrayList::new);

        //过滤游戏
        List<WorkUserSettingSkill> filterGameSettings = shuntSupportService.filterSettingSkillRelateGame(filterSettings, shuntSupportService.transformByWorkSkill(List.of(skill)), dto.getGameId());

        //通过技能编码过滤后的技能获取客服
        List<WorkUser> filterWorkUsers = Option.when(CollUtil.isNotEmpty(filterGameSettings), ()-> workUserDomainService.query(filterGameSettings.stream().map(WorkUserSettingSkill::getWorkUserId).toList(), null)).getOrElse(ArrayList::new);

        //验证符合排班规则条件的客服
        List<WorkUser> finalWorkUsers = Option.when(CollUtil.isNotEmpty(filterWorkUsers), () -> shuntSupportService.filterWorkerByShuntRule(shuntSupportService.transformByWorkSkill(List.of(skill)), filterWorkUsers, dto.getCount())).getOrElse(ArrayList::new);

        //不存在,直接分配新的客服
        if(CollUtil.isEmpty(filterWorkUsers)){
            return shuntAppService.shunt(ReqGetWorkShuntDTO.builder().domain(dto.getDomain()).merchantId(dto.getMerchantId()).personCount(dto.getCount()).skillCodes(new HashSet<>(Lists.newArrayList(dto.getSkillCode()))).build());
        }

        return shuntSupportService.shuntWorkUserResponse(finalWorkUsers, List.of(skill));
    }
}
