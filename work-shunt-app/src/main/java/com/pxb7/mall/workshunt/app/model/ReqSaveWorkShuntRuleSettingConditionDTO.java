package com.pxb7.mall.workshunt.app.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReqSaveWorkShuntRuleSettingConditionDTO implements Serializable {
    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 列表返回就传入 -- 分流规则ID
     */
    private String shuntRuleId;
    /**
     * 分流规则类型1非溢出状态2溢出状态
     */
    private String shuntRuleType;
    /**
     * 条件类型 1当前时间排班内 2未超接待上限制 3IM在线
     */
    private String conditionType;
}