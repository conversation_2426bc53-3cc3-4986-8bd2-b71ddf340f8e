package com.pxb7.mall.workshunt.app.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ReqEditWorkUserDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 工作用户ID
     */
    private String workUserId;
    /**
     * 部门ID
     */
    private String pkDeptId;
    /**
     * 用户ID
     */
    private String pkUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 部门类型1公司部门2号商部门
     */
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
}