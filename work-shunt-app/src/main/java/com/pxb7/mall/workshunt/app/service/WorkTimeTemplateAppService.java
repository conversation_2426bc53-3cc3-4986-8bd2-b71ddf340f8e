package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.app.mapping.WorkTimeTemplateMapping;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.domain.service.TimeTemplateDomainService;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserSettingDayRelationWorkTemplatRepository;
import io.vavr.CheckedFunction0;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;


@Slf4j
@Service
public class WorkTimeTemplateAppService {

    @Resource
    private TimeTemplateDomainService workTimeTemplateDomainService;

    @Resource
    private WorkUserSettingDayRelationWorkTemplatRepository workUserSettingDayRelationWorkTemplatRepository;

    public Page<TimeTemplatePageListRespDTO> listPage(ReqPageListWorkTimeTemplateDTO dto) {
        return Option.of(
            WorkTimeTemplateMapping.INSTANCE.toPageListDTOByBo(
                workTimeTemplateDomainService.listPage(WorkTimeTemplateMapping.INSTANCE.toReqPageListWorkTimeTemplateBOFromDto(dto))
            )
            ).getOrElse(Page.of(0, 0));
    }

    public boolean save(WorkTimeTemplateSaveReqDTO dto) {
        if(workTimeTemplateDomainService.checkRepeat(dto.getTitle())){
            throw new BizException(WORK_TIME_CHECK_REPEAT_TEMPLATE_SAVE.getErrCode(), WORK_TIME_CHECK_REPEAT_TEMPLATE_SAVE.getErrDesc());
        }
        CheckedFunction0<Boolean> save = () -> workTimeTemplateDomainService.save(WorkTimeTemplateMapping.INSTANCE.toReqSaveWorkTimeTemplateBOFromDto(dto));
        return Try.of(save).getOrElseThrow(e -> {
            log.error("{}-{}", ErrorCode.WORK_TIME_TEMPLATE_SAVE.getErrCode(), e.getMessage());
            return new BizException(ErrorCode.WORK_TIME_TEMPLATE_SAVE.getErrCode(), ErrorCode.WORK_TIME_TEMPLATE_SAVE.getErrDesc());
        });
    }


    public boolean edit(ReqEditWorkTimeTemplateDTO dto) {

        boolean exits = workTimeTemplateDomainService.checkRepeat(dto.getTitle(), dto.getTimeTemplateId());

        CheckedFunction0<Boolean> edit = () -> workTimeTemplateDomainService.edit(WorkTimeTemplateMapping.INSTANCE.toReqEditWorkTimeTemplateBOFromDto(dto));
        Function<Throwable, BizException> editThrown = e -> {
            log.error("{}-{}", ErrorCode.WORK_TIME_TEMPLATE_EDIT.getErrCode(), e.getMessage());
            return new BizException(ErrorCode.WORK_TIME_TEMPLATE_EDIT.getErrCode(), ErrorCode.WORK_TIME_TEMPLATE_EDIT.getErrDesc());
        };

        if(CollUtil.isNotEmpty(workUserSettingDayRelationWorkTemplatRepository.queryBy(dto.getTimeTemplateId()))){
            throw new BizException(WORK_SKILL_TIME_TEMPLATE_EDIT_USER_SETTING.getErrCode(), WORK_SKILL_TIME_TEMPLATE_EDIT_USER_SETTING.getErrDesc());
        }

        return Option.when(!exits, Try.of(edit).getOrElseThrow(editThrown))
            .getOrElseThrow(() -> {
            throw new BizException(WORK_TIME_CHECK_REPEAT_TEMPLATE_SAVE.getErrCode(), WORK_TIME_CHECK_REPEAT_TEMPLATE_SAVE.getErrDesc());
        });

    }

    public boolean delete(String workTemplateId) {

        if(CollUtil.isNotEmpty(workUserSettingDayRelationWorkTemplatRepository.queryBy(workTemplateId))){
            throw new BizException(WORK_SKILL_TIME_TEMPLATE_DELETE_USER_SETTING.getErrCode(), WORK_SKILL_TIME_TEMPLATE_DELETE_USER_SETTING.getErrDesc());
        }
        return Option.when(workTimeTemplateDomainService.exist(workTemplateId),
            Try.of(()-> workTimeTemplateDomainService.delete(workTemplateId))
            .getOrElseThrow(e->{
                log.error("{}-{}", WORK_TIME_TEMPLATE_DEL.getErrCode(), e.getMessage());
                return new BizException(WORK_TIME_TEMPLATE_DEL.getErrCode(), WORK_TIME_TEMPLATE_DEL.getErrDesc());
            })).getOrElseThrow(()->{
            throw new BizException(WORK_TIME_TEMPLATE_EXIST.getErrCode(), WORK_TIME_TEMPLATE_EXIST.getErrDesc());
        });
    }

    public List<TimeTemplateDropdownDTO> dropdown() {
        return Option.of(WorkTimeTemplateMapping.INSTANCE.toDropdownDTO(workTimeTemplateDomainService.dropdown()))
            .getOrElse(ArrayList::new);
    }
}
