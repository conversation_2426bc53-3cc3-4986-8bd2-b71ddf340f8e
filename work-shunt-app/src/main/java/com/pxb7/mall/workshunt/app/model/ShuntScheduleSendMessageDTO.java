package com.pxb7.mall.workshunt.app.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Builder
public class ShuntScheduleSendMessageDTO {
    private String skillName;
    private int onlineCount;
    private int offlineCount;
    private int skillBindGameCount;
    private boolean skillRelationGame;
    private int gameCount;

    public String toString(){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(skillName).append("：IM在线").append(onlineCount).append("人，").append("离线：").append(offlineCount).append("人，");
        if (!skillRelationGame) {
            stringBuffer.append("技能无绑定游戏。").append("\n");
            return stringBuffer.toString();
        }
        stringBuffer.append("可服务游戏").append(skillBindGameCount);
        if (skillBindGameCount < gameCount) {
            stringBuffer.append("，低于今日游戏").append(gameCount).append("，请及时关注！").append("\n");
        }else{
            stringBuffer.append("正常。").append("\n");
        }
        return stringBuffer.toString();
    }
}
