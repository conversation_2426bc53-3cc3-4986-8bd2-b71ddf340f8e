package com.pxb7.mall.workshunt.app.service;

import com.pxb7.mall.workshunt.app.mapping.WorkShuntRuleSettingConditionMapping;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkShuntRuleSettingConditionDTO;
import com.pxb7.mall.workshunt.domain.model.workShuntRuleSettingCondition.ReqSaveWorkShuntRuleSettingConditionBO;
import com.pxb7.mall.workshunt.domain.service.ShuntRuleConditionDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WorkShuntRuleSettingConditionAppService {

    @Resource
    private ShuntRuleConditionDomainService workShuntRuleSettingConditionDomainService;

    public Boolean save(ReqSaveWorkShuntRuleSettingConditionDTO dto) {
        ReqSaveWorkShuntRuleSettingConditionBO bo = WorkShuntRuleSettingConditionMapping.INSTANCE.toReqSaveWorkShuntRuleSettingConditionBOFromDto(dto);
        return true;
    }

    public Boolean deleteWorkShuntRuleSettingCondition(String settingConditionId) {
         return workShuntRuleSettingConditionDomainService.deleteWorkShuntRuleSettingCondition(settingConditionId);
    }
}
