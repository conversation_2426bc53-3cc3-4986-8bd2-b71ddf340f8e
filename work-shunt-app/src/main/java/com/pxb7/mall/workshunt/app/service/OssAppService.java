package com.pxb7.mall.workshunt.app.service;

import com.alibaba.cola.exception.BizException;
import com.aliyuncs.exceptions.ClientException;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;
import com.pxb7.mall.workshunt.infra.repository.gateway.sdk.OssGateway;
import com.pxb7.mall.workshunt.infra.util.AesUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;

/**
 * @Description oss 文件上传
 * <AUTHOR>
 * @Date 2024/9/7 15:41
 */
@Slf4j
@Service
public class OssAppService {

    /**
     * aes加密密钥
     */
    private static final String AES_SECRET_KEY = "A2M0ebV5eN9wON5K2-zvbvOnT_qMyTDW8XSurv3lj5w=";

    @Resource
    private OssGateway ossGateway;

    @PostConstruct
    public void init() {
        //预热aes算法
        try {
            AesUtil.encrypt("abc",AES_SECRET_KEY, AesUtil.generateIv());
        }catch (Exception e){
            log.error("预热AesUtil加密算法出错,", e);
            throw new BizException(ErrorCode.SYSTEM_ERROR.getErrDesc());
        }
    }

    public String uploadUrlFile(String url, String targetPaths) {
        try {
            return ossGateway.uploadFile(url, targetPaths);
        } catch (Exception e) {
            log.error("Failed to upload file from URL , system encountered an exception", e);
        }
        return null;
    }

    public List<String> batchUploadUrlFile(List<String> urls, List<String> targetPaths) {
        try {
            return ossGateway.batchUploadFile(urls, targetPaths);
        } catch (ClientException e) {
            log.error("Failed to upload file from URL , client encountered an exception", e);
            return Collections.emptyList();
        }
    }

    public String uploadFile(MultipartFile file, String fileName) {
        try {
            return ossGateway.uploadFile(file, fileName);
        } catch (Exception e) {
            log.error("Failed to upload file , system encountered an exception", e);
        }
        return null;
    }

    public String uploadFile(InputStream inputStream, String filePath) {
        return ossGateway.uploadFile(inputStream, filePath);
    }
}
