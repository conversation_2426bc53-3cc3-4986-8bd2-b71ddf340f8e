package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqDeleteWorkUserSettingDayDTO;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingDayDTO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingDay.ReqDeleteWorkUserSettingDayBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingDay.ReqSaveWorkUserSettingDayBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;



/**
* 用户时间 -> WorkUserSettingDay  mapstruct
*/
@Mapper
public interface WorkUserSettingDayMapping {

    WorkUserSettingDayMapping INSTANCE = Mappers.getMapper(WorkUserSettingDayMapping.class);

    ReqSaveWorkUserSettingDayBO toReqSaveWorkUserSettingDayBOFromDto(ReqSaveWorkUserSettingDayDTO dto);

    ReqDeleteWorkUserSettingDayBO toReqDeleteWorkUserSettingDayBOFromDto(ReqDeleteWorkUserSettingDayDTO dto);
}
