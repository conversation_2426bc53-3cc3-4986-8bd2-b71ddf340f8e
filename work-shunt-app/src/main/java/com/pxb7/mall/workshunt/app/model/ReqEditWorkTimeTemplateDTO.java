package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqEditWorkTimeTemplateDTO implements Serializable {

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private String timeTemplateId;
    /**
     * 标题
     */
    @NotNull(message = "班次标题不能为空")
    private String title;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private String startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private String endTime;

}