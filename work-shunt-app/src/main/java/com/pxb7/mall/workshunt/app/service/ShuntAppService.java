package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workshunt.app.model.WorkSkillLevelDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.domain.service.ShuntLogDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;

@Service
@Slf4j
public class ShuntAppService {

    @Autowired
    private WorkSkillDomainService workSkillDomainService;

    @Autowired
    private ShuntLogDomainService shuntLogDomainService;

    @Autowired
    private ShuntSupportService shuntSupportService;


    /**
     * 分配客服
     *
     * @param dto
     * @return
     */
    public List<GetWorkShuntRespDTO> shunt(ReqGetWorkShuntDTO dto) {
        //begin log record time
        LocalDateTime requestTime = LocalDateTime.now();
        //转换技能
        List<WorkSkill> originSkills = workSkillDomainService.queryBySkillCodes(dto.getSkillCodes().stream().toList());
        if (CollUtil.isEmpty(originSkills)) {
            throw new BizException(WORK_SKILL_NO_EXITS.getErrCode(), WORK_SKILL_NO_EXITS.getErrDesc());
        }

        List<WorkSkillLevelDTO> skills = shuntSupportService.transformByWorkSkill(originSkills);

        //过滤游戏
        List<WorkUserSettingSkill> workUserSettingSkills = shuntSupportService.getUserSettingSkill(skills);
        List<WorkUserSettingSkill> filterSettingRelateGame = shuntSupportService.filterUserSettingRelateGame(workUserSettingSkills, skills, dto.getGameId());

        //获取客服
        List<WorkUser> workUsers = filterSpecifiedWorkUsers(shuntSupportService.getWorkUser(filterSettingRelateGame, dto.getMerchantId()), dto.getFilterUserIds());

        //检查技能数量是否和业务域需求数据一致
        List<WorkUser> filterWorkerUsers = filterWorkUserCount(dto, filterSettingRelateGame, workUsers, skills);

        //通过技能规则筛选
        List<WorkUser> finalWorkUsers = shuntSupportService.filterWorkerByShuntRule(skills, filterWorkerUsers, dto.getPersonCount());

        initAssignTimeAndSaveLog(dto, skills, finalWorkUsers, originSkills, requestTime);

        return Option.when(CollUtil.isNotEmpty(finalWorkUsers), () -> shuntSupportService.shuntWorkUserResponse(finalWorkUsers, originSkills))
            .getOrElse(ArrayList::new);
    }



    private void initAssignTimeAndSaveLog(ReqGetWorkShuntDTO dto,
                           List<WorkSkillLevelDTO> skills,
                           List<WorkUser> finalWorkUsers,
                           List<WorkSkill> originSkills,
                           LocalDateTime requestTime) {

        if (dto.getPersonCount() != -1) {
            Consumer<WorkUser> workUserConsumer = wc-> {
                skills.forEach(sk-> {
                    shuntLogDomainService.initAssignTimeToWorkUserVolume(wc.getUserId(), sk.getSkillId(), sk.getSkillCode());
                });
            };
            finalWorkUsers.forEach(workUserConsumer);
        }
        CompletableFuture.runAsync(()-> asynSaveLog(dto, originSkills, finalWorkUsers, requestTime));
    }



    public void asynSaveLog(ReqGetWorkShuntDTO dto, List<WorkSkill> originSkills, List<WorkUser> finalWorkUsers, LocalDateTime workTime) {
        Try<Void> trySave = Try.runRunnable(()-> {
            shuntLogDomainService.saveShuntLog(originSkills, dto, finalWorkUsers, workTime);
        });

        if (trySave.isFailure()) {
            log.error("save work shunt log error: {}", trySave.getCause().getMessage());
        }
    }


    public List<WorkUser> filterSpecifiedWorkUsers(List<WorkUser> users, Set<String> needFilterUserIds){
        return Option.when(CollUtil.isNotEmpty(users) && CollUtil.isNotEmpty(needFilterUserIds), ()-> users.stream().filter(f-> {
            for (String filterUserId : needFilterUserIds) {
                if(filterUserId.equals(f.getUserId())) return false;
            }
            return true;
        }).toList()).getOrElse(()-> users);
    }

    private static List<WorkUser> filterWorkUserCount(ReqGetWorkShuntDTO dto,
                                                      List<WorkUserSettingSkill> workUserSettingSkills,
                                                      List<WorkUser> workUsers,
                                                      List<WorkSkillLevelDTO> skills) {

        //筛选客服是否具备技能(eq: 客服具备多项技能)
        Map<String /*key=workerUserId*/, List<WorkUserSettingSkill>> workUserMap = workUserSettingSkills.stream()
            .collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkUserId));

        List<WorkUser> filterWorkerUsers = workUsers.stream()
            .filter(workUser -> workUserMap.get(workUser.getWorkUserId()).size() >= skills.size())
            .toList();
        if (CollUtil.isEmpty(filterWorkerUsers) || filterWorkerUsers.size() < dto.getPersonCount()) {
            throw new BizException(WORK_SKILL_USER_COUNT_NO_MATCH_SKILL.getErrCode(), WORK_SKILL_USER_COUNT_NO_MATCH_SKILL.getErrDesc());
        }
        return filterWorkerUsers;
    }


    private List<WorkUserSettingSkill> filterUserSettingByLevel(List<WorkSkillLevelDTO> skills,
                                                                List<WorkUserSettingSkill> us,
                                                                ReqGetWorkShuntDTO dto) {
        return Option.when(dto.getLevel() != 0, () -> shuntSupportService.filterSettingSkillLevel(us, skills, dto.getLevel()))
            .getOrElse(us);
    }

}
