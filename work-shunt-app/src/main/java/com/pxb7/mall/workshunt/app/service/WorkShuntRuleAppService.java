package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.pxb7.mall.workshunt.app.model.ShuntRuleSaveDTO;
import com.pxb7.mall.workshunt.app.model.WorkShuntRuleListRespDTO;
import com.pxb7.mall.workshunt.domain.service.ShuntRuleConditionDomainService;
import com.pxb7.mall.workshunt.domain.service.ShuntRuleSettingDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.infra.enums.ShuntRuleTypeEnum;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

@Slf4j
@Service
public class WorkShuntRuleAppService {

    @Resource
    private ShuntRuleConditionDomainService shuntRuleConditionDomainService;
    @Resource
    private ShuntRuleSettingDomainService shuntRuleSettingDomainService;

    @Resource
    private WorkSkillDomainService workSkillDomainService;


    final String overFlowType = ShuntRuleTypeEnum.OVERFLOW.getShuntRuleType();
    final String noOverFlowType = ShuntRuleTypeEnum.NO_OVERFLOW.getShuntRuleType();


    public List<WorkShuntRuleListRespDTO> listWorkShuntRule() {

        List<UserSkillAllPO> skills = workSkillDomainService.all();
        return Option.when(CollUtil.isNotEmpty(skills), translate(skills)).getOrElse(ArrayList::new);
    }


    private Supplier<List<WorkShuntRuleListRespDTO>> translate(List<UserSkillAllPO> skills) {
        return ()-> skills.stream().map(this::buildWorkShuntRuleListRespDTO).toList();
    }


    private WorkShuntRuleListRespDTO buildWorkShuntRuleListRespDTO(UserSkillAllPO skill) {

        WorkShuntRuleListRespDTO dto = new WorkShuntRuleListRespDTO();
        dto.setSkillId(skill.getSkillId());
        dto.setSkillName(skill.getSkillName());

        WorkShuntRuleSetting overSettings = shuntRuleSettingDomainService.queryBy(skill.getSkillId(), overFlowType);
        dto.setOverFlowRuleSetting(Option.when(!Objects.isNull(overSettings), ()-> overSettings.getShuntType()).getOrElse(""));

        WorkShuntRuleSetting no_overSettings = shuntRuleSettingDomainService.queryBy(skill.getSkillId(), noOverFlowType);
        dto.setNoOverFlowRuleSetting(Option.when(!Objects.isNull(no_overSettings), ()-> no_overSettings.getShuntType()).getOrElse(""));

        List<WorkShuntRuleSettingCondition> overs = shuntRuleConditionDomainService.queryBy(skill.getSkillId(), overFlowType);
        dto.setOverFlowCondition(Option.when(CollUtil.isNotEmpty(overs), ()-> overs.stream().map(WorkShuntRuleSettingCondition::getConditionType).toList()).getOrElse(ArrayList::new));

        List<WorkShuntRuleSettingCondition> on_over = shuntRuleConditionDomainService.queryBy(skill.getSkillId(), noOverFlowType);
        dto.setNoOverFlowCondition(Option.when(CollUtil.isNotEmpty(on_over), () -> on_over.stream().map(WorkShuntRuleSettingCondition::getConditionType).toList()).getOrElse(ArrayList::new));

        return dto;
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean save(ShuntRuleSaveDTO dto) {
        //delete rule condition
        shuntRuleConditionDomainService.deleteBySkillId(dto.getSkillId());
        //delete rule setting
        shuntRuleSettingDomainService.deleteBySkillId(dto.getSkillId());
        //save rule condition
        Option.when(CollUtil.isNotEmpty(dto.getOverFlowCondition()), () -> shuntRuleConditionDomainService.save(dto.getOverFlowCondition(), overFlowType, dto.getSkillId()));
        Option.when(CollUtil.isNotEmpty(dto.getNoOverFlowCondition()), () -> shuntRuleConditionDomainService.save(dto.getNoOverFlowCondition(), noOverFlowType, dto.getSkillId()));
        Option.when(CharSequenceUtil.isNotBlank(dto.getNoOverFlowRuleSetting()), () -> shuntRuleSettingDomainService.saveRuleSetting(dto.getNoOverFlowRuleSetting(), noOverFlowType, dto.getSkillId()));
        Option.when(CharSequenceUtil.isNotBlank(dto.getOverFlowRuleSetting()), ()->shuntRuleSettingDomainService.saveRuleSetting(dto.getOverFlowRuleSetting(), overFlowType
                , dto.getSkillId()));

        return true;

    }
}
