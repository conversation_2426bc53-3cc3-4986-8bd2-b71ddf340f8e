package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.product.client.api.admin.GameServiceI;
import com.pxb7.mall.product.client.dto.response.game.GameIdDTO;
import com.pxb7.mall.user.admin.api.SysUserServiceI;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetSysUserRespDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.workshunt.app.model.ExcelImportData;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingSkillDTO;
import com.pxb7.mall.workshunt.app.model.ReqWorkSkillDTO;
import com.pxb7.mall.workshunt.app.model.WorkErrorMessage;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import jakarta.annotation.Resource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkUserExcelListener implements ReadListener<ExcelImportData> {

    @Resource
    private UserCenterGateway userCenterGateway;

    @DubboReference
    private SysUserServiceI adminUserService;

    @DubboReference
    private GameServiceI gameServiceI;

    @Resource
    private UserSettingSkillAppService workUserSettingSkillAppService;

    @Setter
    private List<ExcelImportData> datas = new ArrayList<>();

    @Resource
    private WorkSkillRepository workSkillRepository;

    @Resource
    private WorkUserRepository workUserRepository;


    @Override
    public void invoke(ExcelImportData o, AnalysisContext context) {
        log.info("读取到一条数据{}", JSON.toJSONString(o));
        String data = o.getUserName();
        String[] datasArray = StringUtils.split(data, '\t');

        ExcelImportData excelImportData = new ExcelImportData();
        excelImportData.setUserName(datasArray[0]);
        excelImportData.setGameName(datasArray[1]);
        excelImportData.setMerchantName(datasArray[2]);
        datas.add(excelImportData);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

        log.info("读取total数据{}", JSON.toJSONString(datas));
        //获取技能
        WorkSkill workSkill = workSkillRepository.getTableOneDataByParamKeyCode("HSKF");
        if (CollUtil.isNotEmpty(datas)) {
            Map<String,List<ExcelImportData>> maps = datas.stream().collect(Collectors.groupingBy(ExcelImportData::getUserName));
            maps.keySet().forEach(d->{
                String account = d;
                SingleResponse<GetSysUserRespDTO> userResponse = adminUserService.getSysUserByUserName(account);
                if (Objects.isNull(userResponse) || !userResponse.isSuccess() || Objects.isNull(userResponse.getData())) {
                    addErrorMessage(account, "该用户不存在");
                    return;
                }
                GetSysUserRespDTO userBaseDTO = userResponse.getData();

                MultiResponse<GetUserRoleInfoRespDTO> userRoleInfoRespDTOMultiResponse = adminUserService.getUserRoleInfoByCondition(GetUserInfoReqDTO.builder()
                    .userName(account)
                    .userIds(Set.of(userBaseDTO.getUserId()))
                    .build());

                if (Objects.isNull(userRoleInfoRespDTOMultiResponse) || !userRoleInfoRespDTOMultiResponse.isSuccess() || userRoleInfoRespDTOMultiResponse.isEmpty()) {
                    addErrorMessage(account, "该用户角色信息");
                    return;
                }

                GetUserRoleInfoRespDTO getUserRoleInfoRespDTO = userRoleInfoRespDTOMultiResponse.getData().get(0);
                ReqSaveWorkUserSettingSkillDTO dto = new ReqSaveWorkUserSettingSkillDTO();
                dto.setUserId(getUserRoleInfoRespDTO.getUserId());
                dto.setDeptId(getUserRoleInfoRespDTO.getDeptId());
                dto.setMerchantId(getUserRoleInfoRespDTO.getMerchantId());
                if (CharSequenceUtil.isBlank(getUserRoleInfoRespDTO.getMerchantId())) {
                    dto.setDeptType("1");
                }else{
                    dto.setDeptType("3");
                }

                ReqWorkSkillDTO reqWorkSkillDTO = new ReqWorkSkillDTO();
                reqWorkSkillDTO.setAble(true);
                reqWorkSkillDTO.setMaxLimit(100L);
                reqWorkSkillDTO.setSkillRelationGame(true);

                reqWorkSkillDTO.setSkillId(workSkill.getSkillId());

                WorkUser workUser = workUserRepository.getTableOneDataByPkUserId(getUserRoleInfoRespDTO.getUserId());
                if (!Objects.isNull(workUser)) {
                    dto.setWorkUserId(workUser.getWorkUserId());
                }
                List<String> gameNames = maps.get(account).stream().map(ExcelImportData::getGameName).distinct().toList();
                List<GameDTO> games = new ArrayList<>(gameNames.size());

                gameNames.forEach(g->{
                    MultiResponse<GameIdDTO> response = gameServiceI.findGameIds(g);
                    if (Objects.isNull(response) || !response.isSuccess() || response.isEmpty()) {
                        return;
                    }

                    GameDTO gameDTO = new GameDTO();
                    gameDTO.setGameName(g);
                    gameDTO.setGameId(response.getData().get(0).getGameId());
                    games.add(gameDTO);

                });

                if (games.size() != gameNames.size()) {
                    log.error("game size 不同....");
                    return;
                }

                reqWorkSkillDTO.setGames(games);
                dto.setSkills(List.of(reqWorkSkillDTO));
                log.info("save data: {}", JSON.toJSONString(dto));
                try{
                    workUserSettingSkillAppService.save(dto);
                }catch (Exception e){
                    addErrorMessage(account, e.getMessage());
                }

            });
        }
    }

    private void addErrorMessage(String account, String errorMessage) {
        WorkErrorMessage message = new WorkErrorMessage();
        message.setAccount(account);
        message.setMessage(errorMessage);
    }

}
