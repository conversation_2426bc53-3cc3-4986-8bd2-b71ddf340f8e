package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Sets;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.ListSysDeptDubboRespDTO;
import com.pxb7.mall.workshunt.app.model.ReqWorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.app.model.WorkUserDropdownDeptInfoRespDTO;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import io.vavr.control.Option;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 下拉控件：部门，人员
 */
@Service
public class DropdownAppService {

    @Autowired
    private UserCenterGateway dubboServiceGateway;

    @Autowired
    private UserSettingSkillDomainService userSettingSkillDomainService;

    private static final String PARENT_ID = "0";

    private Function<WorkUserDropdownDeptInfoRespDTO, WorkUserDropdownDeptInfoRespDTO> transformDeptName = p -> Option.when(CharSequenceUtil.isNotEmpty(p.getCompanyName()), () -> {
        p.setDeptName(p.getCompanyName() + "-" + p.getDeptName());
        return p;
    }).getOrElse(p);

    /**
     * 部门下拉列表
     */
    public List<WorkUserDropdownDeptInfoRespDTO> deptDropdown() {

        List<ListSysDeptDubboRespDTO> deptDubboRespDTOS = dubboServiceGateway.listSysDept();
        List<WorkUserDropdownDeptInfoRespDTO> dtos = Option.when(CollUtil.isNotEmpty(deptDubboRespDTOS), () -> deptDubboRespDTOS.stream()
            .filter(dd -> CharSequenceUtil.isNotEmpty(dd.getDeptName()))
            .map(result -> WorkUserDropdownDeptInfoRespDTO.builder()
                .deptId(result.getDeptId())
                .deptName(result.getDeptName())
                .companyName(result.getCompanyName())
                .merchantId(result.getMerchantId())
                .type(result.getCompanyId())
                .parentDeptId(result.getParentDeptId())
                .build())
            .toList()).getOrElse(ArrayList::new);

        Map<String, List<WorkUserDropdownDeptInfoRespDTO>> maps = dtos.stream()
            .collect(Collectors.groupingBy(WorkUserDropdownDeptInfoRespDTO::getParentDeptId));
        dtos.forEach(dto -> dto.setChildren(maps.get(dto.getDeptId())));

        return dtos.stream().filter(t -> t.getParentDeptId().equals(PARENT_ID)).map(transformDeptName).toList();
    }

    /**
     * @param dto
     * @return
     */
    public List<WorkUserSelectDeptUserRespDTO> userDropdown(ReqWorkUserSelectDeptUserRespDTO dto) {

        return dubboServiceGateway.listUserInfo(GetUserInfoReqDTO.builder()
                .deptId(dto.getDeptId())
                .userName(dto.getUserName())
                .userIds(Sets.newHashSet(Optional.ofNullable(dto.getUserIds()).orElseGet(Lists::newArrayList)))::build)
            .stream()
            .map(r -> {
                WorkUserSelectDeptUserRespDTO respDTO = new WorkUserSelectDeptUserRespDTO();
                respDTO.setUserId(r.getUserId());
                respDTO.setUserName(r.getUserName());
                respDTO.setNickName(r.getNickName());
                respDTO.setDeptName(r.getDeptName());
                respDTO.setDeptType(String.valueOf(r.getCompanyId()));
                respDTO.setDeptId(r.getDeptId());
                respDTO.setMerchantId(r.getMerchantId());
                return respDTO;
            })
            .toList();
    }

    /**
     * Retrieves a list of games associated with a given skill ID.
     *
     * @param skillId The unique identifier of the skill for which to retrieve games.
     * @return A list of {@link GameDTO} objects representing the games associated with the given skill ID. If no games
     * are found, an empty list is returned.
     */
    public Set<GameDTO> gameDropdown(String skillId) {
        List<WorkUserSettingSkill> workUserSettingSkills = userSettingSkillDomainService.queryBySkillId(skillId);
        return Option.when(CollUtil.isNotEmpty(workUserSettingSkills), () -> {
            List<String> settingSkillIds = workUserSettingSkills.stream()
                .map(WorkUserSettingSkill::getSettingSkillId)
                .toList();
            List<WorkUserSettingSkillGameList> gameLists = userSettingSkillDomainService.queryGameListBySettingSkillId(settingSkillIds);
            return gameLists.stream()
                .map(g -> GameDTO.builder().gameId(g.getGameId()).gameName(g.getGameName()).build())
                .collect(Collectors.toSet());
        }).getOrElse(HashSet::new);
    }

}
