package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.WorkSkillTypeSelectListRespDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 技能类型 -> WorkSkillType  mapstruct
*/
@Mapper
public interface WorkSkillTypeMapping {

    WorkSkillTypeMapping INSTANCE = Mappers.getMapper(WorkSkillTypeMapping.class);

    List<WorkSkillTypeSelectListRespDTO> toWorkSkillTypeSelectListRespDTO(List<WorkSkillType> list);
}
