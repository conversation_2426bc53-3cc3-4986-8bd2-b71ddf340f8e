package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqSaveWorkSkillTemplateDTO;
import com.pxb7.mall.workshunt.app.model.WorkSkillTemplateEditReqDTO;
import com.pxb7.mall.workshunt.domain.model.workSkillTemplate.ReqSaveWorkSkillTemplateBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;



/**
* 技能模板 -> WorkSkillTemplate  mapstruct
*/
@Mapper
public interface WorkSkillTemplateMapping {

    WorkSkillTemplateMapping INSTANCE = Mappers.getMapper(WorkSkillTemplateMapping.class);

    /**
    * 技能模板 -> Save
    */
    @Mapping(target = "skills", source = "skills")
    ReqSaveWorkSkillTemplateBO toReqSaveWorkSkillTemplateBOFromDto(ReqSaveWorkSkillTemplateDTO dto);

    @Mapping(target = "skills", source = "skills")
    ReqSaveWorkSkillTemplateBO toReqSaveWorkSkillTemplateEditDto(WorkSkillTemplateEditReqDTO dto);

}
