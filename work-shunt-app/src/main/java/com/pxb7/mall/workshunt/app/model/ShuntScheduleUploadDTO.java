package com.pxb7.mall.workshunt.app.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
public class ShuntScheduleUploadDTO {

    @ExcelProperty("账号")
    @ColumnWidth(20)
    private String userName;

    @ExcelProperty("是否在线(1:在线， 0 离线)")
    @ColumnWidth(50)
    private int imStatus;

    @ExcelProperty("技能")
    @ColumnWidth(20)
    private String skillName;

    @ExcelProperty("游戏数量")
    @ColumnWidth(10)
    private int gameCount;

    @ExcelProperty("游戏列表")
    @ColumnWidth(200)
    private String gameName;


}
