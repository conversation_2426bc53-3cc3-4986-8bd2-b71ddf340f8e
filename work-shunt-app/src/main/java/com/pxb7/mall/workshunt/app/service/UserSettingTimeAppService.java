package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.workshunt.app.mapping.WorkUserSettingDayMapping;
import com.pxb7.mall.workshunt.app.mapping.WorkUserMapping;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingDayDTO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingDay.ReqSaveWorkUserSettingDayBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.WorkUserBO;
import com.pxb7.mall.workshunt.domain.service.UserSettingDayDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UserSettingTimeAppService {

    @Resource
    private UserSettingDayDomainService workUserSettingDayDomainService;

    @Autowired
    private WorkUserDomainService workUserDomainService;

    public Boolean saveWorkUserSettingDay(ReqSaveWorkUserSettingDayDTO dto) {
        ReqSaveWorkUserSettingDayBO bo = WorkUserSettingDayMapping.INSTANCE.toReqSaveWorkUserSettingDayBOFromDto(dto);

        WorkUserBO workUserBO = WorkUserMapping.INSTANCE.workUserBoTransformUserSettingTimeDTO(dto);
        WorkUser pkWorkUser = workUserDomainService.queryAllStatusByUserId(dto.getUserId());
        WorkUser workUser = Option.when(Objects.isNull(pkWorkUser), () -> workUserDomainService.save(workUserBO))
            .getOrElse(() -> workUserDomainService.update(workUserBO));

        return Option.when(!Objects.isNull(workUser), Try.of(() -> {
            workUserSettingDayDomainService.deleteUserSettingTimeTemplate(workUser.getWorkUserId(), dto.getMonth(), dto.getDay());
            workUserSettingDayDomainService.deleteUserSettingDay(workUser.getWorkUserId(), dto.getMonth(), dto.getDay());

            List<String> timeTemplateIds = dto.getTimeTemplateIds();
            if (CollUtil.isEmpty(timeTemplateIds)) {
                return true;
            }
            //save work user setting day
            bo.setWorkUserId(workUser.getWorkUserId());
            WorkUserSettingDay settingDay = workUserSettingDayDomainService.saveWorkUserSettingDay(bo);
            //save user setting day template
            workUserSettingDayDomainService.saveUserSettingDayRelationTemplate(bo, settingDay.getSettingDayId());

            return true;
        }).getOrElseThrow(e -> new BizException("", ""))).getOrElseThrow(() -> new BizException("", ""));
    }

}
