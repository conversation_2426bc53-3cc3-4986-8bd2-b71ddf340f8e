package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class ShuntRuleSaveDTO {

    @NotNull(message = "技能不能为空")
    private String skillId;

    private List<String> noOverFlowCondition;

    private List<String> overFlowCondition;

    private String noOverFlowRuleSetting;

    private String overFlowRuleSetting;

}
