package com.pxb7.mall.workshunt.app.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


@Builder
public class WorkUserDropdownDeptInfoRespDTO implements Serializable {
    /**
     * 部门类型 1博淳 2意竞 3号商
     */
    @Setter
    @Getter
    private int type;
    /**
     * 号商ID
     */
    @Setter
    @Getter
    private String merchantId;
    /**
     * 部门ID
     */
    @Setter
    @Getter
    private String deptId;
    /**
     * 父级别 部门ID
     */
    @Setter
    @Getter
    private String parentDeptId;
    /**
     * 部门名称
     */
    @Setter
    @Getter
    private String deptName;

    /**
     * 公司名称
     */
    @Setter
    @Getter
    private String companyName;

    @Setter
    @Getter
    private List<WorkUserDropdownDeptInfoRespDTO> children;

}