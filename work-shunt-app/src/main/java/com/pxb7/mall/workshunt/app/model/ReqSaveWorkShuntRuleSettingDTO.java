package com.pxb7.mall.workshunt.app.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReqSaveWorkShuntRuleSettingDTO implements Serializable {
    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 列表返回就传入 -- 分流规则ID
     */
    private String shuntRuleId;
    /**
     * 分流规则 1平均分配2比例分配0不分配
     */
    private String shuntType;
    /**
     * 分流规则类型1非溢出状态2溢出状态
     */
    private String shuntRuleType;

}