package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqSaveWorkShuntRuleSettingDTO;
import com.pxb7.mall.workshunt.domain.model.workShuntRuleSetting.ReqSaveWorkShuntRuleSettingBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 分流规则配置 -> WorkShuntRuleSetting  mapstruct
*/
@Mapper
public interface WorkShuntRuleSettingMapping {

    WorkShuntRuleSettingMapping INSTANCE = Mappers.getMapper(WorkShuntRuleSettingMapping.class);
    /**
    * 分流规则配置 -> Save
    */
    ReqSaveWorkShuntRuleSettingBO toReqSaveWorkShuntRuleSettingBOFromDto(ReqSaveWorkShuntRuleSettingDTO dto);

}
