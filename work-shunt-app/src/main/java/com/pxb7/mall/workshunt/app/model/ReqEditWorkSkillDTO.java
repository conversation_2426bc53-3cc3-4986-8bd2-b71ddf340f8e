package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqEditWorkSkillDTO implements Serializable {
    /**
     * 技能ID
     */
    @NotNull(message = "技能ID不能为空")
    private String skillId;
    /**
     * 技能名称
     */
    @NotNull(message = "技能名称不能为空")
    private String skillName ;
    /**
     * 技能code
     */
    @NotNull(message = "技能CODE不能为空")
    @Pattern(regexp = "^[a-zA-Z].*", message = "技能CODE开头以字母开头")
    private String skillCode ;
    /**
     * 技能类型ID
     */
    private String skillTypeId ;
    /**
     * 是否关联游戏1：是0否
     */
    private Boolean skillRelationGame;
    /**
     * 排序值
     */
    private int skillSort ;

    /**
     * 初开始数据
     */
    @NotNull(message = "初开始数据不能为空")
    private int earlyStart ;
    /**
     * 初结束数据
     */
    @NotNull(message = "初结束数据不能为空")
    private int earlyEnd ;
    /**
     * 中开始数据
     */
    @NotNull(message = "中开始数据不能为空")
    private int middleStart ;
    /**
     * 中结束数据
     */
    @NotNull(message = "中结束数据不能为空")
    private int middleEnd ;
    /**
     * 高开始数据
     */
    @NotNull(message = "高开始数据不能为空")
    private int highStart ;
    /**
     * 高结束数据
     */
    @NotNull(message = "高结束数据不能为空")
    private int highEnd ;
    /**
     * 专家开始数据
     */
    @NotNull(message = "专家开始数据不能为空")
    private int expertStart ;
    /**
     * 专家结束数据
     */
    @NotNull(message = "专家结束数据不能为空")
    private int expertEnd ;
}