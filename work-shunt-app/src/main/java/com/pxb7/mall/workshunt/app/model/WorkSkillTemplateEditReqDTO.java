package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
public class WorkSkillTemplateEditReqDTO {

    @NotEmpty(message = "模版ID不能为空")
    private String templateId;
    /**
     * 模版名称
     */
    @NotEmpty(message = "名称不能为空")
    private String templateTitle;

    /**
     * 是否覆盖接待上限
     */
    private Boolean isOverrideMaxLimit;


    /**
     * 设置工作技能
     */
    List<ReqWorkSkillDTO> skills;
}
