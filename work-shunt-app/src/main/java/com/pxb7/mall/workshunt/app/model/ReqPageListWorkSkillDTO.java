package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;


                                                                
@Data
public class ReqPageListWorkSkillDTO implements Serializable {

    /**
     * 第几页
     */
    private Integer pageIndex = 1;
    /**
     * 每页条数
     */
    @NotNull(message = "pageSize必填")
    @Min(value = 1, message = "pageSize不能小于1")
    @Max(value = 100, message = "pageSize不能大于100")
    private Integer pageSize = 20;

}