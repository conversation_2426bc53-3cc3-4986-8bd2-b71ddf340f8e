package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.google.common.collect.Streams;
import com.pxb7.mall.workshunt.app.model.WorkSkillLevelDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.domain.service.*;
import com.pxb7.mall.workshunt.infra.enums.ShuntAssignAlgorithmEnum;
import com.pxb7.mall.workshunt.infra.enums.ShuntRuleTypeEnum;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.ImGateway;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import io.vavr.API;
import io.vavr.Tuple;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.domain.Constants.*;
import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.WORK_SKILL_NO_CLIENT;
import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.WORK_SKILL_NO_MATCH_GAME;
import static io.vavr.API.$;
import static io.vavr.API.Case;

/**
 * 提供分流的规则校验
 */
@Service
@Slf4j
public class ShuntSupportService {

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private UserSettingDayDomainService userSettingDayDomainService;

    @Resource
    private ShuntRuleSettingDomainService shuntRuleSettingDomainService;

    @Resource
    private ShuntRuleConditionDomainService shuntRuleConditionDomainService;


    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;

    @Resource
    private ShuntAssignAlgorithmService shuntAssignAlgorithmService;

    @Resource
    private WorkUserDomainService workUserDomainService;

    @Resource
    private ImGateway imGateway;

    private static final String DATE_TIME_FORMAT = "HH:mm";



    /**
     * 根据分流规则筛选工作用户。
     *
     * @param skills      技能要求列表
     * @param workUsers   待筛选的工作用户列表
     * @param personCount 需要的人数 (NO_LIMIT 表示无限制)
     * @return 筛选后的工作用户列表
     */
    public List<WorkUser> filterWorkerByShuntRule(List<WorkSkillLevelDTO> skills,
                                                  List<WorkUser> workUsers,
                                                  int personCount) {

        final int requestCount = (personCount == NO_LIMIT) ? Integer.MAX_VALUE : personCount;

        List<WorkUser> resultUsers = queryNoOverflowCondition(skills, workUsers, personCount);

        if (resultUsers.size() >= requestCount) {
            return new ArrayList<>(resultUsers.subList(0, requestCount));
        }

        int remainCount = requestCount - resultUsers.size();

        Set<WorkUser> existingUserSet = new HashSet<>(resultUsers);

        List<WorkUser> overflowCandidates = filterWorkUserConditionShuntRuleType(skills, workUsers, OVERFLOW)
                .stream()
                .filter(user -> !existingUserSet.contains(user))
                .toList();

        List<WorkUser> assignedOverflowUsers = assignWorkUserByShuntRuleType(overflowCandidates, skills, OVERFLOW, remainCount);

        List<WorkUser> newResultUsers = new ArrayList<>(resultUsers);
        newResultUsers.addAll(assignedOverflowUsers);

        if (personCount != NO_LIMIT && newResultUsers.size() != personCount) {
            return new ArrayList<>();
        }

        return newResultUsers;
    }




    public List<WorkUser> queryNoOverflowCondition(List<WorkSkillLevelDTO> skills,
                                                   List<WorkUser> workUsers,
                                                   int personCount) {

        List<WorkUser> filterNoOverflowCondition = filterWorkUserConditionShuntRuleType(skills, workUsers, NO_OVERFLOW);
        int requestCount = personCount == NO_LIMIT ? Integer.MAX_VALUE : personCount;
        return Option.when(filterNoOverflowCondition.size() >= requestCount || personCount == NO_LIMIT,
                        () -> assignWorkUserByShuntRuleType(filterNoOverflowCondition, skills, NO_OVERFLOW, requestCount)
                ).getOrElse(() -> filterNoOverflowCondition);
    }



    /**
     * 响应处理
     *
     * @param workUsers
     * @param skills
     * @return
     */
    public List<GetWorkShuntRespDTO> shuntWorkUserResponse(List<WorkUser> workUsers, List<WorkSkill> skills) {
        return Option.of(workUsers)
            .filter(CollUtil::isNotEmpty)
            .getOrElse(ArrayList::new)
            .stream()
            .map(u -> GetWorkShuntRespDTO.builder()
                .userId(u.getUserId())
                .skillCode(Strings.join(skills.stream().map(WorkSkill::getSkillCode).toList(), ','))
                .build())
            .toList();
    }



    /**
     * 将 workskill转换成 DTO
     *
     * @param skills
     * @return
     */
    public List<WorkSkillLevelDTO> transformByWorkSkill(List<WorkSkill> skills) {
        return Option.when(CollUtil.isNotEmpty(skills), () -> skills.stream()
            .map(this::buildWorkSkillLevelDTO )
            .toList()).getOrElse(ArrayList::new);
    }



    private  WorkSkillLevelDTO buildWorkSkillLevelDTO(WorkSkill s) {
        return WorkSkillLevelDTO.builder()
                .level(Tuple.of(s.getEarlyStart(), s.getEarlyEnd(), s.getMiddleStart(), s.getMiddleEnd(), s.getHighStart(), s.getHighEnd(), s.getExpertStart(), s.getExpertEnd()))
                .skillId(s.getSkillId())
                .isRelateGame(!Objects.isNull(s.getSkillRelationGame()) && s.getSkillRelationGame())
                .skillName(s.getSkillName())
                .skillCode(s.getSkillCode())
                .build();
    }



    /**
     /**
     * 根据分流规则类型和技能，为指定数量的用户分配任务。
     *
     * @param workUsers       待分配的用户列表
     * @param skills          技能要求列表
     * @param shuntRuleType   分流规则类型
     * @param requestCount    需要分配的人数
     * @return 分配到的用户列表
     */
    public List<WorkUser> assignWorkUserByShuntRuleType(List<WorkUser> workUsers,
                                                         List<WorkSkillLevelDTO> skills,
                                                         String shuntRuleType,
                                                         int requestCount) {
        if (skills == null || skills.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> skillIds = skills.stream()
                .map(WorkSkillLevelDTO::getSkillId)
                .collect(Collectors.toList());

        List<WorkShuntRuleSetting> shuntRuleSettings = shuntRuleSettingDomainService.queryBySkillIds(skillIds, shuntRuleType);

        if (CollUtil.isNotEmpty(shuntRuleSettings)) {
            WorkShuntRuleSetting primaryRule = shuntRuleSettings.get(0);

            boolean isMultiSkillScenario = skills.size() > 1;

            return shuntAssignAlgorithmService.assignAlgorithm(skills, workUsers, primaryRule, isMultiSkillScenario, requestCount);

        } else {
            String primarySkillId = skills.get(0).getSkillId();
            return shuntAssignAlgorithmService.defaultAssignAlgorithm(workUsers, requestCount, primarySkillId);
        }
    }



    /**
     * @param skills
     * @param filterWorkerUsers
     * @param shuntRuleType     1:非溢出 2 溢出
     * @return
     */
    private List<WorkUser> filterWorkUserConditionShuntRuleType(List<WorkSkillLevelDTO> skills,
                                                                List<WorkUser> filterWorkerUsers,
                                                                String shuntRuleType) {
        List<WorkShuntRuleSettingCondition> conditions = shuntRuleConditionDomainService.queryBy(skills.stream()
            .map(WorkSkillLevelDTO::getSkillId)
            .toList(), shuntRuleType);
        return filterWorkerUsers.stream().filter(u -> filterRuleCondition(conditions, u)).toList();
    }



    /**
     * 分配人员条件
     *
     * @param conditions
     * @return
     */
    private boolean filterRuleCondition(List<WorkShuntRuleSettingCondition> conditions, WorkUser workUser) {
        List<Boolean> result = Option.when(CollUtil.isNotEmpty(conditions), () -> conditions.stream()
            .map(c ->
                switch (c.getConditionType()) {
                    case WORK_TIME_NOW -> checkWorkTime(workUser);
                    case NO_OVER_MAX_LIMIT ->
                            checkLimitMax(c.getSkillId(), workUser.getWorkUserId(), workUser.getUserId(), c.getShuntRuleType());
                    case IM_ONLINE -> checkOnline(workUser);
                    default -> false;
                }
            ).toList()).getOrElse(() -> List.of(true, true, true));
        return result.stream().allMatch(r -> r);
    }



    //检验工时
    private boolean checkWorkTime(WorkUser workUser) {
        List<WorkTimeTemplate> templates = Option.of(userSettingDayDomainService.queryTimeTemplateByWorkUserId(List.of(workUser.getWorkUserId()))).getOrElse(ArrayList::new);
        List<Boolean> check = templates.stream().map(t -> checkDateTime(t.getStartTime(), t.getEndTime())).toList();
        boolean result = check.stream().allMatch(r -> r);
        if (!result) {
            log.warn("work user checkWorkTime fail: userId:{}", workUser.getWorkUserId());
        }
        return result;
    }



    private boolean checkDateTime(@NotNull String startTime, @NotNull String endTime) {

        LocalDate now = LocalDate.now();
        LocalTime nowTime = LocalTime.now();
        LocalDateTime nowDateTime = LocalDateTime.of(now, LocalTime.of(nowTime.getHour(), nowTime.getMinute()));

        LocalTime start = LocalTime.parse(startTime, DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));
        LocalDateTime startDateTime = LocalDateTime.of(now, start);

        LocalTime end = LocalTime.parse(endTime, DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));
        LocalDateTime endDateTime = LocalDateTime.of(now, end);

        if (endDateTime.isBefore(startDateTime)) {
            endDateTime = endDateTime.plusDays(1);
        }
        return (nowDateTime.isAfter(startDateTime) || nowDateTime.isEqual(startDateTime)) && (nowDateTime.isBefore(endDateTime) || nowDateTime.isEqual(endDateTime));
    }




    /**
     * 根据排班规则类型，检查用户的技能设置是否未超过最大限制。
     * 具体逻辑如下：
     * 1. 根据 workUserId 和 skillId 查询用户的技能设置。
     * 2. 如果技能设置不存在或最大限制为0，记录日志并返回 false。
     * 3. 根据 skillId 和 shuntRuleType 查询对应的排班规则设置。
     * 4. 如果排班规则设置不存在，返回 true，表示没有限制。
     * 5. 如果排班规则类型为空，调用 checkIsOverRoomMaxLimit 检查是否超过房间最大限制。
     * 6. 根据排班规则的分配类型，分别调用不同的校验方法：
     *    - ROOM_AVERAGE 或 RATIO 类型调用 checkIsOverRoomMaxLimit。
     *    - SESSION_AVERAGE 类型调用 checkIsOverSessionLimit。
     * 7. 其他情况默认返回 true。
     *
     * @param skillId       技能ID
     * @param workUserId    工作用户ID
     * @param userId        用户ID
     * @param shuntRuleType 排班规则类型（如溢出或非溢出）
     * @return 如果未超过限制返回 true，否则返回 false
     */

    public boolean checkLimitMax(String skillId, String workUserId, String userId, String shuntRuleType) {
        WorkUserSettingSkill settingSkill = userSettingSkillDomainService.queryByWorkUserIdAndSkillId(workUserId, skillId);
        if (Objects.isNull(settingSkill)) {
            log.warn("[排班规则校验]:不存在该技能{}", skillId);
            return false;
        }


        WorkShuntRuleSetting workShuntRuleSetting = shuntRuleSettingDomainService.queryBy(skillId, shuntRuleType);

        if(Objects.isNull(workShuntRuleSetting)){
            return true;
        }

        Long maxLimit = Objects.isNull(settingSkill.getMaxLimit()) ? 0L : settingSkill.getMaxLimit();
        Long maxSessionLimit = Objects.isNull(settingSkill.getMaxSessionLimit()) ? 0L : settingSkill.getMaxSessionLimit();
        boolean result = true;

        if (CharSequenceUtil.isEmpty(workShuntRuleSetting.getShuntRuleType())) {
            result =  checkIsOverRoomMaxLimit(skillId, userId, maxLimit);
        }

        if (workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.ROOM_AVERAGE.getShuntAlgorithmType()) ||
                workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.RATIO.getShuntAlgorithmType())) {
            result =  checkIsOverRoomMaxLimit(skillId, userId, maxLimit);
        }else if(workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.SESSION_AVERAGE.getShuntAlgorithmType())){
            result =  checkIsOverSessionLimit(userId, maxSessionLimit);
        }

        if (!result) {
            log.warn("work shunt check limit max fail: {}, {}", skillId, userId);
        }

        return result;
    }



    private boolean checkIsOverRoomMaxLimit(String skillId, String userId, Long maxLimit) {
        WorkShuntOrderVolume workShuntOrderVolume = workShuntOrderVolumeRepository.queryBySkillIdAndUserId(skillId, userId);
        if(Objects.equals(maxLimit, 0L)){
            return false;
        }
        final Long max = maxLimit;
        boolean condition = Objects.nonNull(workShuntOrderVolume);
        return Option.when(condition, () -> {
            int volume = Objects.isNull(workShuntOrderVolume.getVolume()) ? 0 : workShuntOrderVolume.getVolume();
            return volume < max;
        }).getOrElse(true);
    }



    private boolean checkIsOverSessionLimit(String userId, Long maxLimit) {
        List<ImGateway.DTO> dtos = imGateway.getConsumerSessionCount(List.of(userId));
        if (CollUtil.isNotEmpty(dtos)) {
            return dtos.stream().allMatch(d -> d.getSessionCount() < maxLimit);
        }
        return true;
    }



    public boolean checkOnline(WorkUser workUser) {
        boolean result = workUser.getIsOnline();
        if (!result) {
            log.warn("work shunt check online fail:{}", workUser.getWorkUserId());
        }
        return result;
    }



    public List<WorkUserSettingSkill> filterSettingSkillLevel(List<WorkUserSettingSkill> us,
                                                              List<WorkSkillLevelDTO> skills,
                                                              int level) {

        Map<String, WorkSkillLevelDTO> map = skills.stream()
            .collect(Collectors.toMap(WorkSkillLevelDTO::getSkillId, a -> a, (k1, k2) -> k1));

        return Option.of(us.stream().filter(workUserSettingSkill -> {
            Long maxLimit = workUserSettingSkill.getMaxLimit();
            WorkSkillLevelDTO workSkillLevelDTO = map.get(workUserSettingSkill.getWorkSkillId());
            return API.Match(level)
                .of(Case($(EARLY_LEVEL), maxLimit >= workSkillLevelDTO.getLevel()._1 && maxLimit <= workSkillLevelDTO.getLevel()._2), Case($(MIDDLE_LEVEL), maxLimit >= workSkillLevelDTO.getLevel()._3 && maxLimit <= workSkillLevelDTO.getLevel()._4), Case($(HEIGHT_LEVEL), maxLimit >= workSkillLevelDTO.getLevel()._5 && maxLimit <= workSkillLevelDTO.getLevel()._6), Case($(EXPERT_LEVEL), maxLimit >= workSkillLevelDTO.getLevel()._7 && maxLimit <= workSkillLevelDTO.getLevel()._8));
        }).toList()).getOrElse(ArrayList::new);
    }



    public List<WorkUserSettingSkill> filterSettingSkillRelateGame(List<WorkUserSettingSkill> us,
                                                                   List<WorkSkillLevelDTO> skills,
                                                                   String gameId) {

        List<WorkUserSettingSkillGameList> gameLists =  userSettingSkillDomainService.queryBySkillIdsAndGameId(skills.stream().map(WorkSkillLevelDTO::getSkillId).toList(), gameId);

        Map<String, List<WorkUserSettingSkillGameList>> gameMap = gameLists.stream()
            .collect(Collectors.groupingBy(WorkUserSettingSkillGameList::getSetttingSkillId));

        Map<String, WorkSkillLevelDTO> map = skills.stream()
            .collect(Collectors.toMap(WorkSkillLevelDTO::getSkillId, a -> a, (k1, k2) -> k1));

        return Option.of(us.stream().filter(workUserSettingSkill -> {
            WorkSkillLevelDTO dto = map.get(workUserSettingSkill.getWorkSkillId());
            if (dto.isRelateGame()) {
                return Option.when(CollUtil.isNotEmpty(gameMap.get(workUserSettingSkill.getSettingSkillId())), () -> gameMap.get(workUserSettingSkill.getSettingSkillId())
                    .stream()
                    .map((WorkUserSettingSkillGameList::getGameId))
                    .toList()
                    .contains(gameId)).getOrElse(false);
            }
            return true;
        }).toList()).getOrElse(ArrayList::new);
    }


    //获取设置该技能的客服
    public List<WorkUserSettingSkill> getUserSettingSkill(List<WorkSkillLevelDTO> skills) {
        return Option.of(userSettingSkillDomainService.queryEnableSkillSettingBySkillIds(skills.stream()
                .map(WorkSkillLevelDTO::getSkillId)
                .toList()))
            .getOrElseThrow(() -> new BizException(WORK_SKILL_NO_CLIENT.getErrCode(), WORK_SKILL_NO_CLIENT.getErrDesc()));
    }


    public List<WorkUserSettingSkill> filterUserSettingRelateGame(List<WorkUserSettingSkill> filterSettingSkillByLevel,
                                                                   List<WorkSkillLevelDTO> skills,
                                                                   String gameId) {
        return Option.when(
                            CharSequenceUtil.isNotBlank(gameId),
                            () -> filterSettingSkillRelateGame(filterSettingSkillByLevel, skills, gameId)
                      )
                .getOrElse(filterSettingSkillByLevel);
    }


    public List<WorkUser> getWorkUser(List<WorkUserSettingSkill> filterSettingRelateGame, String merchantId) {
        return Option.when(
                        CollUtil.isNotEmpty(filterSettingRelateGame),
                        () -> workUserDomainService.query(filterSettingRelateGame.stream().map(WorkUserSettingSkill::getWorkUserId).toList(), merchantId)
                ).getOrElseThrow(() -> new BizException(WORK_SKILL_NO_MATCH_GAME.getErrCode(), WORK_SKILL_NO_MATCH_GAME.getErrDesc()));
    }
}
