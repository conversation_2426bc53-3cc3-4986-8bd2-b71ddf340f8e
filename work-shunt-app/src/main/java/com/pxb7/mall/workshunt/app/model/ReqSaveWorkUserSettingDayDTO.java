package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReqSaveWorkUserSettingDayDTO implements Serializable {


        /**
         * 设置技能 - 传入这个字段
         */
        private String workUserId;

        /**
         * 部门类型 1公司部门 2号商部门
         */
        private String deptType;
        /**
         * 号商ID
         */
        private String merchantId;
        /**
         * 部门ID
         */
        @NotNull(message = "部门不能为空")
        private String deptId ;
        /**
         * 用户ID
         */
        @NotNull(message = "用户不能为空")
        private String userId;


        /**
         * 月份
         */
        private String month ;
        /**
         * 天
         */
        private String day ;
        /**
         * 班次模版IDS
         */
        private List<String> timeTemplateIds;

}