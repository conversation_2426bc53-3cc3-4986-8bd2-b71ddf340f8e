package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WorkTemplateRelateUserReqDTO {
    @NotNull(message = "用户ID不能为空")
    private String userId;
    @NotNull(message = "模版ID")
    private String templateId;

    /**
     * 设置技能 - 传入这个字段
     */
    private String workUserId;

    /**
     * 部门类型 1公司部门 2号商部门
     */
    @NotNull(message = "部门类型不能为空")
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
    /**
     * 部门ID
     */
    @NotNull(message = "部门不能为空")
    private String deptId ;

}
