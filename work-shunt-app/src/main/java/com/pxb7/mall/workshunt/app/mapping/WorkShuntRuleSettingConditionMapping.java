package com.pxb7.mall.workshunt.app.mapping;

import com.pxb7.mall.workshunt.app.model.ReqSaveWorkShuntRuleSettingConditionDTO;
import com.pxb7.mall.workshunt.domain.model.workShuntRuleSettingCondition.ReqSaveWorkShuntRuleSettingConditionBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 分流规则配置参数 -> WorkShuntRuleSettingCondition  mapstruct
*/
@Mapper
public interface WorkShuntRuleSettingConditionMapping {

    WorkShuntRuleSettingConditionMapping INSTANCE = Mappers.getMapper(WorkShuntRuleSettingConditionMapping.class);

    /**
    * 分流规则配置参数 -> Save
    */
    ReqSaveWorkShuntRuleSettingConditionBO toReqSaveWorkShuntRuleSettingConditionBOFromDto(
        ReqSaveWorkShuntRuleSettingConditionDTO dto);

}
