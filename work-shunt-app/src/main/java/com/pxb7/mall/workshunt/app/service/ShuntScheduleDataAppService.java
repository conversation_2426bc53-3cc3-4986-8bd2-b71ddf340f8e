package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Functions;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.workshunt.app.model.ShuntScheduleDTO;
import com.pxb7.mall.workshunt.app.model.ShuntScheduleGameDTO;
import com.pxb7.mall.workshunt.app.model.ShuntScheduleUserDTO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShuntScheduleDataAppService {


    @Resource
    private WorkSkillDomainService workSkillDomainService;

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private WorkUserDomainService workUserDomainService;

    @Resource
    private UserCenterGateway userCenterGateway;


    public List<ShuntScheduleDTO> statisticsShunt() {

        List<UserSkillAllPO> allSkills = workSkillDomainService.all();
        List<String> allSkillIds = allSkills.stream().map(UserSkillAllPO::getSkillId).toList();
        List<WorkUserSettingSkill> allUserSettingSkill = userSettingSkillDomainService.queryEnableSkillSettingBySkillIds(allSkillIds);
        if (CollUtil.isEmpty(allSkills) || CollUtil.isEmpty(allUserSettingSkill)) {
            return new ArrayList<>();
        }


        Map<String/*skillId*/, List<WorkUserSettingSkill>> skillIdsMapUserSettingSkill = allUserSettingSkill.stream()
            .collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkSkillId));

        List<String> workUserIds = allUserSettingSkill.stream().map(WorkUserSettingSkill::getWorkUserId).toList();
        List<WorkUser> workUserList = workUserDomainService.queryByWorkUserIds(workUserIds);
        Map<String/*workUserId*/, WorkUser> workUserMap = workUserList.stream().collect(Collectors.toMap(WorkUser::getWorkUserId, Function.identity()));

        Set<String> userIds = workUserList.stream().map(WorkUser::getUserId).collect(Collectors.toSet());
        List<GetUserRoleInfoRespDTO> userRoleInfoRespDTOS = userCenterGateway.listUserInfo(()->  GetUserInfoReqDTO.builder().userIds(userIds).build());
        Map<String, GetUserRoleInfoRespDTO> userIdMapUserRoleInfo = userRoleInfoRespDTOS.stream().collect(Collectors.toMap(GetUserRoleInfoRespDTO::getUserId, Functions.identity()));

        List<String> userSettingIds = allUserSettingSkill.stream().map(WorkUserSettingSkill::getSettingSkillId).toList();
        List<WorkUserSettingSkillGameList> gameLists = userSettingSkillDomainService.queryGameListBySettingSkillId(userSettingIds);
        Map<String, List<WorkUserSettingSkillGameList>> userSettingMapGames = gameLists.stream().collect(Collectors.groupingBy(WorkUserSettingSkillGameList::getSetttingSkillId));

        return allSkills.stream().map(skill->{
            List<WorkUserSettingSkill> userRelatedSkills = skillIdsMapUserSettingSkill.get(skill.getSkillId());
            if (CollUtil.isEmpty(userRelatedSkills)) {
                return ShuntScheduleDTO.builder().skillName(skill.getSkillName()).skillRelationGame(skill.isSkillRelationGame()).build();
            }
            return  ShuntScheduleDTO.builder().skillName(skill.getSkillName()).skillRelationGame(skill.isSkillRelationGame()).skillCode(skill.getSkillCode()).users(getWorkUsers(userRelatedSkills, workUserMap, userIdMapUserRoleInfo, userSettingMapGames)).build();
        }).toList();
    }


    @NotNull
    private static List<ShuntScheduleUserDTO> getWorkUsers(List<WorkUserSettingSkill> userRelatedSkills,
                                                           Map<String, WorkUser> workUserMap,
                                                           Map<String, GetUserRoleInfoRespDTO> userIdMapUserRoleInfo,
                                                           Map<String, List<WorkUserSettingSkillGameList>> userSettingMapGames) {
        return userRelatedSkills.stream().filter(urs -> {
            WorkUser workUser = workUserMap.get(urs.getWorkUserId());
            if (Objects.isNull(workUser)) {
                return false;
            }
            return true;
        }).map(t -> {
            WorkUser wu = workUserMap.get(t.getWorkUserId());
            GetUserRoleInfoRespDTO userRoleInfoRespDTO = userIdMapUserRoleInfo.get(wu.getUserId());
            List<WorkUserSettingSkillGameList> relateGames = userSettingMapGames.get(t.getSettingSkillId());

            return ShuntScheduleUserDTO.builder()
                .userName(Objects.nonNull(userRoleInfoRespDTO) ? userRoleInfoRespDTO.getUserName() : wu.getUserId())
                .isOnline(wu.getIsOnline() ? 1 : 0)
                .deptName(Objects.nonNull(userRoleInfoRespDTO) ? userRoleInfoRespDTO.getDeptName() : "")
                .companyName(Objects.nonNull(userRoleInfoRespDTO) ? userRoleInfoRespDTO.getCompanyName() : "")
                .games(CollUtil.isNotEmpty(relateGames) ? relateGames.stream()
                    .map(g -> ShuntScheduleGameDTO.builder().gameName(g.getGameName()).build())
                    .toList() : List.of())
                .build();

        }).toList();
    }
}
