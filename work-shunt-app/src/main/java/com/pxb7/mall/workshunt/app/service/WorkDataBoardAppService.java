package com.pxb7.mall.workshunt.app.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.app.model.WorkSettingMaxLimitDTO;
import com.pxb7.mall.workshunt.client.work.databoard.ReqListDataBoardDeptWorkUserDTO;
import com.pxb7.mall.workshunt.client.work.response.dataBoard.WorkUserDataBoardPageListRespDTO;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkDataBoardDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkSkillTemplateDomainService;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import io.vavr.CheckedRunnable;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class WorkDataBoardAppService {

    @Resource
    private WorkDataBoardDomainService workDataBoardDomainService;

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Resource
    private WorkSkillTemplateDomainService workSkillTemplateDomainService;

    @Resource
    private WorkUserDomainService workUserDomainService;


    public Page<WorkUserDataBoardPageListRespDTO> listWorkUser(ReqListDataBoardDeptWorkUserDTO dto) {
        return workDataBoardDomainService.listUserWorkUser(dto);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateUserSessionMaxLimit(WorkSettingMaxLimitDTO dto) {

        CheckedRunnable update = ()-> {
            WorkUser workUser = workUserDomainService.queryAllStatusByUserId(dto.getUserId());
            if (Objects.isNull(workUser)) {
                throw new BizException("shunt002", "分流客服不存在");
            }
            if (CharSequenceUtil.isNotEmpty(workUser.getTemplateId())) {
                WorkSkillTemplate workSkillTemplate = workSkillTemplateDomainService.getTableOneDataByParamKey(workUser.getTemplateId());
                Optional.ofNullable(workSkillTemplate).ifPresent(template -> {
                    if (Objects.nonNull(template.getOverrideMaxLimit()) && Objects.equals(template.getOverrideMaxLimit(), true)) {
                        throw new BizException("shunt002", "技能绑定模版,且不允许修改");
                    }
                });
            }
            userSettingSkillDomainService.updateSettingSkillMaxLimit(dto.getSettingSkillId(), dto.getType(), dto.getMaxLimit());
        };

        Try.run(update).onFailure(t-> {
            log.error("update work user session max limit error: {}", t.getMessage());
            throw new BizException("shunt002", "修改分流客服会话并发上限失败");
        });
    }
}
