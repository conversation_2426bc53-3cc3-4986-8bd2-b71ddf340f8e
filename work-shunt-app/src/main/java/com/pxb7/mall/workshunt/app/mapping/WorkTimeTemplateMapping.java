package com.pxb7.mall.workshunt.app.mapping;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.domain.model.time.TimeTemplatePageRespBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqEditWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqPageListWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqSaveWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工作时间模版 -> WorkTimeTemplate  mapstruct
*/
@Mapper
public interface WorkTimeTemplateMapping {

    WorkTimeTemplateMapping INSTANCE = Mappers.getMapper(WorkTimeTemplateMapping.class);

    /**
    * 工作时间模版 -> PageList
    */
    ReqPageListWorkTimeTemplateBO toReqPageListWorkTimeTemplateBOFromDto(ReqPageListWorkTimeTemplateDTO dto);
    /**
    * 工作时间模版 -> Save
    */
    ReqSaveWorkTimeTemplateBO toReqSaveWorkTimeTemplateBOFromDto(WorkTimeTemplateSaveReqDTO dto);
    /**
    * 工作时间模版 -> Edit
    */
    ReqEditWorkTimeTemplateBO toReqEditWorkTimeTemplateBOFromDto(ReqEditWorkTimeTemplateDTO dto);

    Page<TimeTemplatePageListRespDTO> toPageListDTOByBo(Page<TimeTemplatePageRespBO> page);

    List<TimeTemplateDropdownDTO> toDropdownDTO(List<WorkTimeTemplate> timeTemplateList);

}
