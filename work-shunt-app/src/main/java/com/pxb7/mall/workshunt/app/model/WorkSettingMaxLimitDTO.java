package com.pxb7.mall.workshunt.app.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>功能描述:</p>
 * 作者：rookie
 * 创建日期：2025/07/30
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */

@Data
public class WorkSettingMaxLimitDTO implements Serializable {

    /**
     * 1:房间接待量
     * 2. 并发会话接待量
     */

    @NotNull(message = "设置类型不能为空")
    private int type;

    @NotNull(message = "设置接待上限能为空")
    @Min(value = 1, message = "不能小于1")
    @Max(value = 9999999999L, message = "不能大于9999999999")
    private Long maxLimit;

    @NotNull(message = "用户ID不能为空")
    private String userId;

    @NotNull(message = "设置技能ID不能为空")
    private String settingSkillId;

}
