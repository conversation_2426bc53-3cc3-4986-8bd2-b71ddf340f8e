package com.pxb7.mall.workshunt.domain.model.workShuntLog;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReqPageListWorkShuntLogBO implements Serializable {

    /**
     * 第几页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 请求类型：枚举 参考类
     */
    private String shuntType;
    /**
     * 请求技能ID
     */
    private String workSkillId;
    /**
     * 请求游戏ID
     */
    private String workGameId;
    /**
     * 请求时间
     */
    private String workStartTime;
    /**
     * 请求时间
     */
    private String workEndTime;
    /**
     * 返回数据
     */
    private String workOutData;
}