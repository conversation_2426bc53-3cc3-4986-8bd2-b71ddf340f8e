package com.pxb7.mall.workshunt.domain.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkUserPageListRespDTO implements Serializable {
    /**
     *
     * 设置日期的时候 - 传入这个字段
     */
    private String workUserId;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 部门类型1公司部门2号商部门
     */
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
    /**
     * 查看技能 -- 大于0 就显示查看技能  0->显示设置技能
     */
    private int countSkill;

    /**
     * 技能模版ID
     */
    private String templateId;

    /**
     * 技能模版名称
     */
    private String templateName;

    /**
     * 模版是否覆盖客服接待上限
     */
    private Boolean templateOverrideMaxLimit;


    /**
     * 每天设置（时间模版）
     */
    private List<UserDaySettingListDTO> listDay;
}