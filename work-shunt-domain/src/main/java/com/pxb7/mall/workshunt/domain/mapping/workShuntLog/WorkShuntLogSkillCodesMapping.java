package com.pxb7.mall.workshunt.domain.mapping.workShuntLog;

import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogSkillCodes;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 请求日志记录 -> WorkShuntLog  mapstruct
 * work_shunt_log_skill_codes
*/
@Mapper
public interface WorkShuntLogSkillCodesMapping {

    WorkShuntLogSkillCodesMapping INSTANCE = Mappers.getMapper(WorkShuntLogSkillCodesMapping.class);

    List<WorkShuntLogSkillCodes> toWorkShuntLogSkillCodes(List<WorkSkill> listSkill);

}
