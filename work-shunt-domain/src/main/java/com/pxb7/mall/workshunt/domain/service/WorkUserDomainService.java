package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.DayListDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.domain.mapping.workUser.WorkUserMapping;
import com.pxb7.mall.workshunt.domain.model.UserDaySettingListDTO;
import com.pxb7.mall.workshunt.domain.model.UserModifyDepartmentDTO;
import com.pxb7.mall.workshunt.domain.model.WorkUserPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workUser.ReqListWorkUserBO;
import com.pxb7.mall.workshunt.domain.model.workUser.WorkSkillCodeGroupShuntBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.WorkUserBO;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkSkillCodeGroupShuntPO;
import com.pxb7.mall.workshunt.infra.repository.db.*;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkUserDomainService {

    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private WorkUserSettingDayRepository workUserSettingDayRepository;

    @Resource
    private WorkTimeTemplateRepository workTimeTemplateRepository;

    @Resource
    private UserSettingSkillRepository userSettingSkillRepository;

    @Resource
    private WorkSkillTemplateRepository workSkillTemplateRepository;

    @Resource
    private WorkUserSettingDayRelationWorkTemplatRepository workUserSettingDayRelationWorkTemplatRepository;

    @Resource
    private UserCenterGateway userCenterGateway;

    public WorkUser save(WorkUserBO bo) {
        WorkUser workUser = WorkUserMapping.INSTANCE.toWorkUserFromBO(bo);
        String deptType = Option.when(CharSequenceUtil.isNotBlank(bo.getDeptType()) && (bo.getDeptType().equals("1") || bo.getDeptType().equals("2")), ()-> "1").getOrElse(()->"2");
        workUser.setWorkUserId(IdGenUtil.getId());
        workUser.setDeptType(deptType);
        workUser.setIsActive(bo.getIsActive());
        workUser.setImStatus(bo.getImStatus());
        workUser.setCreateTime(LocalDateTime.now());
        workUser.setMerchantId(bo.getMerchantId());
        workUserRepository.save(workUser);
        return workUser;
    }

    public WorkUser update(WorkUserBO bo){
        WorkUser workUser = WorkUserMapping.INSTANCE.toWorkUserFromBO(bo);
        String deptType = Option.when(CharSequenceUtil.isNotBlank(bo.getDeptType()) && (bo.getDeptType().equals("1") || bo.getDeptType().equals("2")), ()-> "1").getOrElse(()->"2");
        workUser.setDeptType(deptType);
        workUser.setCreateTime(LocalDateTime.now());
        workUser.setMerchantId(bo.getMerchantId());
        workUser.setIsActive(bo.getIsActive());
        workUser.setImStatus(bo.getImStatus());
        LambdaQueryWrapper<WorkUser> query = new LambdaQueryWrapper<>();
        query.eq(WorkUser::getWorkUserId, bo.getWorkUserId());

        workUserRepository.update(workUser, query);
        return workUser;
    }


    public WorkUser queryByUserId(String userId){
        return workUserRepository.getTableOneDataByPkUserId(userId);
    }


    public WorkUser queryAllStatusByUserId(String userId) {
        return workUserRepository.queryAllStatusWorkUserByUserId(userId);
    }


    public List<WorkUser> queryByMerchantId(String merchantId){
        return Option.of(workUserRepository.getWorkUserByMerchantId(merchantId)).getOrElse( ArrayList::new);
    }



    public List<WorkUser> query(List<String> workerIds, String merchantId){
        return Option.of(workUserRepository.query(workerIds, merchantId)).getOrElse(ArrayList::new);
    }


    public List<WorkUser> queryByUserIds(List<String> userIds){
        return workUserRepository.queryWorkUsers(userIds);
    }


    public List<WorkUser> queryByWorkUserIds(List<String> workUserIds){
        return workUserRepository.queryByWorkUserIds(workUserIds);
    }


    public List<WorkUserDeptUserRespDTO> getSkillWorkUserByCodeAndGameId(WorkSkillCodeGroupShuntBO bo) {
        List<WorkUserDeptUserRespDTO> list = new ArrayList<>();
        WorkSkillCodeGroupShuntPO workSkillCodeGroupShuntPO = WorkUserMapping.INSTANCE.toWorkSkillCodeGroupShuntPO(bo);
        List<WorkUser> workUserList = workUserRepository.workSkillCodeGroupShunt(workSkillCodeGroupShuntPO);
        if (workUserList == null || workUserList.isEmpty()){
            return list;
        }


        List<WorkUserDeptUserRespDTO> listWorkUserSelectDeptUserRespDTO =  workUserList.stream().map(wu->{
            WorkUserDeptUserRespDTO respDTO = WorkUserMapping.INSTANCE.toWorkUserDeptUserRespDTOOne(wu);
            respDTO.setImOnLineState(wu.getImStatus());
            return respDTO;
        }).toList();

        Set<String> userIds = listWorkUserSelectDeptUserRespDTO.stream().map(WorkUserDeptUserRespDTO::getUserId).collect(Collectors.toSet());
        GetUserInfoReqDTO userInfoReqDTO = GetUserInfoReqDTO.builder()
            .userIds(userIds)
            .build();

        List<GetUserRoleInfoRespDTO> dataRespDTO = userCenterGateway.listUserInfo(() -> userInfoReqDTO);
        Map<String, GetUserRoleInfoRespDTO> userRoleInfoRespDTOMap = dataRespDTO.stream().collect(Collectors.toMap(GetUserRoleInfoRespDTO::getUserId, Function.identity()));

        listWorkUserSelectDeptUserRespDTO.forEach(item ->{
            GetUserRoleInfoRespDTO userRoleInfoRespDTO = userRoleInfoRespDTOMap.get(item.getUserId());
            if (!Objects.isNull(userRoleInfoRespDTO)) {
                item.setDeptName(userRoleInfoRespDTO.getDeptName());
                item.setUserName(userRoleInfoRespDTO.getUserName());
                item.setUserTelPhone(userRoleInfoRespDTO.getPhone());
            }

        });
        return listWorkUserSelectDeptUserRespDTO;
    }



    private int lengthMonthDays(String workMonth){
        return Try.of(()->{
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            var temporalAccessor = formatter.parse(workMonth);
            // 使用解析结果创建 LocalDate，设置为该月的第一天
            LocalDate date = LocalDate.of(
                temporalAccessor.get(java.time.temporal.ChronoField.YEAR),
                temporalAccessor.get(java.time.temporal.ChronoField.MONTH_OF_YEAR),
                1
            );

            YearMonth yearMonth = YearMonth.of(date.getYear(), date.getMonth());

            return yearMonth.lengthOfMonth();
        }).getOrElse(0);
    }



    public List<WorkUserPageListRespDTO>  listUserWorkUser(ReqListWorkUserBO dto, List<WorkUserSelectDeptUserRespDTO> listDeptUser){

        if (CollUtil.isEmpty(listDeptUser)) {
            return new ArrayList<>();
        }

        List<WorkUserPageListRespDTO> workUserPageListRespDTOS =  WorkUserMapping.INSTANCE.toWorkUserPageListRespDTO(listDeptUser);

        List<String> userIds = workUserPageListRespDTOS.stream().map(WorkUserPageListRespDTO::getUserId).toList();

        List<WorkUser> workUsers = workUserRepository.queryByUserIds(userIds);
        Map<String, WorkUser> workUserMap = translateWorkUserToMap(workUsers);

        Map<String, Long> workUserRelateWorkSettingCountMap = getWorkUserRelateWorkSettingCount(workUsers);

        Map<String, List<WorkUserSettingDay>> workUserSettingDayMap = queryWorkUserSettingDayByMonthUserIds(dto.getMonth(), workUsers, days(dto.getMonth()));

        Map<String, WorkSkillTemplate> templateMap = queryWorkSkillTemplate(workUsers);

        workUserPageListRespDTOS.forEach(workUser->{

            WorkUser user = workUserMap.getOrDefault(workUser.getUserId(), null);

            workUser.setWorkUserId(Option.when(Objects.nonNull(user), ()-> user.getWorkUserId()).getOrElse(Strings.EMPTY));
            workUser.setCountSkill(Option.when(Objects.nonNull(user), ()-> workUserRelateWorkSettingCountMap.getOrDefault(user.getWorkUserId(), 0L).intValue()).getOrElse(0));
            workUser.setTemplateId(Option.when(Objects.nonNull(user), ()-> user.getTemplateId()).getOrElse(Strings.EMPTY));

            workUser.setTemplateName(templateMap.getOrDefault(workUser.getTemplateId(), new WorkSkillTemplate()).getTemplateTitle());
            workUser.setTemplateOverrideMaxLimit(templateMap.getOrDefault(workUser.getTemplateId(), new WorkSkillTemplate()).getOverrideMaxLimit());

            workUser.setListDay(initWorkUserDaySetting(user, dto.getMonth(), workUserSettingDayMap));
        });

        return workUserPageListRespDTOS;
    }


    private Map<String, WorkUser> translateWorkUserToMap(List<WorkUser> workUsers) {
        if(CollUtil.isEmpty(workUsers)) {
            return new HashMap<>();
        }
        return workUsers.stream().collect(Collectors.toMap(WorkUser::getUserId, Function.identity()));
    }


    private Map<String, Long> getWorkUserRelateWorkSettingCount(List<WorkUser> workUsers) {
        if(CollUtil.isEmpty(workUsers)) {
            return new HashMap<>();
        }
        List<String> workUserIds = workUsers.stream().map(WorkUser::getWorkUserId).toList();
        return userSettingSkillRepository.totalWorkUserRelateSkillCount(workUserIds);
    }


    private List<UserDaySettingListDTO> initWorkUserDaySetting(WorkUser user, String month, Map<String, List<WorkUserSettingDay>> maps) {

        List<UserDaySettingListDTO> listDay = new ArrayList<>();
        for(int i = 1; i <= lengthMonthDays(month);i++){
            UserDaySettingListDTO daySettingListDTO = new UserDaySettingListDTO();
            WorkUserSettingDay settingDay = null;

            daySettingListDTO.setDay(transDayToStr(i));
            daySettingListDTO.setMonth(month);
            listDay.add(daySettingListDTO);

            if (Objects.isNull(user)) {
                continue;
            }

            settingDay = dayToMap(maps.getOrDefault(user.getWorkUserId(), new ArrayList<>())).getOrDefault(transDayToStr(i), null);

            if(!Objects.isNull(settingDay)){
                daySettingListDTO.setDay(settingDay.getDay());
                daySettingListDTO.setMonth(settingDay.getMonth());
                daySettingListDTO.setSettingDayId(settingDay.getSettingDayId());

                List<WorkUserSettingDayRelationWorkTemplat> workUserSettingDayRelationWorkTemplats = workUserSettingDayRelationWorkTemplatRepository.listWorkUserSettingDayRelationWorkTemplatBySettingDdyId(settingDay.getSettingDayId());

                daySettingListDTO.setData(Option.when(CollUtil.isNotEmpty(workUserSettingDayRelationWorkTemplats),()->
                    workUserSettingDayRelationWorkTemplats.stream().map(t -> {
                        WorkTimeTemplate workTimeTemplate = workTimeTemplateRepository.getTableOneDataByParamKey(t.getWorkTemplateId());
                        DayListDTO dayListDTO = new DayListDTO();
                        dayListDTO.setTimeTemplateId(workTimeTemplate.getTimeTemplateId());
                        dayListDTO.setTimeTemplateName(workTimeTemplate.getTitle());
                        return dayListDTO;
                    }).toList()
                ).getOrElse(ArrayList::new));
            }
        }

        return listDay;
    }


    private String transDayToStr(int day){
        return day < 10 ? "0" + day : "" + day;
    }

    public Boolean relateTemplate(String workUserId, String templateId){
        return Try.of(()-> {workUserRepository.relateTemplate(workUserId, templateId); return true;}).getOrElse(false);
    }

    public List<WorkUser> queryWorkersByTemplateId(String templateId){
        return workUserRepository.queryByTemplateId(templateId);
    }

    private List<String> days(String month){
        List<String> days = new ArrayList<>();
        for(int i = 1; i <= lengthMonthDays(month);i++){
            days.add(transDayToStr(i));
        }
        return days;
    }

    private Map<String, List<WorkUserSettingDay>> queryWorkUserSettingDayByMonthUserIds(String month, List<WorkUser> workUser, List<String> days) {
        if (CollUtil.isEmpty(workUser)) {
            return new HashMap<>();
        }
        List<String> workUserIds = workUser.stream().map(WorkUser::getWorkUserId).toList();
        List<WorkUserSettingDay> workUserSettingDays = workUserSettingDayRepository.query(workUserIds, month, days);
        return CollUtil.isEmpty(workUserSettingDays) ? new HashMap<>() : workUserSettingDays.stream().collect(Collectors.groupingBy(WorkUserSettingDay::getWorkUserId));
    }


    private Map<String, WorkUserSettingDay> dayToMap(List<WorkUserSettingDay> workUserSettingDays){
        if(CollUtil.isEmpty(workUserSettingDays)) {
            return new HashMap<>();
        }
        return workUserSettingDays.stream().collect(Collectors.toMap(WorkUserSettingDay::getDay, Function.identity()));
    }

    private Map<String, WorkSkillTemplate> queryWorkSkillTemplate(List<WorkUser> workUsers) {
        if(CollUtil.isEmpty(workUsers)) {
            return new HashMap<>();
        }
        List<String> templateIds = workUsers.stream().map(WorkUser::getTemplateId).toList();
        List<WorkSkillTemplate> workSkillTemplates = workSkillTemplateRepository.queryByTemplateIds(templateIds);
        return CollUtil.isEmpty(workSkillTemplates) ? new HashMap<>() : workSkillTemplates.stream().collect(Collectors.toMap(WorkSkillTemplate::getSkillTemplateId, Function.identity()));
    }


    public void modifyWorkUserDepartment(UserModifyDepartmentDTO userModifyDepartmentDTO) {
        String userId = userModifyDepartmentDTO.getSysUserId();
        String deptId = userModifyDepartmentDTO.getDeptId();
        String merchantId = userModifyDepartmentDTO.getMerchantId();
        workUserRepository.updateWorkUserDepartment(userId, deptId, merchantId);
    }


}
