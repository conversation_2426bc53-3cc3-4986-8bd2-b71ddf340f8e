package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkUserSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.domain.mapping.workSkillTemplate.WorkSkillTemplateMapping;
import com.pxb7.mall.workshunt.domain.model.workSkillTemplate.ReqSaveWorkSkillTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqWorkSkillBO;
import com.pxb7.mall.workshunt.infra.model.skill.GamePO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillTemplateRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillTemplateSettingGameListRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillTemplateSettingRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSetting;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSettingGameList;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkSkillTemplateDomainService {

    @Resource
    private WorkSkillTemplateRepository workSkillTemplateRepository;
    @Resource
    private WorkSkillTemplateSettingRepository workSkillTemplateSettingRepository;
    @Resource
    private WorkSkillTemplateSettingGameListRepository workSkillTemplateSettingGameListRepository;
    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private UserCenterGateway userCenterGateway;

    public List<WorkSkillTemplateSelectListRespDTO> dropDownList() {
        List<WorkSkillTemplate> list = Option.of(workSkillTemplateRepository.allDataOrderByCreateDate())
            .getOrElse(ArrayList::new);
        list.forEach(peek-> {
                    if (peek.getOverrideMaxLimit() == null) {
                        peek.setOverrideMaxLimit(false);
                    }
                });
        return WorkSkillTemplateMapping.INSTANCE.toWorkSkillTemplateSelectListRespDTO(list);
    }

    public List<WorkUserSkillTemplateSelectListRespDTO> selectUserList(String userId) {
        List<WorkUser> list = workUserRepository.queryWorkUsersExcludeSelfUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<WorkUserSkillTemplateSelectListRespDTO> workUserSkillTemplateSelectListRespDTOS = WorkSkillTemplateMapping.INSTANCE.toWorkUserSkillTemplateSelectListRespDTO(list);
        Set<String> userIds = workUserSkillTemplateSelectListRespDTOS.stream().map(WorkUserSkillTemplateSelectListRespDTO::getUserId).collect(Collectors.toSet());

        GetUserInfoReqDTO userInfoReqDTO = GetUserInfoReqDTO.builder().userIds(userIds).build();
        List<GetUserRoleInfoRespDTO> dataRespDTO = userCenterGateway.listUserInfo(() -> userInfoReqDTO);

        if (CollUtil.isNotEmpty(dataRespDTO)) {
            Map<String,GetUserRoleInfoRespDTO> maps =  dataRespDTO.stream().collect(Collectors.toMap(GetUserRoleInfoRespDTO::getUserId, Function.identity()));
            workUserSkillTemplateSelectListRespDTOS.forEach(item -> {
                String name = Objects.nonNull(maps.get(item.getUserId())) ? maps.get(item.getUserId()).getUserName() : item.getUserId();
                item.setUserName(name);
            });
        }

        return workUserSkillTemplateSelectListRespDTOS;

    }

    public List<WorkSkillTemplateSetting> queryByTemplateSettingByTemplateId(String templateId) {
        return workSkillTemplateSettingRepository.querySkillTemplateSettingByTemplateId(templateId);
    }

    public void doSaveTemplateSettingData(WorkSkillTemplate template, ReqSaveWorkSkillTemplateBO dto) {
        List<ReqWorkSkillBO> reqWorkSkillDTOList = Option.of(dto.getSkills()).getOrElse(ArrayList::new);
        reqWorkSkillDTOList.forEach(item -> {
            WorkSkillTemplateSetting skillTemplateSetting = saveSkillTemplateSettingData(template.getSkillTemplateId(), item);
            List<GamePO> gameDTOList = Option.of(item.getGames()).getOrElse(ArrayList::new);
            saveSkillTemplateSettingDataGame(skillTemplateSetting.getTemplateSettingId(), gameDTOList);
        });
    }

    private WorkSkillTemplateSetting saveSkillTemplateSettingData(String templateId, ReqWorkSkillBO dto) {
        WorkSkillTemplateSetting workSkillTemplateSetting = new WorkSkillTemplateSetting();
        workSkillTemplateSetting.setTemplateSettingId(IdGenUtil.getId().toString());
        workSkillTemplateSetting.setWorkSkillId(dto.getSkillId());
        workSkillTemplateSetting.setCreateTime(LocalDateTime.now());
        workSkillTemplateSetting.setMaxLimit(dto.getMaxLimit());
        workSkillTemplateSetting.setMaxSessionLimit(dto.getMaxSessionLimit());
        workSkillTemplateSetting.setAble(dto.isAble());
        workSkillTemplateSetting.setSkillTemplateId(templateId);
        workSkillTemplateSettingRepository.save(workSkillTemplateSetting);

        return workSkillTemplateSetting;
    }

    private void saveSkillTemplateSettingDataGame(String id, List<GamePO> gameDTOList) {
        List<WorkSkillTemplateSettingGameList> listGame = new ArrayList<>();
        if (CollUtil.isNotEmpty(gameDTOList)) {
            gameDTOList.forEach(it -> {
                WorkSkillTemplateSettingGameList game = new WorkSkillTemplateSettingGameList();
                game.setGameId(it.getGameId());
                game.setGameName(it.getGameName());
                game.setTemplateSettingId(id);
                game.setCreateTime(LocalDateTime.now());
                listGame.add(game);
            });
            workSkillTemplateSettingGameListRepository.saveBatch(listGame);
        }
    }

    public WorkSkillTemplate saveTemplate(ReqSaveWorkSkillTemplateBO dto) {

        WorkSkillTemplate template = new WorkSkillTemplate();
        template.setSkillTemplateId(IdGenUtil.getId());
        template.setTemplateTitle(dto.getTemplateTitle());
        template.setOverrideMaxLimit(dto.getIsOverrideMaxLimit());
        template.setCreateTime(LocalDateTime.now());
        workSkillTemplateRepository.save(template);
        return template;
    }

    public List<WorkSkillTemplateSettingGameList> queryByTemplateSettingId(String queryByTemplateSettingId) {
        return workSkillTemplateSettingGameListRepository.queryByTemplateSettingId(queryByTemplateSettingId);
    }


    public void deleteByTemplateId(String templateId){
        List<WorkSkillTemplateSetting> settings =  workSkillTemplateSettingRepository.querySkillTemplateSettingByTemplateId(templateId);
        Option.when(CollUtil.isNotEmpty(settings), () -> {
            workSkillTemplateSettingGameListRepository.deleteByTemplateSettingId(settings.stream()
                .map(WorkSkillTemplateSetting::getTemplateSettingId)
                .toList());
            workSkillTemplateSettingRepository.deleteByTemplateId(templateId);
            return true;
        }).getOrElse(false);

        workSkillTemplateRepository.deleteByTemplateId(templateId);
    }



    public WorkSkillTemplate getTableOneDataByParamKey(String skillTemplateId) {
        return workSkillTemplateRepository.getTableOneDataByParamKey(skillTemplateId);
    }
}
