package com.pxb7.mall.workshunt.domain.model;

import com.pxb7.mall.workshunt.client.work.response.workUser.DayListDTO;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

@Data
@Getter
public class UserDaySettingListDTO implements Serializable{


    /**
     * 月份天的ID
     */
    private String settingDayId;

    /**
     * 月份
     */
    private String month;
    /**
     * 天
     */
    private String day;
    /***
     * 设置的每天排班数据
     */
    private List<DayListDTO> data;


}
