package com.pxb7.mall.workshunt.domain.model.workSkill;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ReqEditWorkSkillBO {

    /**
     * 技能ID
     */
    private String skillId ;
    /**
     * 技能名称
     */
    private String skillName ;
    /**
     * 技能code
     */
    private String skillCode ;
    /**
     * 技能类型ID
     */
    private String skillTypeId ;
    /**
     * 是否关联游戏1：是0否
     */
    private String isSkillRelationName ;
    /**
     * 排序值
     */
    private Long skillSort ;
    /**
     * 创建时间
     */
    private LocalDateTime createTime ;
    /**
     * 初开始数据
     */
    private Long earlyStart ;
    /**
     * 初结束数据
     */
    private Long earlyEnd ;
    /**
     * 中开始数据
     */
    private Long middleStart ;
    /**
     * 中结束数据
     */
    private Long middleEnd ;
    /**
     * 高开始数据
     */
    private Long highStart ;
    /**
     * 高结束数据
     */
    private Long highEnd ;
    /**
     * 专家开始数据
     */
    private Long expertStart ;
    /**
     * 专家结束数据
     */
    private Long expertEnd ;




}
