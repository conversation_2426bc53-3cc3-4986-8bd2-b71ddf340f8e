package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.exception.BizException;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.product.client.api.admin.GameServiceI;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.WorkShuntLogPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.domain.mapping.workShuntLog.WorkShuntLogMapping;
import com.pxb7.mall.workshunt.domain.mapping.workShuntLog.WorkShuntLogSkillCodesMapping;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqAddGroupWorkShuntBO;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqGroupTransferShuntShuntBO;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqMobileVerifyLogIdBo;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqPageListWorkShuntLogBO;
import com.pxb7.mall.workshunt.infra.enums.ReqTypeEnums;
import com.pxb7.mall.workshunt.infra.model.workShuntLog.WorkShuntLogPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.*;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.*;


@Slf4j
@Service
@DS("mysql")
public class ShuntLogDomainService {

    @Resource
    private WorkShuntLogRepository workShuntLogRepository;

    @Resource
    private WorkShuntLogSkillCodesRepository workShuntLogSkillCodesRepository;
    @Resource
    private UserSettingSkillRepository workUserSettingSkillRepository;

    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private WorkSkillRepository workSkillRepository;

    @DubboReference
    private GameServiceI gameServiceI;
    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;

    @Resource
    private UserCenterGateway userCenterGateway;

    public Page<WorkShuntLogPageListRespDTO> listPageWorkShuntLog(ReqPageListWorkShuntLogBO dto) {
        WorkShuntLogPageListPO workShuntLogPageListPo = WorkShuntLogMapping.INSTANCE.toWorkShuntLogPageListPo(dto);
        Page<WorkShuntLog> pageList = workShuntLogRepository.pages(workShuntLogPageListPo);
        Page<WorkShuntLogPageListRespDTO> outPage = WorkShuntLogMapping.INSTANCE.toWorkShuntLogPageListRespDTO(pageList);
        List<WorkShuntLog> inRecords = pageList.getRecords();
        List<WorkShuntLogPageListRespDTO> outRecords = inRecords.stream().map(this::initLog).toList();
        if (!CollectionUtils.isEmpty(outRecords)) {
            //设置游戏名称
            setGameName(outRecords);
            //设置技能名称
            setSkillName(outRecords);
        }
        outPage.setRecords(outRecords);
        return outPage;
    }

    /**
     * 设置技能名称
     * @param outRecords
     */
    private void setSkillName(List<WorkShuntLogPageListRespDTO> outRecords) {
        List<String> shuntLogIds = outRecords.stream()
                .map(WorkShuntLogPageListRespDTO::getShuntLogId)
                .distinct().toList();
        List<WorkShuntLogSkillCodes> listWorkShuntLogSkillCodesByShuntLogId = workShuntLogSkillCodesRepository.getListWorkShuntLogSkillCodesByShuntLogIds(shuntLogIds);
        if (CollectionUtils.isEmpty(listWorkShuntLogSkillCodesByShuntLogId)) {
            return;
        }

        Map<String, List<WorkShuntLogSkillCodes>> skillMap = listWorkShuntLogSkillCodesByShuntLogId.stream()
                .collect(Collectors.groupingBy(WorkShuntLogSkillCodes::getShuntLogId));
        outRecords.forEach(it -> {
            List<WorkShuntLogSkillCodes> workShuntLogSkillCodes = skillMap.get(it.getShuntLogId());
            if (!CollectionUtils.isEmpty(workShuntLogSkillCodes)) {
                String skillNames = workShuntLogSkillCodes.stream()
                        .map(WorkShuntLogSkillCodes::getSkillName)
                        .collect(Collectors.joining(","));
                it.setWorkSkillName(skillNames);
            }
        });
    }

    /**
     * 设置游戏名称
     * @param outRecords
     */
    private void setGameName(List<WorkShuntLogPageListRespDTO> outRecords) {
        List<String> gameIds = outRecords.stream()
                .map(WorkShuntLogPageListRespDTO::getWorkGameId)
                .distinct().toList();
        MultiResponse<GameBaseDTO> gameBaseDTOMultiResponse = gameServiceI.selectGameBaseInfo(gameIds);
        if (gameBaseDTOMultiResponse.isSuccess()
                && !CollectionUtils.isEmpty(gameBaseDTOMultiResponse.getData())) {
            Map<String, String> gameMap =
                    gameBaseDTOMultiResponse.getData().stream()
                            .collect(Collectors.toMap(GameBaseDTO::getGameId, GameBaseDTO::getGameName, (t1, t2) -> t1));
            outRecords.forEach(it -> it.setWorkGameName(gameMap.get(it.getWorkGameId())));
        }
    }

    private WorkShuntLogPageListRespDTO initLog(WorkShuntLog log){
        WorkShuntLogPageListRespDTO workShuntLog = new WorkShuntLogPageListRespDTO();
        workShuntLog.setShuntLogId(log.getShuntLogId());
        workShuntLog.setId(log.getId());
        workShuntLog.setWorkGameId(log.getWorkGameId());
        workShuntLog.setWorkGameName(log.getWorkGameName());
        workShuntLog.setWorkTime(log.getWorkTime());
        workShuntLog.setWorkOutData(log.getPkUserNames());
        workShuntLog.setShuntType(log.getShuntType());
        workShuntLog.setWorkUserNum(log.getWorkUserNum());
        workShuntLog.setWorkTime(log.getWorkTime());
        workShuntLog.setWorkOutTime(log.getWorkOutTime());
        return workShuntLog;
    }

    public void saveShuntLog(List<WorkSkill> skills, ReqGetWorkShuntDTO bo, List<WorkUser> workers, LocalDateTime workDate) {
        //设置主体
        WorkShuntLog workShuntLog = new WorkShuntLog();
        workShuntLog.setShuntLogId(IdGenUtil.getId());
        workShuntLog.setShuntType(bo.getDomain());
        workShuntLog.setWorkTime(workDate);
        workShuntLog.setWorkGameId(bo.getGameId());
        workShuntLog.setWorkGameName(bo.getGameName());
        workShuntLog.setWorkUserNum(bo.getPersonCount());
        workShuntLog.setCreateTime(LocalDateTime.now());
        workShuntLog.setWorkOutTime(LocalDateTime.now());
        workShuntLog.setReqType(ReqTypeEnums.reqType1.getType().toString());

        initUserName(workers, workShuntLog);

        workShuntLogRepository.save(workShuntLog);

        //设置技能
        List<WorkShuntLogSkillCodes> workShuntLogSkillCodes = WorkShuntLogSkillCodesMapping.INSTANCE.toWorkShuntLogSkillCodes(skills);
        workShuntLogSkillCodes.forEach(item -> {
            item.setId(null);
            item.setShuntLogId(workShuntLog.getShuntLogId());
            item.setCreateTime(LocalDateTime.now());
        });

        workShuntLogSkillCodesRepository.saveBatch(workShuntLogSkillCodes);
    }

    private void initUserName(List<WorkUser> workers, WorkShuntLog workShuntLog) {
        if(CollUtil.isNotEmpty(workers)){
            List<String> userIds = workers.stream().map(WorkUser::getUserId).toList();
            GetUserInfoReqDTO dto = GetUserInfoReqDTO.builder().userIds(userIds.stream().collect(Collectors.toSet())).build();
            List<GetUserRoleInfoRespDTO> userShortInfoDTOS = userCenterGateway.listUserInfo(()-> dto);
            if(CollUtil.isNotEmpty(userShortInfoDTOS)){
                List<String> userNames = userShortInfoDTOS.stream().map(GetUserRoleInfoRespDTO::getUserName).toList();
                workShuntLog.setPkUserNames(String.join(",", userNames));
            }
        }
    }

    /**
     * Confirms the workload for a given user based on the provided skill codes.
     *
     * @param logIdBo The request object containing the user ID and skill codes.
     * @return {@code true} if the workload confirmation is successful, {@code false} otherwise.
     *
     * @throws BizException If any error occurs during the workload confirmation process.
     */
    public Boolean confirmWorkload(ReqMobileVerifyLogIdBo logIdBo) {
        List<String> codes = logIdBo.getSkillCodes();
        WorkUser workUser = workUserRepository.getTableOneDataByPkUserId(logIdBo.getUserId());
        return Option.when(!Objects.isNull(workUser),Try.of(()->{
            codes.forEach(code -> {
                saveOrAddWorkUserVolume(logIdBo.getUserId(), code);
            });
            return true;
        }).onFailure(t-> log.error("[客服确认接单异常:]{}{}{}", String.join(",", codes), logIdBo.getUserId(),t.getMessage())).getOrElse(false)).getOrElse(()->false);
    }

    
        /**
     * Saves or adds work user volume based on the given user ID and skill code.
     * If a record already exists for the user and skill, the volume is incremented.
     * If no record exists, a new record is created with a volume of 1.
     *
     * @param userId The ID of the user for whom the volume is being saved or added.
     * @param code The skill code associated with the user's volume.
     */
    private void saveOrAddWorkUserVolume(String userId, String code) {
        WorkSkill workSkill = workSkillRepository.getTableOneDataByParamKeyCode(code);
        if (Objects.isNull(workSkill)) {
           return;
        }
        WorkShuntOrderVolume queryVolume = workShuntOrderVolumeRepository.queryBySkillIdAndUserId(workSkill.getSkillId(), userId);
        Optional.ofNullable(queryVolume).ifPresentOrElse(v-> {
            workShuntOrderVolumeRepository.addWorkShuntOrderVolume(workSkill.getSkillId(), userId);
        }, ()-> {
            WorkShuntOrderVolume workShuntOrderVolume = new WorkShuntOrderVolume();
            workShuntOrderVolume.setUserId(userId);
            workShuntOrderVolume.setSkillCode(code);
            workShuntOrderVolume.setSkillId(workSkill.getSkillId());
            workShuntOrderVolume.setVolumeId(IdGenUtil.getId());
            workShuntOrderVolume.setVolume(1);
            workShuntOrderVolumeRepository.save(workShuntOrderVolume);
        });
    }

    /**
     *  分配客服后，添加接单量数据: 分配时间
     * @param userId
     * @param skillId
     * @param skillCode
     */
    public void initAssignTimeToWorkUserVolume(String userId, String skillId, String skillCode) {

        WorkShuntOrderVolume queryVolume = workShuntOrderVolumeRepository.queryBySkillIdAndUserId(skillId, userId);

        Consumer<WorkShuntOrderVolume> updateAssignTime = wsov-> { wsov.setAssignTime(LocalDateTime.now()); workShuntOrderVolumeRepository.updateAssignTime(wsov);};

        Runnable initAssignTime = () -> {
            WorkShuntOrderVolume workShuntOrderVolume = new WorkShuntOrderVolume();
            workShuntOrderVolume.setUserId(userId);
            workShuntOrderVolume.setSkillCode(skillCode);
            workShuntOrderVolume.setSkillId(skillId);
            workShuntOrderVolume.setAssignTime(LocalDateTime.now());
            workShuntOrderVolume.setVolumeId(IdGenUtil.getId());
            workShuntOrderVolume.setVolume(0);
            workShuntOrderVolumeRepository.save(workShuntOrderVolume);
        };

        Optional.ofNullable(queryVolume).ifPresentOrElse(updateAssignTime, initAssignTime);

    }

    public Boolean closeDetailsId(ReqMobileVerifyLogIdBo logIdBo) {
        List<String> codes = logIdBo.getSkillCodes();
        return Option.when(CollUtil.isNotEmpty(codes), () -> {
            codes.forEach(code -> {
                WorkSkill workSkill = workSkillRepository.getTableOneDataByParamKeyCode(code);
                if (Objects.isNull(workSkill)) throw new BizException(WORK_SKILL_NO_EXITS.getErrCode(), WORK_SKILL_NO_EXITS.getErrDesc());
                WorkShuntOrderVolume queryVolume = workShuntOrderVolumeRepository.queryBySkillIdAndUserId(workSkill.getSkillId(), logIdBo.getUserId());
                if (Objects.isNull(queryVolume)) throw new BizException(WORK_SKILL_ORDER_VOLUME_NO_DATA.getErrCode(), WORK_SKILL_ORDER_VOLUME_NO_DATA.getErrDesc());
                int volume = queryVolume.getVolume();
                if(volume <= 0) throw new BizException(WORK_SKILL_ORDER_VOLUME_ZERO.getErrCode(), WORK_SKILL_ORDER_VOLUME_ZERO.getErrDesc());
                workShuntOrderVolumeRepository.minusWorkShuntOrderVolume(workSkill.getSkillId(), logIdBo.getUserId());
            });

            return true;
        }).getOrElse(false);
    }

    public Boolean batchMinusWorkShuntOrderVolume(List<String> skillCodes, List<String> userIds) {
        return workShuntOrderVolumeRepository.batchMinusWorkShuntOrderVolume(skillCodes, userIds);
    }

    public GetWorkShuntRespDTO addGroupShunt(ReqAddGroupWorkShuntBO bo) {

        String pkUserId = bo.getUserId();
        String workSkillCode = bo.getSkillCode();
        return doTargetPkUser(pkUserId, workSkillCode, bo.getDomain());

    }

    @Transactional
    public GetWorkShuntRespDTO groupTransferShunt(ReqGroupTransferShuntShuntBO bo) {
        String pkUserId = bo.getTargetUserId();
        String workSkillCode = bo.getSkillCode();
        return doTargetPkUser(pkUserId, workSkillCode, bo.getDomain());
    }

    protected GetWorkShuntRespDTO doTargetPkUser(String pkUserId, String workSkillCode, String shuntType) {

        saveOrAddWorkUserVolume(pkUserId, workSkillCode);
        //设置主体
        WorkShuntLog workShuntLog = new WorkShuntLog();
        workShuntLog.setShuntLogId(IdGenUtil.getId());
        workShuntLog.setShuntType(shuntType);
        workShuntLog.setCreateTime(LocalDateTime.now());
        workShuntLog.setWorkTime(LocalDateTime.now());
        workShuntLog.setWorkOutTime(LocalDateTime.now());
        workShuntLog.setReqType(ReqTypeEnums.reqType2.getType().toString());
        workShuntLogRepository.save(workShuntLog);

        return GetWorkShuntRespDTO.builder().skillCode(workSkillCode).userId(pkUserId).build();

    }

    public void deleteByTime(String date) {
        workShuntLogRepository.deleteByTime(date);
        workShuntLogSkillCodesRepository.deleteByTime(date);
    }
}
