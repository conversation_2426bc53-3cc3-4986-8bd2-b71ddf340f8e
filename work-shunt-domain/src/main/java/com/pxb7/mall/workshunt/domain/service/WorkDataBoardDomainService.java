package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.ListSysDeptDubboRespDTO;
import com.pxb7.mall.workshunt.client.work.databoard.ReqListDataBoardDeptWorkUserDTO;
import com.pxb7.mall.workshunt.client.work.response.dataBoard.WorkUserDataBoardPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.PkSysUserDataBO;
import com.pxb7.mall.workshunt.infra.enums.ShuntAssignAlgorithmEnum;
import com.pxb7.mall.workshunt.infra.repository.db.*;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.ImGateway;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user.UserCenterGateway;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("mysql")
public class WorkDataBoardDomainService {

    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private WorkUserSettingSkillGameListRepository workUserSettingSkillGameListRepository;

    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;

    @Resource
    private UserSettingSkillRepository settingSkillRepository;

    @Resource
    private WorkShuntRuleSettingRepository workShuntRuleSettingRepository;

    @Resource
    private WorkSkillTemplateRepository workSkillTemplateRepository;

    @Resource
    private UserCenterGateway userCenterGateway;

    @Resource
    private ImGateway imGateway;


    public PkSysUserDataBO getPkSysUsrData(String pkUserId) {
        PkSysUserDataBO bo = new PkSysUserDataBO();

        WorkUser tableOneDataByParamKey = workUserRepository.getTableOneDataByPkUserId(pkUserId);
        if (tableOneDataByParamKey != null) {
            Set<String> pkUserIds = new HashSet<>();
            pkUserIds.add(tableOneDataByParamKey.getUserId());
            GetUserInfoReqDTO userInfoReqDTO = GetUserInfoReqDTO.builder()
                .deptId(tableOneDataByParamKey.getDeptId())
                .userIds(pkUserIds)
                .build();
            List<GetUserRoleInfoRespDTO> dataRespDTO = userCenterGateway.listUserInfo(() -> userInfoReqDTO);
            if (dataRespDTO != null && !dataRespDTO.isEmpty()) {
                GetUserRoleInfoRespDTO getUserRoleInfoRespDTO = dataRespDTO.get(0);
                bo.setUserName(getUserRoleInfoRespDTO.getUserName());
                bo.setDeptName(getUserRoleInfoRespDTO.getDeptName());
            }
        }
        return bo;
    }

    public Page<WorkUserDataBoardPageListRespDTO> listUserWorkUser(ReqListDataBoardDeptWorkUserDTO dto) {

        //拉取分页数据
        Page<WorkUserSettingSkillBoard> boardPage = getWorkUserSettingSkillBoardPage(dto);
        //初始化返回接口分页数据
        Page<WorkUserDataBoardPageListRespDTO> resultPage = initResultPage(boardPage);

        List<WorkUserSettingSkillBoard> records = boardPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return resultPage;
        }

        List<WorkUserDataBoardPageListRespDTO> respDTOS = handleResult(records, dto);

        return resultPage.setRecords(respDTOS);
    }



    @NotNull
    private List<WorkUserDataBoardPageListRespDTO> handleResult(List<WorkUserSettingSkillBoard> records, ReqListDataBoardDeptWorkUserDTO dto) {

        Map<String /*userId*/, List<WorkShuntOrderVolume>> userOrderVolumeMap = getWorkShuntOrderVolumes(records);

        Map<String /*userId*/, List<GetUserRoleInfoRespDTO>> userInfoMap = getUserInfoMap(records);

        Optional<Map<String, List<WorkUserSettingSkillGameList>>> relateGames = getSkillRealteGame(records);

        Map<String /*skillId*/, List<WorkShuntRuleSetting>>  shuntRuleMaps = getSkillRelateRule(records);

        Map<String, WorkSkillTemplate> workSkillTemplateMap = workUserRelateTemplate(records);

        Map<String /*userId*/, Integer> sessionCountMap = getUserSessionCount(records);

        return records.stream().map(r -> {
            //init user info
            WorkUserDataBoardPageListRespDTO respDTO = new WorkUserDataBoardPageListRespDTO();
            respDTO.setUserId(r.getUserId());
            respDTO.setImOnLineState((r.getIsOnline() == null || !r.getIsOnline()) ? 0 : 1);
            List<GetUserRoleInfoRespDTO> userRoleInfoRespDTOS = userInfoMap.get(r.getUserId());
            if (CollUtil.isNotEmpty(userRoleInfoRespDTOS)) {
                respDTO.setUserName(userRoleInfoRespDTOS.get(0).getUserName());
                respDTO.setDeptName(userRoleInfoRespDTOS.get(0).getDeptName());
            }

            respDTO.setIsOverrideMaxLimit(Objects.nonNull(workSkillTemplateMap.get(r.getUserId())) && workSkillTemplateMap.get(r.getUserId()).getOverrideMaxLimit());

            Tuple2<Boolean, Boolean> shuntRuleTuple = loadRuleSetting(shuntRuleMaps.get(r.getSkillId()));
            respDTO.setHasRoomMaxLimit(shuntRuleTuple._1);
            respDTO.setHasSessionMaxLimit(shuntRuleTuple._2);
            respDTO.setSkillName(r.getSkillName());
            respDTO.setSkillId(r.getSkillId());
            respDTO.setTemplateId(workSkillTemplateMap.getOrDefault(r.getUserId(), new WorkSkillTemplate()).getSkillTemplateId());
            respDTO.setRuleMaxNum(Objects.isNull(r.getMaxLimit()) ? 0L : r.getMaxLimit());
            respDTO.setCurrentSessionNum(sessionCountMap.getOrDefault(r.getUserId(), 0));
            respDTO.setSessionMaxLimit(Objects.isNull(r.getSessionMaxLimit())? 0L : r.getSessionMaxLimit());
            respDTO.setSettingSkillId(r.getSettingSkillId());

            //init game
            initRelateGame(relateGames, r, respDTO);
            //init volume
            initVolume(userOrderVolumeMap, r, respDTO);

            return respDTO;

        }).toList();
    }


    private Map<String /**/, Integer> getUserSessionCount(List<WorkUserSettingSkillBoard> records) {
        Set<String> userIds = records.stream().map(WorkUserSettingSkillBoard::getUserId).collect(Collectors.toSet());
        List<ImGateway.DTO> dtos = imGateway.getConsumerSessionCount(userIds.stream().toList());
        if (CollUtil.isEmpty(dtos)) {
            return new HashMap<>();
        }
        return dtos.stream().collect(Collectors.toMap(ImGateway.DTO::getUserId, ImGateway.DTO::getSessionCount));
    }


    private Map<String /**/, WorkSkillTemplate> workUserRelateTemplate(List<WorkUserSettingSkillBoard> records) {
        Map<String, WorkSkillTemplate> workUserIdMapTemplateMap = new HashMap<>();

        List<String> userIds = records.stream().map(WorkUserSettingSkillBoard::getUserId).toList();
        if (CollUtil.isEmpty(userIds)) {
            return workUserIdMapTemplateMap;
        }

        List<WorkUser> workUsers = workUserRepository.queryByUserIds(userIds);
        if(CollUtil.isEmpty(workUsers)) {
            return workUserIdMapTemplateMap;
        }


        List<WorkUser> containerTemplateWorkUsers = workUsers.stream().filter(this::filterNotNullTemplateId).toList();
        Map<String, String> containerTemplateWorkUserMap = containerTemplateWorkUsers.stream().collect(Collectors.toMap(WorkUser::getUserId, WorkUser::getTemplateId));

        List<String> templateIds = containerTemplateWorkUsers.stream().map(WorkUser::getTemplateId).filter(CharSequenceUtil::isNotBlank).toList();
        List<WorkSkillTemplate> workSkillTemplates = workSkillTemplateRepository.queryByTemplateIds(templateIds);
        if (CollUtil.isEmpty(workSkillTemplates)) {
            return workUserIdMapTemplateMap;
        }

        Map<String, WorkSkillTemplate> workSkillTemplateMap = workSkillTemplates.stream().collect(Collectors.toMap(WorkSkillTemplate::getSkillTemplateId, a -> a));

        containerTemplateWorkUserMap.forEach((k,v) -> {
            WorkSkillTemplate workSkillTemplate = workSkillTemplateMap.get(v);
            if(Objects.nonNull(workSkillTemplate)) {
                workUserIdMapTemplateMap.put(k, workSkillTemplate);
            }
        });

        return workUserIdMapTemplateMap;

    }



    private boolean filterNotNullTemplateId(WorkUser workUser) {
        return Objects.nonNull(workUser.getTemplateId());
    }


    private Map<String, List<WorkShuntRuleSetting>> getSkillRelateRule(List<WorkUserSettingSkillBoard> records) {
        List<String> skillIds = records.stream().map(WorkUserSettingSkillBoard::getSkillId).toList();
        List<WorkShuntRuleSetting> workShuntRuleSettings = workShuntRuleSettingRepository.queryBy(skillIds);
        if(CollUtil.isEmpty(workShuntRuleSettings)) {
            return new HashMap<>();
        }
        return workShuntRuleSettings.stream().collect(Collectors.groupingBy(WorkShuntRuleSetting::getSkillId));
    }



    private Optional<Map<String /*settingSkillId*/,List<WorkUserSettingSkillGameList>>> getSkillRealteGame(List<WorkUserSettingSkillBoard> records) {
        List<String> settingIds = records.stream().map(WorkUserSettingSkillBoard::getSettingSkillId).toList();
        List<WorkUserSettingSkillGameList> gameLists = workUserSettingSkillGameListRepository.queryBySettingSkillId(settingIds);
        return Optional.of(gameLists.stream().collect(Collectors.groupingBy(WorkUserSettingSkillGameList::getSetttingSkillId)));
    }



    @NotNull
    private Map<String, List<GetUserRoleInfoRespDTO>> getUserInfoMap(List<WorkUserSettingSkillBoard> records) {

        Set<String> userIds = records.stream().map(WorkUserSettingSkillBoard::getUserId).collect(Collectors.toSet());

        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }

        GetUserInfoReqDTO userInfoReqDTO = GetUserInfoReqDTO.builder().userIds(userIds).build();

        List<GetUserRoleInfoRespDTO> dataRespDTO = userCenterGateway.listUserInfo(() -> userInfoReqDTO);

        return Option.when(CollUtil.isNotEmpty(dataRespDTO), () -> dataRespDTO)
                .getOrElse(ArrayList::new)
                .stream()
                .collect(Collectors.groupingBy(GetUserRoleInfoRespDTO::getUserId));

    }



    private Map<String, List<WorkShuntOrderVolume>> getWorkShuntOrderVolumes(List<WorkUserSettingSkillBoard> records) {
        List<String> userIds = records.stream().map(WorkUserSettingSkillBoard::getUserId).filter(CharSequenceUtil::isNotBlank).toList();
        List<String> skillIds = records.stream().map(WorkUserSettingSkillBoard::getSkillId).toList();
        List<WorkShuntOrderVolume> workShuntOrderVolumes = workShuntOrderVolumeRepository.queryBySkillIdsAndUserIds(skillIds, userIds);

        return Option.when(CollUtil.isNotEmpty(workShuntOrderVolumes), () ->
                workShuntOrderVolumes.stream().collect(Collectors.groupingBy(WorkShuntOrderVolume::getUserId))
        ).getOrElse(HashMap::new);
    }


    private Tuple2<Boolean, Boolean> loadRuleSetting(List<WorkShuntRuleSetting> workShuntRuleSettings) {
        boolean hasSessionMaxLimit = false;
        boolean hasRoomMaxLimit = false;
        if(CollUtil.isNotEmpty(workShuntRuleSettings)) {
            for (WorkShuntRuleSetting workShuntRuleSetting : workShuntRuleSettings) {
                if (workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.ROOM_AVERAGE.getShuntAlgorithmType())) {
                    hasRoomMaxLimit = true;
                } else if (workShuntRuleSetting.getShuntType().equals(ShuntAssignAlgorithmEnum.SESSION_AVERAGE.getShuntAlgorithmType())) {
                    hasSessionMaxLimit = true;
                }
            }
        }
        return Tuple.of(hasRoomMaxLimit, hasSessionMaxLimit);
    }

    private static void initVolume(Map<String, List<WorkShuntOrderVolume>> userOrderVolumeMap,
                                  WorkUserSettingSkillBoard r,
                                  WorkUserDataBoardPageListRespDTO respDTO) {
        List<WorkShuntOrderVolume> workShuntOrderVolumes = userOrderVolumeMap.get(r.getUserId());
        if (CollUtil.isNotEmpty(workShuntOrderVolumes)) {
            Optional<WorkShuntOrderVolume> workShuntOrderVolume = workShuntOrderVolumes.stream()
                .filter(t -> t.getSkillId().equals(r.getSkillId()))
                .findFirst();
            workShuntOrderVolume.ifPresent(shuntOrderVolume -> respDTO.setOrderNum(shuntOrderVolume.getVolume()));
        }
    }



    private static void initRelateGame(Optional<Map<String, List<WorkUserSettingSkillGameList>>> relateGames,
                                  WorkUserSettingSkillBoard r,
                                  WorkUserDataBoardPageListRespDTO respDTO) {
        relateGames.ifPresent(gamesMap->{
            List<WorkUserSettingSkillGameList> gameLists = gamesMap.get(r.getSettingSkillId());
            if (CollUtil.isNotEmpty(gameLists)) {
                respDTO.setGameName(gameLists.stream().map(WorkUserSettingSkillGameList::getGameName).toList());
                respDTO.setGameIds(gameLists.stream().map(WorkUserSettingSkillGameList::getGameId).toList());
            }
        });
    }



    @NotNull
    private static Page<WorkUserDataBoardPageListRespDTO> initResultPage(Page<WorkUserSettingSkillBoard> boardPage) {
        Page<WorkUserDataBoardPageListRespDTO> resultPage = new Page<>();
        resultPage.setTotal(boardPage.getTotal());
        resultPage.setSize(boardPage.getSize());
        resultPage.setCurrent(boardPage.getCurrent());
        return resultPage;
    }


    /**
     * Retrieves a paginated list of {@link WorkUserSettingSkillBoard} objects based on the provided parameters.
     *
     * @param dto The data transfer object containing the pagination parameters and filtering criteria.
     * @return A {@link Page} object containing the paginated list of {@link WorkUserSettingSkillBoard} objects.
     *

     * @see WorkUserSettingSkillBoard
     * @see ReqListDataBoardDeptWorkUserDTO
     * @see Page
     */
    private Page<WorkUserSettingSkillBoard> getWorkUserSettingSkillBoardPage(ReqListDataBoardDeptWorkUserDTO dto) {

        WorkUserSettingSkillPagePO po = WorkUserSettingSkillPagePO.builder().pageIndex(dto.getPageIndex()).pageSize(dto.getPageSize())
                .userIds(dto.getUserIds()).deptId(dto.getDeptId()).skillId(dto.getSkillId()).gameId(dto.getGameId()).imStatus(dto.getImStatus()).build();

        return Option.when(CharSequenceUtil.isNotBlank(dto.getGameId()),
            ()-> settingSkillRepository.queryWorkUserSettingByGamePage(po))
            .getOrElse(
                ()-> settingSkillRepository.queryWorkUserSettingPage(po)
            );
    }

}
