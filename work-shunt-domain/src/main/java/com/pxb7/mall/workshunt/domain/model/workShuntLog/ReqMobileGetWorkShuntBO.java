package com.pxb7.mall.workshunt.domain.model.workShuntLog;

import lombok.Data;

@Data
public class ReqMobileGetWorkShuntBO {
    /**
     *  ShuntTypeEnums.class
     *  IM_REGION(1, "IM域"),
     *  product_REGION(2, "商品工单域"),
     *  After_sale_REGION(3, "售后工单域"),
     *  complain_REGION(4, "客诉工单域");
     */
    private String shuntType;
    /**
     * 请求的技能CODE，用逗号分隔，
     */
    private String workSkillCodes;
    /**
     * 技能等级：1初级2中级3高级4专家
     */
    private String skillLevel;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 表达请求调度的人员数量，
     * Req num
     */
    private String reqNum;
//    /**
//     * 溢出分配
//     * 人员不足情况下，是否必须分配人员，1代表必须分配，0代表不需要分配,不传代表不需要分配
//     */
//    private String spill;
    /**
     * 是否在线 1是 0否
     */
    private String onlineState;
}
