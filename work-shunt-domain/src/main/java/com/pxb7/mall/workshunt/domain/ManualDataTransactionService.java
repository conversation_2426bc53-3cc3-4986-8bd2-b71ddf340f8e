package com.pxb7.mall.workshunt.domain;

import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

@Service
@Slf4j
public class ManualDataTransactionService {

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    public <T> Try<T> manual(Try<T> operation) {
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        return operation.onFailure(t -> {
            log.error("Transaction rollback due to exception: {}", t.getMessage());
                platformTransactionManager.rollback(transaction);
            })
            .andThen(() -> platformTransactionManager.commit(transaction));
    }

}
