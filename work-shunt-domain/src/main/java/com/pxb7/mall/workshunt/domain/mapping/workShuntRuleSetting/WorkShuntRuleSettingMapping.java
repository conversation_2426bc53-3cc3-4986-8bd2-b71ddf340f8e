package com.pxb7.mall.workshunt.domain.mapping.workShuntRuleSetting;

import com.pxb7.mall.workshunt.client.work.response.workShuntRule.WorkShuntRuleSettingListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workShuntRuleSetting.ReqSaveWorkShuntRuleSettingBO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 分流规则配置 -> WorkShuntRuleSetting  mapstruct
*/
@Mapper
public interface WorkShuntRuleSettingMapping {

    WorkShuntRuleSettingMapping INSTANCE = Mappers.getMapper(WorkShuntRuleSettingMapping.class);

    WorkShuntRuleSettingListRespDTO toWorkShuntRuleSettingListRespDTO(WorkShuntRuleSetting dto);

    WorkShuntRuleSetting toWorkShuntRuleSetting(ReqSaveWorkShuntRuleSettingBO bo);

}
