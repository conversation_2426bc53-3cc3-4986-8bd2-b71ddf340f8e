package com.pxb7.mall.workshunt.domain.mapping.workShuntRuleSettingCondition;

import com.pxb7.mall.workshunt.client.work.response.workShuntRule.WorkShuntRuleSettingConditionListRespDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 分流规则配置参数 -> WorkShuntRuleSettingCondition  mapstruct
*/
@Mapper
public interface WorkShuntRuleSettingConditionMapping {

    WorkShuntRuleSettingConditionMapping INSTANCE = Mappers.getMapper(WorkShuntRuleSettingConditionMapping.class);

    List<WorkShuntRuleSettingConditionListRespDTO> toWorkShuntRuleSettingConditionListRespDTO(List<WorkShuntRuleSettingCondition> dto);

}
