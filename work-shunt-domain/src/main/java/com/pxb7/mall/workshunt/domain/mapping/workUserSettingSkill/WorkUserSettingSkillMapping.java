package com.pxb7.mall.workshunt.domain.mapping.workUserSettingSkill;

import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 用户技能 -> WorkUserSettingSkill  mapstruct
*/
@Mapper
public interface WorkUserSettingSkillMapping {

    WorkUserSettingSkillMapping INSTANCE = Mappers.getMapper(WorkUserSettingSkillMapping.class);

    @Mapping(target = "maxLimit", source = "ruleMaxNum")
    List<WorkUserSettingSkillPageListRespDTO> toWorkUserSettingSkillPageListRespDTO(List<WorkUserSettingSkill> list);
}
