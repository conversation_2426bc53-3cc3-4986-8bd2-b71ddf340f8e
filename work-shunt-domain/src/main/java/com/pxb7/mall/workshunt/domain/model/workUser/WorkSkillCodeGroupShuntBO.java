package com.pxb7.mall.workshunt.domain.model.workUser;

import lombok.Data;

import java.io.Serializable;

@Data
public class WorkSkillCodeGroupShuntBO implements Serializable {
    /**
     * 部门类型 1博淳 2意竞 3号商
     */
    private String deptType;
    /**
     *  ShuntTypeEnums.class
     *  IM_REGION(1, "IM域"),
     *  product_REGION(2, "商品工单域"),
     *  After_sale_REGION(3, "售后工单域"),
     *  complain_REGION(4, "客诉工单域");
     */
    private String shuntType;
    /**
     * 请求的技能CODE，用逗号分隔，
     */
    private String workSkillCodes;

    private String skillCode;
    /**
     * 技能等级：1初级2中级3高级4专家
     */
    private String skillLevel;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 表达请求调度的人员数量，
     * Req num
     */
    private String reqNum;
    /**
     * 是否在线
     */
    private String onlineState;

    /**
     * 0:默认后台操作  1：IM
     */
    private String sourceType;
}
