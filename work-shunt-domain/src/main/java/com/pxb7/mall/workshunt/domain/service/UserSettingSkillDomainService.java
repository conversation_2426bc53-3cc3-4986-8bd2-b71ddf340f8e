package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.domain.mapping.workUser.WorkUserMapping;
import com.pxb7.mall.workshunt.domain.mapping.workUserSettingSkill.WorkUserSettingSkillMapping;
import com.pxb7.mall.workshunt.domain.mapping.workUserSettingSkillGameList.WorkUserSettingSkillGameListMapping;
import com.pxb7.mall.workshunt.domain.model.PkSysUserDataBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqSaveWorkUserSettingSkillBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqWorkSkillBO;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;
import com.pxb7.mall.workshunt.infra.model.skill.GamePO;
import com.pxb7.mall.workshunt.infra.repository.db.*;
import com.pxb7.mall.workshunt.infra.repository.db.entity.*;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("mysql")
public class UserSettingSkillDomainService {

    @Resource
    private UserSettingSkillRepository workUserSettingSkillRepository;
    @Resource
    private WorkUserSettingSkillGameListRepository workUserSettingSkillGameListRepository;
    @Resource
    private WorkSkillRepository workSkillRepository;
    @Resource
    private WorkSkillTypeRepository workSkillTypeRepository;

    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private WorkDataBoardDomainService workDataBoardDomainService;



    protected void setParams(WorkUserSettingSkillPageListRespDTO item) {
        //设置游戏
        List<WorkUserSettingSkillGameList> listGame = workUserSettingSkillGameListRepository.queryBySettingSkillId(item.getSettingSkillId());
        if (CollUtil.isNotEmpty(listGame)) {
            List<GameDTO> gameDTOS = WorkUserSettingSkillGameListMapping.INSTANCE.toGameListDTO(listGame);
            item.setGameDTOList(gameDTOS);
        }
        WorkSkill workSkill = workSkillRepository.getTableOneDataByParamKey(item.getWorkSkillId());
        if (workSkill != null) {
            item.setSkillRelationName(workSkill.getSkillRelationGame());
            item.setWorkSkillName(workSkill.getSkillName());
            item.setRuleMaxNum(item.getRuleMaxNum());
            item.setSkillCode(workSkill.getSkillCode());
            //设置技能分类
            LambdaQueryWrapper<WorkSkillType> lambdaWorkSkillType = new LambdaQueryWrapper<>();
            lambdaWorkSkillType.eq(WorkSkillType::getSkillTypeId, workSkill.getSkillTypeId());
            WorkSkillType workSkillType = workSkillTypeRepository.getOne(lambdaWorkSkillType);
            if (workSkillType != null) {
                item.setSkillTypeId(workSkillType.getSkillTypeId());
                item.setSkillTypeName(workSkillType.getSkillTypeName());
            }
        }
    }


    public void saveBatch(ReqSaveWorkUserSettingSkillBO bo) {
        List<WorkUserSettingSkill> skills = queryAllByWorkUserId(bo.getWorkUserId());
        if (CollUtil.isNotEmpty(skills)) {
            //remove user_skill_setting old data
            List<String> settingSkillIds = skills.stream()
                    .map(WorkUserSettingSkill::getSettingSkillId)
                    .toList();
            removeRelationGameBySkillIds(settingSkillIds);
            removeBatch(skills);
        }

        //save
        bo.getSkills().forEach(skill -> {
            WorkUserSettingSkill userSettingSkill = saveSettingSkill(bo.getWorkUserId(), skill);
            if (skill.isSkillRelationGame() && CollUtil.isNotEmpty(skill.getGames())) {
                saveSettingRelationGame(userSettingSkill.getSettingSkillId(), skill.getGames());
            }
        });
    }


    public WorkUserSettingSkill saveSettingSkill(String workUserId, ReqWorkSkillBO data) {
        String settingSkillId = IdGenUtil.getId();
        WorkUserSettingSkill userSettingSkill = WorkUserSettingSkill.builder()
                .settingSkillId(settingSkillId)
                .workSkillId(data.getSkillId())
                .workUserId(workUserId)
                .able(data.isAble())
                .createTime(LocalDateTime.now())
                .maxLimit(data.getMaxLimit())
                .maxSessionLimit(data.getMaxSessionLimit())
                .build();
        workUserSettingSkillRepository.save(userSettingSkill);

        return userSettingSkill;
    }

    public void saveSettingRelationGame(String userSettingSKillId, List<GamePO> games) {
        workUserSettingSkillGameListRepository.saveBatch(games.stream()
            .map(game -> WorkUserSettingSkillGameList.builder()
                .gameId(game.getGameId())
                .gameName(game.getGameName())
                .setttingSkillId(userSettingSKillId)
                .createTime(LocalDateTime.now())
                .build())
            .toList());
    }


    public void removeBatch(List<WorkUserSettingSkill> settingSkills) {
        workUserSettingSkillRepository.removeBatchByIds(settingSkills);
    }


    public void removeRelationGameBySkillIds(List<String> settingSkillIds) {
        workUserSettingSkillGameListRepository.removeBatchBySkillId(settingSkillIds);
    }

    public List<WorkUserSettingSkill> queryAllByWorkUserId(String  workUserId) {
        return workUserSettingSkillRepository.queryAllByWorkUserId(workUserId);
    }

    public List<WorkUserSettingSkillGameList> queryGameListBySettingSkillId(String settingSkillId) {
        return Option.of(workUserSettingSkillGameListRepository.queryBySettingSkillId(settingSkillId))
            .getOrElse(ArrayList::new);
    }

    public List<WorkUserSettingSkillGameList> queryGameListBySettingSkillId(List<String> ids){
        return Option.when(CollUtil.isNotEmpty(ids), () -> workUserSettingSkillGameListRepository.queryBySettingSkillId(ids))
            .getOrElse(ArrayList::new);
    }

    public List<WorkUserSettingSkillGameList> queryBySkillIdsAndGameId(List<String> skillIds, String gameId) {
        return Option.when(CollUtil.isNotEmpty(skillIds) && CharSequenceUtil.isNotBlank(gameId), () -> workUserSettingSkillRepository.queryBySKillIdsAndGameId(skillIds, gameId))
            .getOrElse(ArrayList::new);
    }


    public List<WorkUserSettingSkill> queryBySkillId(String skillID) {
        return Option.of(workUserSettingSkillRepository.queryBySkillId(skillID)).getOrElse(ArrayList::new);
    }

    public List<WorkUserSettingSkill> queryEnableSkillSettingBySkillIds(List<String> skillIds){
        return Option.when(CollUtil.isNotEmpty(skillIds), () -> workUserSettingSkillRepository.queryBySkillIds(skillIds, true))
            .getOrElse(ArrayList::new);
    }

    public List<WorkUserSettingSkill> queryEnableSkillSettingBySettingIds(List<String> settingSkillIds){
        return Option.when(CollUtil.isNotEmpty(settingSkillIds), () -> workUserSettingSkillRepository.queryBySettingSkillId(settingSkillIds))
            .getOrElse(ArrayList::new);
    }

    public List<WorkUserSettingSkillGameList> queryWorkUserSettingSkillGameListByGameId(List<String> gameId) {
        return workUserSettingSkillGameListRepository.queryBySettingSkillIdByGameIds(gameId);
    }



    public List<WorkUserSettingSkill> queryByWorkUserIds(List<String> workUserId){
        return workUserSettingSkillRepository.queryByWorkUserIds(workUserId);
    }



    public WorkUserSettingSkill queryByWorkUserIdAndSkillId(String workUserId, String skillId){
        return workUserSettingSkillRepository.queryByWorkUserIdAndSkillId(workUserId, skillId);
    }


    public List<WorkUserSettingSkill> queryByWorkUserIdsAndSkillId(List<String> workUserIds, String skillIds){
        if (CollUtil.isEmpty(workUserIds) || CharSequenceUtil.isEmpty(skillIds)) {
            return new ArrayList<>();
        }
        return workUserSettingSkillRepository.queryByWorkUserIdsAndSkillId(workUserIds, skillIds);
    }


    public List<WorkUserSettingSkillPageListRespDTO> getUserSettingSkillByPkUserId(String pkUserId) {
        WorkUser workUser = workUserRepository.getTableOneDataByPkUserId(pkUserId);
        return Option.when(!Objects.isNull(workUser), ()-> {
            List<WorkUserSettingSkill> list = workUserSettingSkillRepository.queryAllByWorkUserId(workUser.getWorkUserId());
            List<WorkUserSettingSkill> filters = Option.when(CollUtil.isNotEmpty(list), ()-> list.stream().filter(WorkUserSettingSkill::isAble).toList()).getOrElse(ArrayList::new);
            return Option.when(CollUtil.isNotEmpty(filters), ()-> {
                List<WorkUserSettingSkillPageListRespDTO> listRespDTOS = WorkUserSettingSkillMapping.INSTANCE.toWorkUserSettingSkillPageListRespDTO(filters);
                listRespDTOS.forEach(this::setParams);
                return listRespDTOS;
            }).getOrElse(ArrayList::new);
        }).getOrElse(ArrayList::new);

    }




    public List<WorkUserSelectDeptUserRespDTO> getSkillWorkUserByCode(String skillCode) {
        WorkSkill one =  workSkillRepository.getTableOneDataByParamKeyCode(skillCode);
        return Option.when(!Objects.isNull(one),()-> {
            List<WorkUser> workUserBySkillCode = workUserRepository.getWorkUserBySkillCode(skillCode);
            List<WorkUserSelectDeptUserRespDTO> listUser = WorkUserMapping.INSTANCE.toWorkUserSelectDeptUserRespDTO(workUserBySkillCode);
            listUser.forEach(item ->{
                PkSysUserDataBO pkSysUsrData = workDataBoardDomainService.getPkSysUsrData(item.getUserId());
                item.setUserName(pkSysUsrData.getUserName());
                item.setDeptName(pkSysUsrData.getDeptName());
            });
            return listUser;
        }).getOrElseThrow(()-> new BizException(ErrorCode.NO_DATA_MSG.getErrCode(), ErrorCode.NO_DATA_MSG.getErrDesc()));
    }



    public List<WorkUser> getOnlineWorkUsersBySkillCode(String skillCode, String gameId) {
        WorkSkill workSkill = workSkillRepository.getTableOneDataByParamKeyCode(skillCode);
        if (Objects.isNull(workSkill)) {
            throw new BizException(ErrorCode.WORK_SKILL_NO_EXITS.getErrCode(), ErrorCode.WORK_SKILL_NO_EXITS.getErrDesc());
        }

        if (StringUtils.isNotBlank(gameId)) {
            if (!workSkill.getSkillRelationGame()) {
                throw new BizException(ErrorCode.WORK_SKILL_NO_MATCH_GAME.getErrCode(), ErrorCode.WORK_SKILL_NO_MATCH_GAME.getErrDesc());
            }
            return filterOnlineWorkUser(workUserRepository.getWorkUserBySkillCodeAndGameId(skillCode, gameId));
        }

        return filterOnlineWorkUser(workUserRepository.getWorkUserBySkillCode(skillCode));

    }



    private List<WorkUser> filterOnlineWorkUser(List<WorkUser> workUsers) {
        return Option.when(CollUtil.isNotEmpty(workUsers), () -> workUsers.stream()
            .filter(WorkUser::getIsOnline)
            .toList()).getOrElse(ArrayList::new);
    }


    public void updateSettingSkillMaxLimit(String settingSkillId, int type, Long maxLimit) {
        if (type == 1) {
            workUserSettingSkillRepository.updateWorkUserRoomMaxLimit(settingSkillId, maxLimit);
        } else {
            workUserSettingSkillRepository.updateWorkUserSessionMaxLimit(settingSkillId, maxLimit);
        }
    }

}
