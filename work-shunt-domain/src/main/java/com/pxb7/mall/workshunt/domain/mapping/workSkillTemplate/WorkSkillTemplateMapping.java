package com.pxb7.mall.workshunt.domain.mapping.workSkillTemplate;

import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkUserSkillTemplateDetailsRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkUserSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSettingGameList;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 技能模板 -> WorkSkillTemplate  mapstruct
*/
@Mapper
public interface WorkSkillTemplateMapping {

    WorkSkillTemplateMapping INSTANCE = Mappers.getMapper(WorkSkillTemplateMapping.class);

    List<WorkSkillTemplateSelectListRespDTO> toWorkSkillTemplateSelectListRespDTO(List<WorkSkillTemplate> list);

    List<GameDTO> toGameListDTO(List<WorkSkillTemplateSettingGameList> List);

    @Mappings({@Mapping(target = "userId", source = "userId"), @Mapping(target = "deptId", source = "deptId")})
    List<WorkUserSkillTemplateSelectListRespDTO> toWorkUserSkillTemplateSelectListRespDTO(List<WorkUser> List);

    List<WorkUserSkillTemplateDetailsRespDTO> tWorkUserSkillTemplateDetailsRespDTO(List<WorkUserSettingSkill> List);

}
