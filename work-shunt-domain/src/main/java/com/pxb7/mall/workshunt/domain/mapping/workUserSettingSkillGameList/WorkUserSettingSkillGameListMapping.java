package com.pxb7.mall.workshunt.domain.mapping.workUserSettingSkillGameList;

import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 技能游戏 -> WorkUserSettingSkillGameList  mapstruct
*/
@Mapper
public interface WorkUserSettingSkillGameListMapping {

    WorkUserSettingSkillGameListMapping INSTANCE = Mappers.getMapper(WorkUserSettingSkillGameListMapping.class);

    List<GameDTO> toGameListDTO(List<WorkUserSettingSkillGameList> List);
}
