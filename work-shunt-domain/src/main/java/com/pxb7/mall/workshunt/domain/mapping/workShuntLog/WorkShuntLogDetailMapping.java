package com.pxb7.mall.workshunt.domain.mapping.workShuntLog;

import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogDetail;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 请求日志记录 -> WorkShuntLogDetail  mapstruct
 * work_shunt_log_detail
*/
@Mapper
public interface WorkShuntLogDetailMapping {

    WorkShuntLogDetailMapping INSTANCE = Mappers.getMapper(WorkShuntLogDetailMapping.class);

    List<WorkShuntLogDetail> toWorkShuntLogDetails(List<WorkUser> listSkill);

    List<GetWorkShuntRespDTO> toMobileGetWorkShuntRespDTO(List<WorkShuntLogDetail> listDetails);

    GetWorkShuntRespDTO toMobileGetWorkShuntRespDTOObj(WorkShuntLogDetail listDetails);

}
