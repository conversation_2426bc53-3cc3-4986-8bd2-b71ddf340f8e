package com.pxb7.mall.workshunt.domain.mapping.workTimeTemplate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.domain.model.time.TimeTemplatePageRespBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqEditWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqPageListWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqSaveWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.infra.model.workTimeTemplate.WorkTimeTemplatePageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
* 工作时间模版 -> WorkTimeTemplate  mapstruct
*/
@Mapper
public interface WorkTimeTemplateMapping {

    WorkTimeTemplateMapping INSTANCE = Mappers.getMapper(WorkTimeTemplateMapping.class);

    WorkTimeTemplatePageListPO toWorkTimeTemplatePageListPO(ReqPageListWorkTimeTemplateBO bo);

    Page<TimeTemplatePageRespBO> toWorkTimeTemplatePageListRespDTO(Page<WorkTimeTemplate> page);

    WorkTimeTemplate toReqSaveWorkTimeTemplate(ReqSaveWorkTimeTemplateBO dto);

    WorkTimeTemplate toReqEditWorkTimeTemplate(ReqEditWorkTimeTemplateBO dto);

}
