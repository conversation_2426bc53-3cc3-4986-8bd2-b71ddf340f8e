package com.pxb7.mall.workshunt.domain.mapping.workUser;

import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.dataBoard.WorkUserDataBoardPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.domain.model.WorkUserPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workUser.WorkSkillCodeGroupShuntBO;
import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.WorkUserBO;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkSkillCodeGroupShuntPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 排班用户 -> WorkUser  mapstruct
*/
@Mapper
public interface WorkUserMapping {

    WorkUserMapping INSTANCE = Mappers.getMapper(WorkUserMapping.class);

    WorkUser toWorkUserFromBO(WorkUserBO workUserBO);

    List<WorkUserPageListRespDTO> toWorkUserPageListRespDTO(List<WorkUserSelectDeptUserRespDTO> listDeptUser);


    List<WorkUserSelectDeptUserRespDTO> toWorkUserSelectDeptUserRespDTO(List<WorkUser> workUserBySkillCode);

    WorkSkillCodeGroupShuntPO toWorkSkillCodeGroupShuntPO(WorkSkillCodeGroupShuntBO bo);

    WorkUserDeptUserRespDTO toWorkUserDeptUserRespDTOOne(WorkUser workUser);
    List<WorkUserDeptUserRespDTO> toWorkUserDeptUserRespDTO(List<WorkUser> workUserList);

    List<WorkUserDataBoardPageListRespDTO> toWorkUserDataBoardPageListRespDTO(List<WorkUserSelectDeptUserRespDTO> listDeptUser);



}
