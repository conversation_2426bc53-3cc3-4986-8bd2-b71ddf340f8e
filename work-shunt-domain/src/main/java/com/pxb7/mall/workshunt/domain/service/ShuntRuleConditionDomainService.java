package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntRuleSettingConditionRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DS("mysql")
public class ShuntRuleConditionDomainService {

    @Resource
    private WorkShuntRuleSettingConditionRepository workShuntRuleSettingConditionRepository;

    public boolean save(List<String> condition, String ruleType, String skillId) {
        Option.when(CollUtil.isNotEmpty(condition), () -> {
            condition.forEach(c->{
                WorkShuntRuleSettingCondition cond = new WorkShuntRuleSettingCondition();
                cond.setSkillId(skillId);
                cond.setConditionType(c);
                cond.setSettingConditionId(IdGenUtil.getId());
                cond.setCreateTime(LocalDateTime.now());
                cond.setShuntRuleType(ruleType);
                workShuntRuleSettingConditionRepository.save(cond);
            });
            return true;
        }).getOrElse(() -> null);

        return true;
    }

    public List<WorkShuntRuleSettingCondition> queryBy(String skillId, String shuntRuleType) {
        return workShuntRuleSettingConditionRepository.queryBy(skillId, shuntRuleType);
    }

    public List<WorkShuntRuleSettingCondition> queryBy(List<String> skillId, String shuntRuleType) {
        return Option.of(workShuntRuleSettingConditionRepository.queryBy(skillId, shuntRuleType)).getOrElse(ArrayList::new);
    }


    public boolean deleteBySkillId(String skillId) {
        return workShuntRuleSettingConditionRepository.deleteBy(skillId);
    }


    public Boolean deleteWorkShuntRuleSettingCondition(String settingConditionId) {
        LambdaQueryWrapper<WorkShuntRuleSettingCondition> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkShuntRuleSettingCondition::getSettingConditionId, settingConditionId);
        WorkShuntRuleSettingCondition workShuntRuleSettingCondition = workShuntRuleSettingConditionRepository.getOne(lambdaQueryWrapper);
        if (workShuntRuleSettingCondition == null) {
            String outMessage = "数据ID = " + settingConditionId + ErrorCode.NO_DATA_MESSAGE.getErrDesc();
            log.error(outMessage);
            throw new BizException(ErrorCode.NO_DATA_MESSAGE.getErrCode(), outMessage);
        }
        return workShuntRuleSettingConditionRepository.removeById(workShuntRuleSettingCondition.getId());
    }
}
