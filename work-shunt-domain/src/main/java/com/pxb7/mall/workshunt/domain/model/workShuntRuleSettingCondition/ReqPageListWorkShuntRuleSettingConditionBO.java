package com.pxb7.mall.workshunt.domain.model.workShuntRuleSettingCondition;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


                    
@Data
public class ReqPageListWorkShuntRuleSettingConditionBO implements Serializable {

    /**
     * 第几页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

/**
* 主键
*/
        private Long id ;
    /**
* 条件ID
*/
        private String settingConditionId ;
    /**
* 分流规则ID
*/
        private String shuntSettingRuleId ;
    /**
* 条件类型1当前时间排班内2未超接待上限制3IM在线
*/
        private String conditionType ;
    /**
* 创建时间
*/
        private LocalDateTime createTime ;
    }