package com.pxb7.mall.workshunt.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillTypeRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class WorkSkillTypeDomainService {

    @Resource
    private WorkSkillTypeRepository workSkillTypeRepository;

    public List<WorkSkillType> getSelectList(){
        LambdaQueryWrapper<WorkSkillType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(WorkSkillType::getSort);
        List<WorkSkillType> list = workSkillTypeRepository.list(queryWrapper);

        return Option.of(list).getOrElse(ArrayList::new);
    }
}
