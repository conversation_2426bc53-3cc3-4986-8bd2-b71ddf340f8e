package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.pxb7.mall.workshunt.domain.model.workUserSettingDay.ReqSaveWorkUserSettingDayBO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkTimeTemplateRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserSettingDayRelationWorkTemplatRepository;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserSettingDayRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDayRelationWorkTemplat;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@DS("mysql")
public class UserSettingDayDomainService {

    @Resource
    private WorkUserSettingDayRelationWorkTemplatRepository workUserSettingDayRelationWorkTemplatRepository;
    @Resource
    private WorkUserSettingDayRepository workUserSettingDayRepository;

    @Resource
    private WorkTimeTemplateRepository workTimeTemplateRepository;


    public List<WorkTimeTemplate> queryTimeTemplateByWorkUserId(List<String> userId){

        String day = DateUtil.dayOfMonth(new Date())+"";
        day = Integer.parseInt(day) <10 ? "0"+day : day;
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化当前日期
        String formattedDate = currentDate.format(formatter);

        List<WorkUserSettingDay> days = workUserSettingDayRepository.query(userId, formattedDate, day);
        List<WorkUserSettingDayRelationWorkTemplat> templates = Option.when(CollUtil.isNotEmpty(days), () -> workUserSettingDayRelationWorkTemplatRepository.queryBy(days.stream()
            .map(WorkUserSettingDay::getSettingDayId)
            .toList())).getOrElse(ArrayList::new);

        return Option.when(CollUtil.isNotEmpty(templates), ()-> workTimeTemplateRepository.queryByIds(templates.stream().map(WorkUserSettingDayRelationWorkTemplat::getWorkTemplateId).toList())).getOrElse(ArrayList::new);

    }



    public WorkUserSettingDay saveWorkUserSettingDay(ReqSaveWorkUserSettingDayBO bo) {
        WorkUserSettingDay userSettingDay = new WorkUserSettingDay();
        userSettingDay.setSettingDayId(IdGenUtil.getId().toString());
        userSettingDay.setMonth(bo.getMonth());
        userSettingDay.setDay(bo.getDay());
        userSettingDay.setWorkUserId(bo.getWorkUserId());
        userSettingDay.setCreateTime(LocalDateTime.now());
        workUserSettingDayRepository.save(userSettingDay);
        return userSettingDay;
    }


    public void saveUserSettingDayRelationTemplate(ReqSaveWorkUserSettingDayBO bo, String userSettingID) {
        List<String> templateIds = bo.getTimeTemplateIds();

        List<WorkUserSettingDayRelationWorkTemplat> listWorkUserSettingDayRelationWork = templateIds.stream().map(s -> {
            WorkUserSettingDayRelationWorkTemplat oneWorkUserSettingDayRelationWorkTemplat = new WorkUserSettingDayRelationWorkTemplat();
            oneWorkUserSettingDayRelationWorkTemplat.setSettingDayId(userSettingID);
            oneWorkUserSettingDayRelationWorkTemplat.setWorkTemplateId(s);
            oneWorkUserSettingDayRelationWorkTemplat.setCreateTime(LocalDateTime.now());
            return oneWorkUserSettingDayRelationWorkTemplat;
        }).toList();
        workUserSettingDayRelationWorkTemplatRepository.saveBatch(listWorkUserSettingDayRelationWork);
    }

    public void deleteUserSettingDay(String userId, String month, String day) {
        workUserSettingDayRepository.deleteUserSettingDay(userId, month, day);
    }

    public void deleteUserSettingTimeTemplate(String userId, String month, String day) {

        WorkUserSettingDay userSettingDays = workUserSettingDayRepository.query(userId, month, day);

        if (!Objects.isNull(userSettingDays)) {
            workUserSettingDayRelationWorkTemplatRepository.deleteByTimeTemplate(userSettingDays.getSettingDayId());
        }
    }
}
