package com.pxb7.mall.workshunt.domain.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.domain.mapping.workTimeTemplate.WorkTimeTemplateMapping;
import com.pxb7.mall.workshunt.domain.model.time.TimeTemplatePageRespBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqEditWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqPageListWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.domain.model.workTimeTemplate.ReqSaveWorkTimeTemplateBO;
import com.pxb7.mall.workshunt.infra.model.workTimeTemplate.WorkTimeTemplatePageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.WorkTimeTemplateRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@DS("mysql")
public class TimeTemplateDomainService {

    @Resource
    private WorkTimeTemplateRepository workTimeTemplateRepository;

    public Page<TimeTemplatePageRespBO> listPage(ReqPageListWorkTimeTemplateBO dto) {
        WorkTimeTemplatePageListPO workTimeTemplatePageListPo = WorkTimeTemplateMapping.INSTANCE.toWorkTimeTemplatePageListPO(dto);
        Page<WorkTimeTemplate> pageList = workTimeTemplateRepository.listPageWorkTimeTemplate(workTimeTemplatePageListPo);
        return WorkTimeTemplateMapping.INSTANCE.toWorkTimeTemplatePageListRespDTO(pageList);
    }

    public Boolean save(ReqSaveWorkTimeTemplateBO dto) {
        WorkTimeTemplate workTimeTemplate = WorkTimeTemplateMapping.INSTANCE.toReqSaveWorkTimeTemplate(dto);
        workTimeTemplate.setTimeTemplateId(String.valueOf(IdGenUtil.getId()));
        workTimeTemplate.setCreateTime(LocalDateTime.now());
        workTimeTemplate.setDeleted(false);
        return workTimeTemplateRepository.save(workTimeTemplate);
    }

    public Boolean edit(ReqEditWorkTimeTemplateBO dto) {
        WorkTimeTemplate editWorkTimeTemplate = WorkTimeTemplateMapping.INSTANCE.toReqEditWorkTimeTemplate(dto);
        editWorkTimeTemplate.setUpdateTime(LocalDateTime.now());
        return workTimeTemplateRepository.update(editWorkTimeTemplate);
    }

    public boolean exist(String templateTimeId) {
        LambdaQueryWrapper<WorkTimeTemplate> query = new LambdaQueryWrapper<>();
        query.eq(WorkTimeTemplate::getTimeTemplateId, templateTimeId).eq(WorkTimeTemplate::getDeleted, false);
        return workTimeTemplateRepository.getOne(query) != null;
    }

    public Boolean delete(String timeTemplateId) {
        LambdaUpdateWrapper<WorkTimeTemplate> lambdaUpdateWrapper = new LambdaUpdateWrapper<WorkTimeTemplate>();
        lambdaUpdateWrapper.eq(WorkTimeTemplate::getTimeTemplateId, timeTemplateId);
        return workTimeTemplateRepository.remove(lambdaUpdateWrapper);
    }

    public List<WorkTimeTemplate> dropdown() {
        LambdaQueryWrapper<WorkTimeTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkTimeTemplate::getDeleted, false);

        return workTimeTemplateRepository.list(queryWrapper);
    }

    public boolean checkRepeat(String templateTitle) {
        return workTimeTemplateRepository.checkRepeat(templateTitle);
    }

    public boolean checkRepeat(String templateTitle, String timeTemplateId) {
        return workTimeTemplateRepository.checkRepeat(templateTitle, timeTemplateId);
    }
}
