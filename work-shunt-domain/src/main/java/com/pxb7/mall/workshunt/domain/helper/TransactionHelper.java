package com.pxb7.mall.workshunt.domain.helper;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.function.BiConsumer;
import java.util.function.Function;

@Slf4j
@Component
public class TransactionHelper {

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 事务处理
     *
     * @param function 事务内业务逻辑
     * @param consumer 事务执行完的处理事件
     */
    public <T> void execute(T t, Function<T, Boolean> function, BiConsumer<T, Boolean> consumer) {
        long startTime = System.currentTimeMillis();
        Boolean flag = transactionTemplate.execute(status -> {
            try {
                if (function.apply(t)) {
                    return true;
                }
                status.setRollbackOnly();
                return false;
            } catch (DuplicateKeyException duplicateKeyException) {
                log.warn("唯一键冲突, param:{}, errorMsg:{}", JSON.toJSONString(t), duplicateKeyException.getMessage());
                return true;
            } catch (Exception e) {
                log.error("事务发生异常进行回滚", e);
                status.setRollbackOnly();
                return false;
            }
        });
        log.debug("本次事务，耗时{}微秒", (System.currentTimeMillis() - startTime));
        consumer.accept(t, flag);
    }
}
