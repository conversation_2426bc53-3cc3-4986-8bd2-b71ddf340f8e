package com.pxb7.mall.workshunt.domain.mapping.workUserSettingDay;

import com.pxb7.mall.workshunt.domain.model.workUserSettingDay.ReqSaveWorkUserSettingDayBO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
* 用户时间 -> WorkUserSettingDay  mapstruct
*/
@Mapper
public interface WorkUserSettingDayMapping {

    WorkUserSettingDayMapping INSTANCE = Mappers.getMapper(WorkUserSettingDayMapping.class);

    WorkUser toWorkUser(ReqSaveWorkUserSettingDayBO bo);
}
