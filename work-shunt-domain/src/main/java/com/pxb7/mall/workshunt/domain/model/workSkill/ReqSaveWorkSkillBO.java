package com.pxb7.mall.workshunt.domain.model.workSkill;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder
public class ReqSaveWorkSkillBO {


    /**
     * 技能名称
     */
    private String skillName ;
    /**
     * 技能code
     */
    private String skillCode ;
    /**
     * 技能类型ID
     */
    private String skillTypeId ;
    /**
     * 是否关联游戏1：是0否
     */
    private Boolean skillRelationGame;
    /**
     * 排序值
     */
    private int skillSort ;

    /**
     * 初开始数据
     */
    private int earlyStart ;
    /**
     * 初结束数据
     */
    private int earlyEnd ;
    /**
     * 中开始数据
     */
    private int middleStart ;
    /**
     * 中结束数据
     */
    private int middleEnd ;
    /**
     * 高开始数据
     */
    private int highStart ;
    /**
     * 高结束数据
     */
    private int highEnd ;
    /**
     * 专家开始数据
     */
    private int expertStart ;
    /**
     * 专家结束数据
     */
    private int expertEnd ;


    private String createUserId;

    private String updateUserId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
