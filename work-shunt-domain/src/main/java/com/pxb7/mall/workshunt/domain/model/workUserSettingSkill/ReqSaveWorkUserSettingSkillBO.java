package com.pxb7.mall.workshunt.domain.model.workUserSettingSkill;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReqSaveWorkUserSettingSkillBO implements Serializable {

    /**
     * 设置技能 - 传入这个字段
     */
    private String workUserId;

    /**
     * 部门类型 1公司部门 2号商部门
     */
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
    /**
     * 部门ID
     */
    private String deptId ;
    /**
     * 用户ID
     */
    private String userId ;
    /**
     * 设置工作技能
     */
    private  List<ReqWorkSkillBO> skills;
}