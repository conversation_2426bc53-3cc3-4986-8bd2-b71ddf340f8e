package com.pxb7.mall.workshunt.domain.service;

import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntRuleSettingRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShuntRuleSettingDomainService {

    @Resource
    private WorkShuntRuleSettingRepository workShuntRuleSettingRepository;


    public WorkShuntRuleSetting queryBy(String skillId, String shuntRuleType){
        return workShuntRuleSettingRepository.queryBy(skillId, shuntRuleType);
    }

    public void deleteBySkillId(String skillId) {
        workShuntRuleSettingRepository.deleteBySkillId(skillId);
    }




    public boolean saveRuleSetting(String shuntType, String shuntRuleType, String skillId) {
        WorkShuntRuleSetting workShuntRuleSetting = new WorkShuntRuleSetting();
        workShuntRuleSetting.setShuntType(shuntType);
        workShuntRuleSetting.setShuntSettingRuleId(IdGenUtil.getId());
        workShuntRuleSetting.setShuntRuleType(shuntRuleType);
        workShuntRuleSetting.setCreateTime(LocalDateTime.now());
        workShuntRuleSetting.setSkillId(skillId);
        return workShuntRuleSettingRepository.save(workShuntRuleSetting);
    }


    public List<WorkShuntRuleSetting> queryBySkillIds(List<String> skillIds, String shunRuleType){
        return Option.of(workShuntRuleSettingRepository.queryBy(skillIds, shunRuleType)).getOrElse(ArrayList::new);
    }


    public List<WorkShuntRuleSetting> queryBySkillIds(List<String> skillIds){
        return Option.of(workShuntRuleSettingRepository.queryBy(skillIds)).getOrElse(ArrayList::new);
    }

    public Map<String, List<WorkShuntRuleSetting>> translateListToMap(List<WorkShuntRuleSetting> shuntRuleSettings) {
        return Option.of(shuntRuleSettings)
                .map(list -> list.stream().collect(Collectors.groupingBy(WorkShuntRuleSetting::getSkillId)))
                .getOrElse(HashMap::new);
    }

}
