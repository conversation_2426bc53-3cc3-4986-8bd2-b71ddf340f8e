package com.pxb7.mall.workshunt.domain.model.workSkillTemplate;

import com.pxb7.mall.workshunt.domain.model.workUserSettingSkill.ReqWorkSkillBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReqSaveWorkSkillTemplateBO implements Serializable {
    /**
     * 模版名称
     */
    private String templateTitle;
    /**
     * 设置工作技能
     */
    List<ReqWorkSkillBO> skills;

    /**
     * 是否覆盖接待上限
     */
    private Boolean isOverrideMaxLimit;

}