package com.pxb7.mall.workshunt.domain.mapping.workShuntLog;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.WorkShuntLogPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workShuntLog.ReqPageListWorkShuntLogBO;
import com.pxb7.mall.workshunt.infra.model.workShuntLog.WorkShuntLogPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
* 请求日志记录 -> WorkShuntLog  mapstruct
*/
@Mapper
public interface WorkShuntLogMapping {

    WorkShuntLogMapping INSTANCE = Mappers.getMapper(WorkShuntLogMapping.class);

    WorkShuntLogPageListPO toWorkShuntLogPageListPo(ReqPageListWorkShuntLogBO bo);

    @Mappings({
        @Mapping(source = "pkUserNames",target = "workOutData"),
        @Mapping(source = "skillIds",target = "workSkillId"),
    })
    WorkShuntLogPageListRespDTO toDtoObj(WorkShuntLog page);
    Page<WorkShuntLogPageListRespDTO> toWorkShuntLogPageListRespDTO(Page<WorkShuntLog> page);

}
