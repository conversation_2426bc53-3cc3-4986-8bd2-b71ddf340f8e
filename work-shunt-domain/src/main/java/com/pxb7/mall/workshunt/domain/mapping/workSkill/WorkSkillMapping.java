package com.pxb7.mall.workshunt.domain.mapping.workSkill;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.client.work.response.workSkill.WorkSkillPageListRespDTO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqEditWorkSkillBO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqPageListWorkSkillBO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqSaveWorkSkillBO;
import com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO;
import com.pxb7.mall.workshunt.infra.model.skill.WorkSkillPageListPo;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
* 技能管理 -> WorkSkill  mapstruct
*/
@Mapper
public interface WorkSkillMapping {

    WorkSkillMapping INSTANCE = Mappers.getMapper(WorkSkillMapping.class);

    WorkSkillPageListPo toWorkSkillPageListPo(ReqPageListWorkSkillBO bo);

    Page<WorkSkillPageListRespDTO> toWorkSkillPageListRespDTO(Page<SkillPageListPO> page);

    WorkSkill toReqSaveWorkSkill(ReqSaveWorkSkillBO dto);

    WorkSkill toReqEditWorkSkill(ReqEditWorkSkillBO dto);

}
