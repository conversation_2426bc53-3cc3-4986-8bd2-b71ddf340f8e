package com.pxb7.mall.workshunt.domain.model.workUserSettingSkill;

import com.pxb7.mall.workshunt.infra.model.skill.GamePO;
import lombok.Data;

import java.util.List;

@Data
public class ReqWorkSkillBO {
    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 设置接待上线
     */
    private Long maxLimit;

    /**
     * 接待会话并发上限
     */
    private Long maxSessionLimit;

    private boolean isSkillRelationGame;
    /**
     * 设置游戏
     */
    private List<GamePO> games;

    /**
     * 是否禁用
     */
    private boolean able;
}
