package com.pxb7.mall.workshunt.domain.model.workShuntRuleSetting;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


                        
@Data
public class ReqPageListWorkShuntRuleSettingBO implements Serializable {

    /**
     * 第几页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

/**
* 主键
*/
        private Long id ;
    /**
* 分流类型ID
*/
        private String shuntRuleId ;
    /**
* 分流规则ID
*/
        private String shuntSettingRuleId ;
    /**
* 分流规则类型1非溢出状态2溢出状态
*/
        private String shuntRuleType ;
    /**
* 分流规则1平均分配2比例分配0不分配-1未分配
*/
        private String shuntType ;
    /**
* 创建时间
*/
        private LocalDateTime createTime ;
    }