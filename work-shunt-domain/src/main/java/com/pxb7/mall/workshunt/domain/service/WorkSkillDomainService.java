package com.pxb7.mall.workshunt.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.client.work.response.workSkill.WorkSkillPageListRespDTO;
import com.pxb7.mall.workshunt.domain.mapping.workSkill.WorkSkillMapping;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqPageListWorkSkillBO;
import com.pxb7.mall.workshunt.domain.model.workSkill.ReqSaveWorkSkillBO;
import com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.model.skill.WorkSkillPageListPo;
import com.pxb7.mall.workshunt.infra.repository.db.WorkSkillRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.util.IdGenUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class WorkSkillDomainService {

    @Resource
    private WorkSkillRepository workSkillRepository;
    public Page<WorkSkillPageListRespDTO> listPage(ReqPageListWorkSkillBO dto) {

        WorkSkillPageListPo workSkillPageListPo = WorkSkillMapping.INSTANCE.toWorkSkillPageListPo(dto);
        Page<SkillPageListPO> pageList = Option.of(workSkillRepository.listPage(workSkillPageListPo)).getOrElse(Page::new);
        return WorkSkillMapping.INSTANCE.toWorkSkillPageListRespDTO(pageList);
    }

    public Boolean save(ReqSaveWorkSkillBO bo) {
        bo.setCreateTime(LocalDateTime.now());
        WorkSkill workSkill = WorkSkillMapping.INSTANCE.toReqSaveWorkSkill(bo);
        workSkill.setSkillId(IdGenUtil.getId());
        return workSkillRepository.save(workSkill);
    }

    public Boolean edit(WorkSkill workSkill) {
        return workSkillRepository.updateById(workSkill);
    }

    public Boolean delete(String skillId) {

        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkSkill::getSkillId, skillId);
        return workSkillRepository.remove(lambdaQueryWrapper);
    }

    public WorkSkill get(String skillId) {
        return workSkillRepository.getTableOneDataByParamKey(skillId);
    }

    public Long getSkillCount() {
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkSkill::getDeleted, false);
        return workSkillRepository.count(lambdaQueryWrapper);
    }

    public List<UserSkillAllPO> all() {
        return workSkillRepository.all();
    }

    public List<WorkSkill> list(){
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(WorkSkill::getSkillCode);
        return workSkillRepository.list(lambdaQueryWrapper);
    }

    public List<WorkSkill> queryBySkillCodes(List<String> codes) {
        return Option.when(CollUtil.isNotEmpty(codes), () -> workSkillRepository.queryBySkillCode(codes))
            .getOrElse(ArrayList::new);
    }

    public List<WorkSkill> queryBySkillId(List<String> skillIds) {
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(WorkSkill::getSkillId, skillIds);
        return workSkillRepository.list(lambdaQueryWrapper);
    }

    public WorkSkill queryBySKillCode(String code){
        return workSkillRepository.getTableOneDataByParamKeyCode(code);
    }

    public boolean isExitsSkillStoreInSkillType(String skillId, String skillTypeId, Integer skillSort) {
        return workSkillRepository.isExitsSkillStoreInSkillType(skillId, skillTypeId, skillSort);
    }

    public boolean isExitsSkillStoreInSkillType(String skillTypeId, Integer skillSort) {
        return workSkillRepository.isExitsSkillStoreInSkillType(skillTypeId, skillSort);
    }
}
