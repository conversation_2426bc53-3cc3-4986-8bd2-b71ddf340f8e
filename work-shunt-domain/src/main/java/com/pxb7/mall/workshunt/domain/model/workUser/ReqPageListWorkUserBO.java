package com.pxb7.mall.workshunt.domain.model.workUser;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


                            
@Data
public class ReqPageListWorkUserBO implements Serializable {

    /**
     * 第几页
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

/**
* 主键
*/
        private Long id ;
    /**
* 工作用户ID
*/
        private String workUserId ;
    /**
* 部门ID
*/
        private String pkDeptId ;
    /**
* 用户ID
*/
        private String pkUserId ;
    /**
* 创建时间
*/
        private LocalDateTime createTime ;
    /**
* 部门类型1公司部门2号商部门
*/
        private String deptType ;
    /**
* 号商ID
*/
        private String merchantId ;
    }