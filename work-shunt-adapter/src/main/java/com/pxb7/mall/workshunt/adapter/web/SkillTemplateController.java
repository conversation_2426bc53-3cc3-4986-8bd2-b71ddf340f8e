package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.app.service.WorkSkillTemplateAppService;
import com.pxb7.mall.workshunt.app.service.WorkUserAppService;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workSkillTemplate.WorkUserSkillTemplateSelectListRespDTO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
* 排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/skill/template")
public class SkillTemplateController {

    @Resource
    private WorkSkillTemplateAppService workSkillTemplateAppService;

    @Resource
    private WorkUserAppService workUserAppService;
    /**
     * 技能模板 列表
     */
    @GetMapping("/dropdown")
    public SingleResponse<List<WorkSkillTemplateSelectListRespDTO>> dropDownList() {
        return SingleResponse.of(workSkillTemplateAppService.dropDownList());
    }

    /**
    * 技能模板 保存
    */
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@Valid @RequestBody ReqSaveWorkSkillTemplateDTO dto) {
        return SingleResponse.of(workSkillTemplateAppService.saveWorkSkillTemplate(dto));
    }

    /**
     * 技能模版-编辑
     * @param dto
     * @return
     */
    @PostMapping("/edit")
    public PxResponse<Boolean> edit(@Valid @RequestBody WorkSkillTemplateEditReqDTO dto){
        return PxResponse.ok(workSkillTemplateAppService.edit(dto));
    }


    /**
     *  技能模板 获取详情
     */
    @GetMapping("/detail")
    public SingleResponse<Map<String, List<UserSkillAllPO>>> getDetails(@RequestParam("skillTemplateId") String skillTemplateId) {
        return SingleResponse.of(workSkillTemplateAppService.getDetails(skillTemplateId));
    }

    /**
     * 技能模版 删除
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    public SingleResponse<Boolean> delete(@RequestBody WorkSkillTemplateDeleteDTO dto){
        return SingleResponse.of(workSkillTemplateAppService.deleteTemplate(dto.getTemplateId()));
    }

    /**
     * 人员技能管理:人员复制下拉接口
     * 当前能获取到workUserId就传入
     */
    @GetMapping("/selectUserList")
    public SingleResponse<List<WorkUserSkillTemplateSelectListRespDTO>> selectUserList(@RequestParam(value = "userId", required = false) String userId) {
        List<WorkUserSkillTemplateSelectListRespDTO> outPage = workSkillTemplateAppService.selectUserList(userId);
        return SingleResponse.of(outPage);
    }

    /**
     * 将员工关联到模版
     * @param dto
     * @return
     */
    @PostMapping("/relate/user")
    public SingleResponse<Boolean> relateWorkUser(@Valid @RequestBody WorkTemplateRelateUserReqDTO dto){
        return SingleResponse.of(workSkillTemplateAppService.relateUser(dto));
    }

    /**
     * 清除员工关联的模版
     * @param dto
     * @return
     */
    @PostMapping("/relate/clear")
    public SingleResponse<Boolean> clearWorkUserTemplate(@Valid @RequestBody ReqClearWorkUserTemplateDTO dto){
        return SingleResponse.of(workUserAppService.relateTemplate(dto.getWorkUserId(), null));
    }

}
