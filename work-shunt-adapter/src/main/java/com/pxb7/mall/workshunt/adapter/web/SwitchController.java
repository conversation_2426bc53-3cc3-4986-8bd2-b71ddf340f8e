package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.infra.config.WorkShuntServiceSwitchConfig;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开关接口
 * <AUTHOR>
 * @date 2025/6/4 17:39
 */
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/shunt")
public class SwitchController {

    @Resource
    private WorkShuntServiceSwitchConfig workShuntServiceSwitchConfig;

    /**
     * 服务切换开关,true为切换到新服务
     */
    @GetMapping("/switch-new-service")
    public SingleResponse<String> switchNewService() {
        return SingleResponse.of(workShuntServiceSwitchConfig.getSwitchNew());
    }

}
