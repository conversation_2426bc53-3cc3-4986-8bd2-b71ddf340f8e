package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.adapter.utils.PageResponseUtil;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.app.service.WorkSkillAppService;
import com.pxb7.mall.workshunt.client.work.response.workSkill.WorkSkillPageListRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
* 排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/skill")
public class SkillController {

    @Resource
    private WorkSkillAppService workSkillAppService;

    /**
     * 技能管理
     * 获取技能数量
     */
    @GetMapping("/countNum")
    public SingleResponse<Long> count() {
        return SingleResponse.of(workSkillAppService.getSkillCount());
    }

    /**
    * 技能管理
     * 列表
    */
    @GetMapping("/list")
    public PageResponse<WorkSkillPageListRespDTO> pages(ReqPageListWorkSkillDTO dto) {
        Page<WorkSkillPageListRespDTO> outPage = workSkillAppService.listPage(dto);
        return PageResponseUtil.getDataTable(outPage);
    }

    /**
    * 技能管理 保存
    */
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@Valid @RequestBody SaveWorkSkillReqDTO dto) {
        return SingleResponse.of(workSkillAppService.save(dto));
    }
    /**
     * 技能管理 详情
     */
    @GetMapping("/detail")
    public SingleResponse<WorkSkillDetailRespDTO> detail(@RequestParam("skillId") String skillId) {
        return SingleResponse.of(workSkillAppService.getDetail(skillId));
    }

    /**
    * 技能管理 编辑
    */
    @PostMapping("/edit")
    public SingleResponse<Boolean> edit(@Valid @RequestBody ReqEditWorkSkillDTO dto) {
        return SingleResponse.of(workSkillAppService.edit(dto));
    }
    /**
    * 技能管理 删除
    */
    @PostMapping("/delete")
    public SingleResponse<Boolean> delete(@RequestBody WorkSkillDeleteReqDTO skillId) {
        return SingleResponse.of(workSkillAppService.delete(skillId.getSkillId()));
    }

    /**
     * 技术下拉列表
     * im前端调用
     * @return
     */
    @GetMapping("/dropdown")
    public MultiResponse<SkillDropdownRepDTO> dropdown(){
        return MultiResponse.of(workSkillAppService.dropdown());
    }


}
