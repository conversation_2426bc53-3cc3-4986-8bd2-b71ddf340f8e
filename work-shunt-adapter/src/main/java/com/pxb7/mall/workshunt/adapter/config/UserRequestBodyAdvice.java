package com.pxb7.mall.workshunt.adapter.config;

import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.workshunt.app.model.UserReqDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;
import java.util.Objects;

import static com.pxb7.mall.workshunt.infra.constant.TokenConstants.*;

@ControllerAdvice
public class UserRequestBodyAdvice extends RequestBodyAdviceAdapter {



    @Resource
    private HttpServletRequest request;

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        // 检查请求体的类型是否是 BaseRequest 或其子类
        return UserReqDTO.class.isAssignableFrom(methodParameter.getParameterType());
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
        Class<? extends HttpMessageConverter<?>> converterType) {
        // 从 HttpServletRequest 中获取用户 ID 并注入到请求体对象中

        String userId = "";
        String userName = "";
        if (Objects.nonNull(request.getHeader(ADMIN_TOKEN_KEY)) && StringUtils.isNotBlank(AdminUserUtil.getUserId())) {
            userId = AdminUserUtil.getUserId();
            userName = AdminUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(USER_TOKEN_KEY)) && StringUtils.isNotBlank(
            LoginUserUtil.getUserId())) {
            userId = LoginUserUtil.getUserId();
            userName = LoginUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(IM_TOKEN_KEY)) && StringUtils.isNotBlank(ImUserUtil.getUserId())) {
            userId = ImUserUtil.getUserId();
            userName = ImUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(MERCHANT_TOKEN_KEY)) && StringUtils.isNotBlank(
            MerchantUserUtil.getUserId())) {
            userId = MerchantUserUtil.getUserId();
            userName = MerchantUserUtil.getUserName();
        }
        UserReqDTO userReqDTO = (UserReqDTO)body;
        userReqDTO.setUserId(userId);
        userReqDTO.setUserName(userName);

        return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
    }
}
