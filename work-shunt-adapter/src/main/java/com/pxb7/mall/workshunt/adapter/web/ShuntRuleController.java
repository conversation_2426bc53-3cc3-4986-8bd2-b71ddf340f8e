package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.ShuntRuleSaveDTO;
import com.pxb7.mall.workshunt.app.model.WorkShuntRuleListRespDTO;
import com.pxb7.mall.workshunt.app.service.WorkShuntRuleAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排班分流-admin
 */
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/assign/rule")
public class ShuntRuleController {

    @Resource
    private WorkShuntRuleAppService workShuntRuleAppService;

    /**
     * 17.分流规则-列表
     */
    @GetMapping("/list")
    public SingleResponse<List<WorkShuntRuleListRespDTO>> listWorkShuntRuleWorkShuntRule() {
        List<WorkShuntRuleListRespDTO> outPage = workShuntRuleAppService.listWorkShuntRule();
        return SingleResponse.of(outPage);
    }

    /**
     * 18.分流规则-设置
     */
    @PostMapping("/setting")
    public SingleResponse setting(@Valid @RequestBody ShuntRuleSaveDTO dto){
        return SingleResponse.of(workShuntRuleAppService.save(dto));
    }

}
