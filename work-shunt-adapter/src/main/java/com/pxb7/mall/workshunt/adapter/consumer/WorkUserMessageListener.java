package com.pxb7.mall.workshunt.adapter.consumer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workshunt.domain.service.UserSettingSkillDomainService;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserRepository;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = ConsumerConstants.USER_EVENT_GROUP,
        topic = ConsumerConstants.USER_EVENT_TOPIC,
        tag = ConsumerConstants.USER_EVENT_TAG)
public class WorkUserMessageListener implements RocketMQListenerExt<SysUserEvent> {

    private List<Integer> activte = List.of(1,2,5);

    private List<Integer> noActivte = List.of(4);

    private static final String ACTIVATE = "1";

    private static final String NOT_ACTIVATE = "0";

    private static final int DELETED  = 3;

    @Resource
    private WorkUserRepository workUserRepository;

    @Resource
    private UserSettingSkillDomainService userSettingSkillDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView, SysUserEvent o) {

        return Try.of(()-> {
            log.info("consumer sys user info status: {}", JSON.toJSONString(o));
            if(activte.contains(o.getType())){
                workUserRepository.updateWorkUserActivity(o.getSysUserId(), ACTIVATE);
            }
            if (noActivte.contains(o.getType())) {
                workUserRepository.updateWorkUserActivity(o.getSysUserId(), NOT_ACTIVATE);
            }
            if (o.getType() == DELETED) { // delete
                WorkUser workUser = workUserRepository.getTableOneDataByPkUserId(o.getSysUserId());
                if (Objects.isNull(workUser)) {
                    log.warn("WorkUserMessageListener work user is null, userId : {}", o.getSysUserId());
                    return ConsumeResult.SUCCESS;
                }
                List<WorkUserSettingSkill> skills = userSettingSkillDomainService.queryAllByWorkUserId(workUser.getWorkUserId());
                if (!CollUtil.isEmpty(skills)) {
                    //remove user_skill_setting old data
                    List<String> settingSkillIds = skills.stream().map(WorkUserSettingSkill::getSettingSkillId).toList();
                    userSettingSkillDomainService.removeRelationGameBySkillIds(settingSkillIds);
                    userSettingSkillDomainService.removeBatch(skills);
                }
                workUserRepository.deleteWorkUser(o.getSysUserId());
            }
            return ConsumeResult.SUCCESS;
        }).getOrElseGet((e)-> {
            log.error("消费用户状态失败: {}",e.getMessage());
            return ConsumeResult.FAILURE;
        });

    }
}
