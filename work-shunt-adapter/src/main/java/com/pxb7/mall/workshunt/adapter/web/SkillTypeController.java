package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.WorkSkillTypeSelectListRespDTO;
import com.pxb7.mall.workshunt.app.service.WorkSkillTypeAppService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* 排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/skill/type")
public class SkillTypeController {

    @Resource
    private WorkSkillTypeAppService workSkillTypeAppService;

    /**
    * 技能类型-下拉列表
    */
    @GetMapping("/dropdown")
    public SingleResponse<List<WorkSkillTypeSelectListRespDTO>> selectList() {
        List<WorkSkillTypeSelectListRespDTO> list = workSkillTypeAppService.selectList();
        return SingleResponse.of(list);
    }

}
