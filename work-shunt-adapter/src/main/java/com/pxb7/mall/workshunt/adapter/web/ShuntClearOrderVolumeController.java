package com.pxb7.mall.workshunt.adapter.web;

import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/clear")
public class ShuntClearOrderVolumeController {

    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;
    @GetMapping("/volume")
    public String clearOrderVolume() {
        workShuntOrderVolumeRepository.cleanOrderVolume();
        return "clear";
    }
}
