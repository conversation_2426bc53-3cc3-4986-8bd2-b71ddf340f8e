package com.pxb7.mall.workshunt.adapter.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.workshunt.app.model.WorkSkillBindGameReqDTO;
import com.pxb7.mall.workshunt.app.service.UserSettingSkillAppService;
import com.pxb7.mall.workshunt.app.service.WorkSkillAppService;
import com.pxb7.mall.workshunt.client.api.WorkSkillServiceI;
import com.pxb7.mall.workshunt.client.dto.request.QuerySkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QuerySkillReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillGameRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillRepDTO;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@DubboService
public class WorkSKillStubService implements WorkSkillServiceI {

    @Resource
    private WorkSkillAppService workSkillAppService;

    @Resource
    private UserSettingSkillAppService userSettingSkillAppService;


    @Override
    public MultiResponse<SkillRepDTO> querySkill(QuerySkillReqDTO dto) {
        return MultiResponse.of(Option.when(CharSequenceUtil.isNotBlank(dto.getUserId()), () -> {
            List<WorkUserSettingSkillPageListRespDTO> settingSkillPageListRespDTOS = userSettingSkillAppService.getWorkUserSettingSkillByPkUserId(dto.getUserId());
            return Option.when(CollUtil.isNotEmpty(settingSkillPageListRespDTOS), () ->
                filterSkillRelGameFromQueryUserId(settingSkillPageListRespDTOS, dto.getGameIds()).stream()
                    .map(f -> SkillRepDTO.builder()
                        .name(f.getWorkSkillName())
                        .code(f.getSkillCode())
                        .relateGame(f.getSkillRelationName())
                        .build())
                    .toList()
            ).getOrElse(ArrayList::new);
        }).getOrElse(filterSkillRelGameFromQueryAllSkill(dto.getGameIds())));
    }

    private List<WorkUserSettingSkillPageListRespDTO> filterSkillRelGameFromQueryUserId(List<WorkUserSettingSkillPageListRespDTO> settingSkillPageListRespDTOS,
                                                                 List<String> gameIds) {
        return settingSkillPageListRespDTOS.stream().filter(f-> predicateGame(f.getGameDTOList() == null ? null : f.getGameDTOList().stream().collect(Collectors.toSet()), gameIds)).toList();
    }

    private List<SkillRepDTO> filterSkillRelGameFromQueryAllSkill(List<String> gameIds){
        return CollUtil.isEmpty(gameIds) ? workSkillAppService.selectAllSKills() : workSkillAppService.queryWorkSkillBindGame(gameIds) ;
    }

    private List<SkillRepDTO> filterGame(List<String> gameId) {
        List<WorkSkillBindGameReqDTO> workSkillBindGameReqDTOS = workSkillAppService.queryAllWorkSkillBindGame();
        return Option.when(CollUtil.isNotEmpty(workSkillBindGameReqDTOS), ()-> {
            return workSkillBindGameReqDTOS.stream().filter(f-> predicateGame(f.getGameDTOS(), gameId)).map(m-> {
                return SkillRepDTO.builder().name(m.getSkillName()).code(m.getCode()).relateGame(m.isRelateGame()).build();
            }).toList();
        }).getOrElse(ArrayList::new);
    }

    private boolean predicateGame(Set<GameDTO> gameDTOList, List<String> gameIds){
        if (CollUtil.isEmpty(gameIds)) {
            return true;
        }
        return CollUtil.isEmpty(gameDTOList)
            ? false
            : gameDTOList.stream().filter(f -> CharSequenceUtil.isNotBlank(f.getGameId()) && gameIds.contains(f.getGameId())).count() > 0;
    }

    @Override
    public MultiResponse<SkillGameRepDTO> queryGameByBindSkill(QuerySkillGameReqDTO dto) {
        return MultiResponse.of(filterQueryGameByBindSkill(dto.getCode(), dto.getGameIds()));
    }

    private Set<SkillGameRepDTO> filterQueryGameByBindSkill(String code, List<String> gameIds) {
        Set<SkillGameRepDTO> result =  workSkillAppService.queryWorkSkillBindGameBySkillCode(code);
        return CollUtil.isEmpty(gameIds) ? result : result.stream().filter(f-> gameIds.contains(f.getId())).collect(Collectors.toSet());
    }
}
