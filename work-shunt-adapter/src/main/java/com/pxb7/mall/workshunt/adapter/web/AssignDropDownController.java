package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.ReqWorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.app.model.WorkUserDropdownDeptInfoRespDTO;
import com.pxb7.mall.workshunt.app.service.DropdownAppService;
import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 排班分流-admin
 */

@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/assign/dropdown")
public class AssignDropDownController {

    @Autowired
    private DropdownAppService dropdownAppService;


    /**
     * 排班管理-部门下拉列表数据
     */
    @GetMapping("/depts")
    public MultiResponse<WorkUserDropdownDeptInfoRespDTO> selectDeptList() {
        return MultiResponse.of(dropdownAppService.deptDropdown());
    }


    /**
     * 排班用户 获取部门用户
     */
    @PostMapping("/users")
    public MultiResponse<WorkUserSelectDeptUserRespDTO> selectDeptUserList(@RequestBody ReqWorkUserSelectDeptUserRespDTO dto) {
        return MultiResponse.of(dropdownAppService.userDropdown(dto));
    }

    /**
     * 技能关联的游戏
     * @param skillId
     * @return
     */
    public MultiResponse<GameDTO> selectGameList(@RequestParam("skillId") String skillId) {
        return MultiResponse.of(dropdownAppService.gameDropdown(skillId));
    }
}
