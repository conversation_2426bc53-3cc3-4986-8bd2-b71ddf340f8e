package com.pxb7.mall.workshunt.adapter.utils;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.Serializable;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PageResponseUtil implements Serializable {

    public static PageResponse getDataTable(Page page) {

        return PageResponse.of(page.getRecords(), (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }

    public static <T, P> PageResponse<T> getDataTable(Page<P> page, Function<P, T> mapFunc) {
        List<T> collect = page.getRecords().stream().map(mapFunc).collect(Collectors.toList());
        return PageResponse.of(collect, (int)page.getTotal(), (int)page.getSize(), (int)page.getCurrent());
    }
}
