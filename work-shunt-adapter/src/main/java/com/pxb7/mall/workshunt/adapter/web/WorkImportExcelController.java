package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.WorkErrorMessage;
import com.pxb7.mall.workshunt.app.service.WorkImportAppService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 临时导入excel接口
 *
 */
@RestController
@SaAdminCheckLogin
@RequestMapping("/admin/web/work/import")
public class WorkImportExcelController {

    @Resource
    private WorkImportAppService workImportAppService;

    @GetMapping("")
    public SingleResponse<Boolean> importExcel() {
        return SingleResponse.of(workImportAppService.importExcel());
    }

    public MultiResponse<WorkErrorMessage> query(){
        return MultiResponse.of(workImportAppService.getErrorMessages());
    }
}
