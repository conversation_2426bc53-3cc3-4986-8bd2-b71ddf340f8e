package com.pxb7.mall.workshunt.adapter.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.workshunt.app.service.*;
import com.pxb7.mall.workshunt.client.api.ShuntSkillServiceI;
import com.pxb7.mall.workshunt.client.dto.request.GetSkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryOnlineWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.WorkUserOnlineRespDTO;
import com.pxb7.mall.workshunt.client.work.BatchDeduceOrderVolumeReqDTo;
import com.pxb7.mall.workshunt.client.work.DeduceOrderVolume;
import com.pxb7.mall.workshunt.client.work.GetWorkVerifyLogIdDubboDTO;
import com.pxb7.mall.workshunt.client.work.response.GetSkillGameRespDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ReqWorkShuntGroupDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ValidationWorkerReqDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqAddGroupWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGroupTransferShuntShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqVerifyLogIdDTO;
import com.pxb7.mall.workshunt.infra.config.WorkShuntBlacklistConfig;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.*;
import java.util.stream.Collectors;

@DubboService
public class ShuntSkillDubboService implements ShuntSkillServiceI {

    @Resource
    private ShuntAppService shuntAppService;

    @Resource
    private ShuntLogAppService shuntLogAppService;

    @Resource
    private WorkUserAppService workUserAppService;


    @Resource
    private UserSettingSkillAppService workUserSettingSkillAppService;

    @Resource
    private ShuntValidationService shuntValidationService;

    @Resource
    private WorkShuntBlacklistConfig workShuntBlacklistConfig;



    @Override
    public MultiResponse<GetWorkShuntRespDTO> shunt(ReqGetWorkShuntDTO reqDTO) {
        filterTestUsers(reqDTO);
        return MultiResponse.of(shuntAppService.shunt(reqDTO));
    }

    public void filterTestUsers(ReqGetWorkShuntDTO reqDTO) {
        if (Objects.nonNull(reqDTO.getGameId())
                || workShuntBlacklistConfig.getFilterUsers().equals(WorkShuntBlacklistConfig.DEFAULT_NULL)) {
            return;
        }
        String nacosConfig = workShuntBlacklistConfig.getFilterUsers();
        Set<String> filterUsers = reqDTO.getFilterUserIds();
        if (CollUtil.isEmpty(filterUsers)) {
            filterUsers = new HashSet<String>();
        }
        filterUsers.addAll(new HashSet<>(Arrays.asList(nacosConfig.split(","))));
        reqDTO.setFilterUserIds(filterUsers);
    }
    
    @Override
    public SingleResponse<Boolean> verifyDetailsId(ReqVerifyLogIdDTO dto) {
        return  SingleResponse.of(shuntLogAppService.confirmWorkload(dto));
    }

    @Override
    public SingleResponse<Boolean> closeDetailsLogId(GetWorkVerifyLogIdDubboDTO dto) {
        return SingleResponse.of(shuntLogAppService.dubboCloseDetailsLogId(dto));
    }

    @Override
    public SingleResponse<Boolean> batchDeduceOrderVolume(BatchDeduceOrderVolumeReqDTo dto) {
        Optional<List<DeduceOrderVolume>> dtoList = Optional.ofNullable(dto.getDeduces());
        if(dtoList.isPresent()) {
            List<String> skillCodes = dtoList.get().stream().map(DeduceOrderVolume::getSkillCode).toList();
            List<String> userIds = dtoList.get().stream().map(DeduceOrderVolume::getUserId).toList();
            shuntLogAppService.batchMinutesWorkVolumes(skillCodes, userIds);

            return SingleResponse.buildSuccess();
        }
        return SingleResponse.buildFailure("","注意传参数. 煜晟");
    }

    @Override
    public SingleResponse<GetWorkShuntRespDTO> addWorkloadToUser(ReqAddGroupWorkShuntDTO dto) {
        return SingleResponse.of(shuntLogAppService.addWorkloadToUser(dto));
    }

    @Override
    public SingleResponse<GetWorkShuntRespDTO> groupTransferShunt(ReqGroupTransferShuntShuntDTO dto) {
        return SingleResponse.of(shuntLogAppService.groupTransferShunt(dto));
    }

    @Override
    public MultiResponse<WorkUserDeptUserRespDTO> getSkillWorkUserByCodeAndGameId(ReqWorkShuntGroupDTO reqWorkShuntGroupDTO) {
        List<WorkUserDeptUserRespDTO> outPage = workUserAppService.getSkillWorkUserByCodeAndGameId(reqWorkShuntGroupDTO);
        return MultiResponse.of(outPage);
    }

    @Override
    public MultiResponse<WorkUserSelectDeptUserRespDTO> getSkillWorkUserByCode(String skillCode) {
        List<WorkUserSelectDeptUserRespDTO> outPage = workUserSettingSkillAppService.getSkillWorkUserByCode(skillCode);
        return MultiResponse.of(outPage);
    }

    @Override
    public MultiResponse<WorkUserOnlineRespDTO> getOnlineWorkUserBy(QueryOnlineWorkUserReqDTO dto) {
        return MultiResponse.of(workUserSettingSkillAppService.getOnlineWorkUser(dto));
    }

    @Override
    public MultiResponse<WorkUserSettingSkillPageListRespDTO> getWorkUserSettingSkillByPkUserId(String pkUserId) {
        List<WorkUserSettingSkillPageListRespDTO> outPage = workUserSettingSkillAppService.getWorkUserSettingSkillByPkUserId(pkUserId);
        return MultiResponse.of(outPage);
    }

    @Override
    public MultiResponse<GetWorkShuntRespDTO> validationWorker(ValidationWorkerReqDTO dto) {
        return MultiResponse.of(shuntValidationService.validationWorkUser(dto));
    }

    @Override
    public MultiResponse<GetSkillGameRespDTO> querySkillGame(GetSkillGameReqDTO dto) {
        return MultiResponse.of(workUserSettingSkillAppService.getSkillRelatedGames(dto));
    }
}
