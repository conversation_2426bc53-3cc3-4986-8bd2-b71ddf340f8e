package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.service.ShuntSearchWorkUserAppService;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserByNoOverFlowReqDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SaAdminCheckLogin
@RestController
@RequestMapping("/web/work/shunt")
public class ShuntSearchWorkUserController {

    @Resource
    private ShuntSearchWorkUserAppService shuntSearchWorkUserAppService;

    @PostMapping("/search/noOverflow")
    public MultiResponse<GetWorkShuntRespDTO> queryByNoOverflowCondition(@RequestBody QueryWorkUserByNoOverFlowReqDTO dto) {
        return MultiResponse.of(shuntSearchWorkUserAppService.queryWorkUsersByOnOverflowCondition(dto));
    }
}
