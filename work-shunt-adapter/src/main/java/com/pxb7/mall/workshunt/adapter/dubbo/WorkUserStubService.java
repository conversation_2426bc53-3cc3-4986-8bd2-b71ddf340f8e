package com.pxb7.mall.workshunt.adapter.dubbo;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.workshunt.app.service.ShuntSearchWorkUserAppService;
import com.pxb7.mall.workshunt.app.service.WorkUserAppService;
import com.pxb7.mall.workshunt.client.api.WorkUserServiceI;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserByNoOverFlowReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.QueryWorkUserRepDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

@DubboService
public class WorkUserStubService implements WorkUserServiceI {

    @Resource
    private WorkUserAppService workUserAppService;
    
    @Resource
    private ShuntSearchWorkUserAppService shuntSearchWorkUserAppService;
    
    /**
     * This method is used to retrieve a list of merchant work users based on the provided request parameters.
     *
     * @param dto The request object containing the necessary parameters to query work users. It is expected to have the
     *            following fields: - field1: Description of field1 - field2: Description of field2 - ...
     * @return A list of {@link QueryWorkUserRepDTO} objects representing the work users that match the given criteria.
     * If no users are found, an empty list is returned. The returned list may contain the following fields: - field1:
     * Description of field1 - field2: Description of field2 - ...
     * @throws IllegalArgumentException If the input parameters are invalid.
     * @throws IllegalStateException    If the system is in an invalid state for performing the operation.
     * @throws Exception                If any other unexpected error occurs during the operation.
     */
    @Override
    public MultiResponse<QueryWorkUserRepDTO> queryMerchantWorkUser(QueryWorkUserReqDTO dto) {
        return MultiResponse.of(workUserAppService.queryMerchantWorkUser(dto));
    }

        /**
     * Queries work users based on no-overflow conditions.
     * 
     * This method retrieves a list of work users that meet specific no-overflow criteria
     * as defined in the input DTO. It's useful for scenarios where you need to find
     * users who haven't exceeded certain thresholds or limits.
     *
     * @param dto The request object containing the necessary parameters to query work users
     *            based on no-overflow conditions. It should include criteria that define
     *            what constitutes a "no overflow" state for a work user.
     * @return A MultiResponse containing a list of GetWorkShuntRespDTO objects. Each object
     *         represents a work user that meets the no-overflow criteria. If no users are found,
     *         an empty list is returned within the MultiResponse.
     * @throws IllegalArgumentException If the input DTO is null or contains invalid parameters.
     * @throws IllegalStateException If the system is unable to process the request due to its current state.
     * @throws Exception If any other unexpected error occurs during the query process.
     */
    @Override
    public MultiResponse<GetWorkShuntRespDTO> queryWorkUserByNoOverflowCondition(QueryWorkUserByNoOverFlowReqDTO dto) {
        return MultiResponse.of(shuntSearchWorkUserAppService.queryWorkUsersByOnOverflowCondition(dto));
    }
}
