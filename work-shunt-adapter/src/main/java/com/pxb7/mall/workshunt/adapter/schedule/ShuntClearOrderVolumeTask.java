package com.pxb7.mall.workshunt.adapter.schedule;

import com.pxb7.mall.workshunt.infra.repository.db.WorkShuntOrderVolumeRepository;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 * 清除人员的接单量
 */
@Service
@Slf4j
public class ShuntClearOrderVolumeTask  implements BasicProcessor {

    @Resource
    private WorkShuntOrderVolumeRepository workShuntOrderVolumeRepository;
    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        log.info("Processing clear order volume :{}");
        Try<Void> voidTry = Try.run(()-> workShuntOrderVolumeRepository.cleanOrderVolume());
        if (voidTry.isFailure()) {
            log.error("清除接单量任务执行失败:{}", voidTry.getCause().getMessage());
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
}
