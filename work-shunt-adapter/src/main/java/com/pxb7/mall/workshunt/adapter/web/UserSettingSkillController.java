package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingSkillDTO;
import com.pxb7.mall.workshunt.app.model.WorkSettingMaxLimitDTO;
import com.pxb7.mall.workshunt.app.service.UserSettingSkillAppService;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
*  排班分流-admin
*/
@SaAdminCheckLogin
@Validated
@RestController
@RequestMapping("/admin/web/work/user/setting/skill")
public class UserSettingSkillController {

    @Resource
    private UserSettingSkillAppService workUserSettingSkillAppService;


    /**
     *
     * 用户技能-按照技能分类(列表)
     * @return
     */
    @GetMapping("/group/type")
    public SingleResponse<Map<String, List<UserSkillAllPO>>> querySkillGroupByTypeQueryByUserId(@RequestParam(name ="userId", required = false) String userId){
        return SingleResponse.of(workUserSettingSkillAppService.queryUserSkillsGroupBySkillType(userId));
    }

    /**
    * 用户技能-保存
    */
    @PostMapping("")
    public SingleResponse<Void> saveWorkUserSettingSkill( @Valid @RequestBody ReqSaveWorkUserSettingSkillDTO dto) {
        workUserSettingSkillAppService.save(dto);
        return SingleResponse.buildSuccess();
    }

}
