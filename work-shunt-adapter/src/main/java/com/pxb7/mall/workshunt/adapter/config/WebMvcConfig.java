package com.pxb7.mall.workshunt.adapter.config;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private UserTokenInterceptor userTokenInterceptor;

    @Resource
    private ClientTypeInterceptor clientTypeInterceptor;

    /**
     * 跨域cors配置
     * @param registry 跨域注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOriginPatterns("*").allowCredentials(true).allowedMethods("GET", "POST", "DELETE", "PUT")
            .maxAge(3600);
    }

    /**
     * 添加拦截器
     * @param registry 拦截注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userTokenInterceptor).addPathPatterns("/**");
        registry.addInterceptor(clientTypeInterceptor).addPathPatterns("/**");

    }
}


