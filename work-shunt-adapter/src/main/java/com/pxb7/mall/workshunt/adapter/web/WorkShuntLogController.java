package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.adapter.utils.PageResponseUtil;
import com.pxb7.mall.workshunt.app.service.ShuntLogAppService;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.WorkShuntLogPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqPageListWorkShuntLogDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/workShuntLog")
public class WorkShuntLogController {

    @Resource
    private ShuntLogAppService workShuntLogAppService;

    /**
    * 请求日志记录 列表
    */
    @GetMapping("/list")
    public PageResponse<WorkShuntLogPageListRespDTO> listPageWorkShuntLogWorkShuntLog(@Validated ReqPageListWorkShuntLogDTO dto) {
        Page<WorkShuntLogPageListRespDTO> outPage = workShuntLogAppService.listPageWorkShuntLog(dto);
        return PageResponseUtil.getDataTable(outPage);
    }
}
