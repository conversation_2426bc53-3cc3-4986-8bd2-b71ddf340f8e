package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.ReqClearSkillSettingDTO;
import com.pxb7.mall.workshunt.app.model.ReqListDeptWorkUserDTO;
import com.pxb7.mall.workshunt.app.service.WorkUserAppService;
import com.pxb7.mall.workshunt.domain.model.WorkUserPageListRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/assign")
public class UserAssignController {

    @Resource
    private WorkUserAppService workUserAppService;

    /**
    * 排班用户 列表
    */
    @PostMapping("/users")
    public MultiResponse<WorkUserPageListRespDTO> listDeptUserList(@Valid @RequestBody ReqListDeptWorkUserDTO dto) {
        return MultiResponse.of(workUserAppService.listWorkUser(dto));
    }

    /**
     * 排班用户 清除设置的技能
     * @param dto
     * @return
     */
    @PostMapping("/clear")
    public SingleResponse<Boolean> clearSkills(@Valid @RequestBody ReqClearSkillSettingDTO dto) {
        return SingleResponse.of(workUserAppService.clearWorkUserSkills(dto.getWorkUserId()));
    }
}

