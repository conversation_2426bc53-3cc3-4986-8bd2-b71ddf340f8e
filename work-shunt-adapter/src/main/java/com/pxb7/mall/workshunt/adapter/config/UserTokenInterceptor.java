package com.pxb7.mall.workshunt.adapter.config;

import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.workshunt.app.model.UserReqDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Objects;

import static com.pxb7.mall.workshunt.infra.constant.TokenConstants.*;


@Component
public class UserTokenInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 解析 token 并获取 userId 和 userName
        String userId = "";
        String userName = "";
        if (Objects.nonNull(request.getHeader(ADMIN_TOKEN_KEY)) && StringUtils.isNotBlank(AdminUserUtil.getUserId())) {
            userId = AdminUserUtil.getUserId();
            userName = AdminUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(USER_TOKEN_KEY)) && StringUtils.isNotBlank(
            LoginUserUtil.getUserId())) {
            userId = LoginUserUtil.getUserId();
            userName = LoginUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(IM_TOKEN_KEY)) && StringUtils.isNotBlank(ImUserUtil.getUserId())) {
            userId = ImUserUtil.getUserId();
            userName = ImUserUtil.getUserName();
        } else if (Objects.nonNull(request.getHeader(MERCHANT_TOKEN_KEY)) && StringUtils.isNotBlank(
            MerchantUserUtil.getUserId())) {
            userId = MerchantUserUtil.getUserId();
            userName = MerchantUserUtil.getUserName();
        }
        UserReqDTO userReqDTO = new UserReqDTO();
        userReqDTO.setUserId(userId);
        userReqDTO.setUserName(userName);
        request.setAttribute("user", userReqDTO);

        return true;
    }
}
