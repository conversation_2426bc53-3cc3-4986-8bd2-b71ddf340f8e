package com.pxb7.mall.workshunt.adapter.web;

import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workshunt.adapter.schedule.ShuntScheduleInfoTask;
import com.pxb7.mall.workshunt.app.model.ShuntScheduleDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/scheduler")
public class WorkShuntScheduleController {

    @Resource
    private ShuntScheduleInfoTask shuntScheduleInfoTask;

    @GetMapping("")
    public PxResponse<ShuntScheduleDTO> schedule() {
        shuntScheduleInfoTask.task();
        return PxResponse.ok();
    }
}
