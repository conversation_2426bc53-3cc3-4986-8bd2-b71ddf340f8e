package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.PageResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.workshunt.adapter.utils.PageResponseUtil;
import com.pxb7.mall.workshunt.app.model.WorkSettingMaxLimitDTO;
import com.pxb7.mall.workshunt.app.service.WorkDataBoardAppService;
import com.pxb7.mall.workshunt.client.work.databoard.ReqListDataBoardDeptWorkUserDTO;
import com.pxb7.mall.workshunt.client.work.response.dataBoard.WorkUserDataBoardPageListRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 排班分流-admin
 */
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/data/board")
public class WorkDataBoardController {

    @Resource
    private WorkDataBoardAppService workDataBoardAppService;

    /**
     * 工单流-工作饱和度数据看板
     */
    @PostMapping("/list")
    public PageResponse<List<WorkUserDataBoardPageListRespDTO>> listDeptUserList(@Valid @RequestBody ReqListDataBoardDeptWorkUserDTO dto) {
        return PageResponseUtil.getDataTable(workDataBoardAppService.listWorkUser(dto));
    }


    /**
     * 工单流-工作饱和度数据看板：设置房间/会话最大限制数
     */
    @PostMapping("/max-limit-count")
    public PxResponse<Boolean> editMaxLimitCount(@Valid @RequestBody WorkSettingMaxLimitDTO dto) {
        workDataBoardAppService.updateUserSessionMaxLimit(dto);
        return PxResponse.ok();
    }
}
