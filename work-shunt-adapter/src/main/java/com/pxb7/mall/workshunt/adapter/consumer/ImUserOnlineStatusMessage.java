package com.pxb7.mall.workshunt.adapter.consumer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImUserOnlineStatusMessage implements Serializable {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 客服状态 1:在线 2:离线 3: 暂离
     */
    private Integer status;
}
