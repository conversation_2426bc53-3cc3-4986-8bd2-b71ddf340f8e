package com.pxb7.mall.workshunt.adapter.consumer;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workshunt.domain.model.UserModifyDepartmentDTO;
import com.pxb7.mall.workshunt.domain.service.WorkUserDomainService;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import static com.pxb7.mall.workshunt.adapter.consumer.ConsumerConstants.TAG_USER_MODIFY_DEPARTMENT;

/**
 * <p>功能描述:</p>
 * 作者：rookie
 * 创建日期：2025/08/28
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = ConsumerConstants.CID_USER_MODIFY_DEPARTMENT,
        topic = ConsumerConstants.USER_EVENT_TOPIC,
        tag = TAG_USER_MODIFY_DEPARTMENT)
public class UserDepartmentModifyListener implements RocketMQListenerExt<UserModifyDepartmentDTO> {

    @Resource
    private WorkUserDomainService workUserDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView, UserModifyDepartmentDTO userModifyDepartmentDTO) {
        log.info("用户部门修改事件消费开始，消息ID：{}，消息内容：{}", messageView.getMessageId(), userModifyDepartmentDTO);
        Try.run(()-> workUserDomainService.modifyWorkUserDepartment(userModifyDepartmentDTO))
                .onFailure(e->log.error("用户部门修改事件消费失败，消息ID：{}，消息内容：{}", messageView.getMessageId(), userModifyDepartmentDTO,e));
        return ConsumeResult.SUCCESS;
    }
}
