package com.pxb7.mall.workshunt.adapter.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import static com.pxb7.mall.workshunt.infra.constant.HeaderConstants.CLIENT_TYPE_KEY;


@Slf4j
@Component
public class ClientTypeInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 获取客户端类型
        Integer clientType = null;
        if (StringUtils.isNotBlank(request.getHeader(CLIENT_TYPE_KEY))) {
            try {
                clientType = Integer.valueOf(request.getHeader(CLIENT_TYPE_KEY));
            } catch (Exception e) {
                log.warn("请求头client_type参数值不支持 clientType:{}", request.getHeader(CLIENT_TYPE_KEY));
            }
        }
        request.setAttribute("clientType", clientType);
        return true;
    }
}
