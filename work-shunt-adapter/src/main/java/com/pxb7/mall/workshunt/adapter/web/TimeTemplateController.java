package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.adapter.utils.PageResponseUtil;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.app.service.WorkTimeTemplateAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
*  排班分流-admin
*/
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/time/template")
public class TimeTemplateController {

    @Resource
    private WorkTimeTemplateAppService workTimeTemplateAppService;

    @GetMapping("/dropdown")
    public SingleResponse dropdown() {
        return SingleResponse.of(workTimeTemplateAppService.dropdown());
    }

    /**
    * 班次模版-列表
    */
    @PostMapping("/list")
    public PageResponse<TimeTemplatePageListRespDTO> listPage(@RequestBody ReqPageListWorkTimeTemplateDTO dto) {
        Page<TimeTemplatePageListRespDTO> outPage = workTimeTemplateAppService.listPage(dto);
        return PageResponseUtil.getDataTable(outPage);
    }

    /**
     * 班次模版-保存
     */
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@Valid  @RequestBody WorkTimeTemplateSaveReqDTO dto) {
        return SingleResponse.of(workTimeTemplateAppService.save(dto));
    }

    /**
    * 班次模版-编辑
    */
    @PostMapping("/edit")
    public SingleResponse<Boolean> edit(@Valid @RequestBody ReqEditWorkTimeTemplateDTO dto) {
        return SingleResponse.of(workTimeTemplateAppService.edit(dto));
    }

    /**
    * 班次模版-删除
    */
    @PostMapping("/delete")
    public SingleResponse<Boolean> delete(@RequestBody @Validated TimeTemplateDeleteReqDTO dto) {
        return SingleResponse.of(workTimeTemplateAppService.delete(dto.getTimeTemplateId()));
    }
}
