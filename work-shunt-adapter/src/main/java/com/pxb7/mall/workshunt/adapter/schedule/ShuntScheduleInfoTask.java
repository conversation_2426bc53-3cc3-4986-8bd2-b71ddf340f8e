package com.pxb7.mall.workshunt.adapter.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcel;
import com.pxb7.mall.common.client.request.message.BotMessageTextReqDTO;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.workshunt.app.model.*;
import com.pxb7.mall.workshunt.app.service.OssAppService;
import com.pxb7.mall.workshunt.app.service.ShuntScheduleDataAppService;
import com.pxb7.mall.workshunt.infra.config.ShuntScheduleConfig;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.common.MessageGateway;
import com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.product.ProductCenterGateway;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * 排班调度
 */
@Service
@Slf4j
public class ShuntScheduleInfoTask implements BasicProcessor {



    @Resource
    private ProductCenterGateway productCenterGateway;

    @Resource
    private MessageGateway messageGateway;

    @Resource
    private OssAppService ossAppService;

    @Resource
    private ShuntScheduleDataAppService shuntScheduleDataAppService;

    @Resource
    private ShuntScheduleConfig shuntScheduleConfig;

    private static final List<String> noHiddenGameRelateSkill = List.of("SPJD", "SPMG","ZHJY","ZHJF","ZXDDJF","SPGJ");


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        log.info("process ShuntScheduleAppService");
        boolean success = false;
        try {
            task();
            success = true;
        }catch (Exception e) {
            log.error("process ShuntScheduleAppService error",e);
        }
        return new ProcessResult(success);
    }


    public void task() {
        List<ShuntScheduleDTO> shuntScheduleDTOList = shuntScheduleDataAppService.statisticsShunt();
        if (CollUtil.isEmpty(shuntScheduleDTOList)) {
            log.info("统计排班数据不存在");
            return;
        }

        CompletableFuture<String> mainContent = mainContent(shuntScheduleDTOList);
        CompletableFuture<String> ossContent = ossContent(shuntScheduleDTOList);

        StringBuffer sb = new StringBuffer();
        try {
            String main = mainContent.get();
            String oss = ossContent.get();
            if (CharSequenceUtil.isBlank(main) || CharSequenceUtil.isBlank(oss)) {
                log.info("文本排班数据不存在");
                return;
            }
            sb.append(main).append(oss);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        BotMessageTextReqDTO botMessageTextReqDTO = new BotMessageTextReqDTO();
        botMessageTextReqDTO.setText(sb.toString());
        botMessageTextReqDTO.setWebhook(shuntScheduleConfig.getFeiShuRebootUrl());
        messageGateway.botTextMessage(botMessageTextReqDTO);
    }

    private CompletableFuture<String> mainContent(List<ShuntScheduleDTO> shuntScheduleDTOList) {
        return CompletableFuture.supplyAsync(() -> {

            int gamesSize = shangjiaGame()._1;
            List<String> gameNames = shangjiaGame()._2;

            StringBuffer stringBuffer = new StringBuffer();
            LocalTime nowTime = LocalTime.now();
            stringBuffer.append(nowTime.getHour()).append(":").append(nowTime.getMinute() < 10 ? "0" + nowTime.getMinute(): nowTime.getMinute()).append("排班通知:").append("\n");

            shuntScheduleDTOList.forEach(shunt->{
                List<ShuntScheduleUserDTO> dtos = shunt.getUsers();
                int onlineCount = onlineStatus(dtos, true);
                int offlineCount = onlineStatus(dtos, false);
                Set<String> setGame = new HashSet<String>();
                if (CollUtil.isNotEmpty(dtos)) {
                    dtos.forEach(d->{
                        List<ShuntScheduleGameDTO> gameDTOS = d.getGames();
                        if (CollUtil.isNotEmpty(gameDTOS)) {
                            gameDTOS.forEach(gameDTO -> {
                                if (gameNames.contains(gameDTO.getGameName())) {
                                    setGame.add(gameDTO.getGameName());
                                }
                            });
                        }
                    });
                }

                int gameCount = dtos == null ? 0 : setGame.size();
                stringBuffer.append(ShuntScheduleSendMessageDTO.builder()
                    .skillName(shunt.getSkillName())
                    .onlineCount(onlineCount)
                    .offlineCount(offlineCount)
                    .skillRelationGame(shunt.isSkillRelationGame())
                    .skillBindGameCount(gameCount)
                    .gameCount(gamesSize)
                    .build()
                    .toString());
            });
             return stringBuffer.toString();
        });
    }

    private Tuple2<Integer, List<String>> shangjiaGame(){
        Try<List<GameBaseDTO>> listGameTry = productCenterGateway.queryAllGame();
        if (listGameTry.isFailure()) {
            log.error("schedule shunt task error:{}", listGameTry.getCause().getMessage());
            return Tuple.of(0, new ArrayList<>());
        }
        List<GameBaseDTO> gameBaseDTOs = Option.when(CollUtil.isNotEmpty(listGameTry), ()-> listGameTry.get().stream().filter(g-> g.getIsShangjia() != null && g.getIsShangjia()).toList()).getOrElse(ArrayList::new);
        return Tuple.of(gameBaseDTOs.size(), gameBaseDTOs.stream().map(GameBaseDTO::getGameName).toList());
    }

    public static void main(String[] args) {
        List<GameBaseDTO> gameBaseDTOs = new ArrayList<>();
        Tuple2 tuple2 = Tuple.of(gameBaseDTOs.size(), gameBaseDTOs.stream().map(GameBaseDTO::getGameName).toList());
        System.out.println(tuple2);
    }

    private int onlineStatus(List<ShuntScheduleUserDTO> dtos, Boolean isOnline) {
        if (CollUtil.isEmpty(dtos)) {
            return 0;
        }
        if (isOnline) {
            return (int) dtos.stream().filter(user -> user.getIsOnline() == 1).count();
        }
        return (int) dtos.stream().filter(user -> user.getIsOnline() == 0).count();
    }

    private CompletableFuture<String> ossContent(List<ShuntScheduleDTO> shuntScheduleDTOList) {
        return CompletableFuture.supplyAsync(()->{
            try {
                StringBuffer stringBuffer = new StringBuffer();
                String ossName = writeExcel(shuntScheduleDTOList);
                stringBuffer.append("数据下载地址：").append(ossName);
                return stringBuffer.toString();
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private String writeExcel(List<ShuntScheduleDTO> shuntScheduleDTOList) throws IOException {
        List<ShuntScheduleUploadDTO> datas = uploadShuntScheduleInfo(shuntScheduleDTOList);
        String fileName = "work_shunt_log" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(fileName, ShuntScheduleUploadDTO.class)
            .sheet("数据")
            .doWrite(() -> {
                // 分页查询数据
                return datas;
            });
        File file = new File(fileName);
        String ossName = ossAppService.uploadFile(new FileInputStream(file), fileName);
        Files.deleteIfExists(Paths.get(file.getPath()));
        return ossName;
    }

    private List<ShuntScheduleUploadDTO> uploadShuntScheduleInfo(List<ShuntScheduleDTO> shuntScheduleDTOList) {
        List<ShuntScheduleUploadDTO> results = new ArrayList<>();
        shuntScheduleDTOList.forEach(shuntScheduleDTO -> {
            List<ShuntScheduleUserDTO> userDTOS = shuntScheduleDTO.getUsers();
            if (CollUtil.isNotEmpty(userDTOS)) {
                results.addAll(userDTOS.stream().map(user-> {
                    int gameCount = user.getGames() == null ? 0 : user.getGames().size();
                    List<String> gameNames = user.getGames().stream().map(ShuntScheduleGameDTO::getGameName).toList();
                    return ShuntScheduleUploadDTO.builder().userName(user.getUserName()).skillName(shuntScheduleDTO.getSkillName()).imStatus(user.getIsOnline())
                        .gameCount(gameCount).gameName(CollUtil.isNotEmpty(user.getGames())?   Strings.join(gameNames, ',') : null).build();
                }).toList());
            }
        });
        return results;
    }

}
