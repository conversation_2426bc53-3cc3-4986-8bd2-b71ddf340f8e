package com.pxb7.mall.workshunt.adapter.consumer;

import com.alibaba.fastjson.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.workshunt.infra.repository.db.WorkUserDbRepository;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = ConsumerConstants.CID_USER_ONLINE_STATUS,
                         topic = ConsumerConstants.IM_USER_ONLINE_STATUS_TOPIC,
                         tag = "*")
public class ImUserOnLineStatueMessageListener  implements RocketMQListenerExt<ImUserOnlineStatusMessage> {

    @Resource
    private WorkUserDbRepository workUserDbRepository;

    @Override
    public ConsumeResult consume(MessageView messageView, ImUserOnlineStatusMessage payload) {
        log.info("Received ImUserOnlineStatusMessage {}", JSON.toJSONString(payload));
        return Try.of(()-> {
            workUserDbRepository.updateWorkUserOnline(payload.getUserId(), payload.getStatus());
            return ConsumeResult.SUCCESS;
        }).getOrElseGet((e)-> {
            log.error("update work user online error, userId: {}, status: {}, error: {}", payload.getUserId(), payload.getStatus(), e.getMessage());
            return ConsumeResult.FAILURE;
        });
    }
}
