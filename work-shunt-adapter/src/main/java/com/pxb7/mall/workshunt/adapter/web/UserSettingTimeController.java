package com.pxb7.mall.workshunt.adapter.web;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.auth.c.annotation.SaAdminCheckLogin;
import com.pxb7.mall.workshunt.app.model.ReqSaveWorkUserSettingDayDTO;
import com.pxb7.mall.workshunt.app.service.UserSettingTimeAppService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排班分流-admin
 */
@SaAdminCheckLogin
@RestController
@RequestMapping("/admin/web/work/user/setting/time")
public class UserSettingTimeController {

    @Resource
    private UserSettingTimeAppService workUserSettingDayAppService;

    /**
     * 排班管理-人员设置班次
     */
    @PostMapping("")
    public SingleResponse<Boolean> saveWorkUserSettingDay(@Valid @RequestBody ReqSaveWorkUserSettingDayDTO dto) {
        return SingleResponse.of(workUserSettingDayAppService.saveWorkUserSettingDay(dto));
    }
}
