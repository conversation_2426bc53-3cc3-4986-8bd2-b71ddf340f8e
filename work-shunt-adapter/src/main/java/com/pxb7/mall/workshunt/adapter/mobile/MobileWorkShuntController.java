package com.pxb7.mall.workshunt.adapter.mobile;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.workshunt.app.service.*;
import com.pxb7.mall.workshunt.client.dto.request.GetSkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryOnlineWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.QueryWorkUserRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillGameRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.WorkUserOnlineRespDTO;
import com.pxb7.mall.workshunt.client.work.response.GetSkillGameRespDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ReqWorkShuntGroupDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ValidationWorkerReqDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqAddGroupWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGroupTransferShuntShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqVerifyLogIdDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客服分流管理
 */
@RestController
@RequestMapping("/mobile/work/shunt")
public class MobileWorkShuntController {

    @Resource
    private ShuntLogAppService shuntLogAppService;
    @Resource
    private WorkUserAppService workUserAppService;

    @Resource
    private UserSettingSkillAppService workUserSettingSkillAppService;

    @Resource
    private ShuntAppService shuntAppService;

    @Resource
    private ShuntValidationService shuntValidationService;

    @Resource
    private WorkSkillAppService workSkillAppService;

    /**
     * 排班调度任务：调度分流
     */
    @PostMapping("/getShunt")
    public MultiResponse<GetWorkShuntRespDTO> listGetShunt(@RequestBody ReqGetWorkShuntDTO dto) {
        return MultiResponse.of(shuntAppService.shunt(dto));
    }

    /**
     * 确认 排班任务 ->
     */
    @PostMapping("/verifyLogId")
    public SingleResponse<Boolean> verifyDetailsId(@RequestBody ReqVerifyLogIdDTO dto) {
        return SingleResponse.of(shuntLogAppService.confirmWorkload(dto));
    }

    /**
     * 结束 排班任务 ->
     */
    @PostMapping("/closeDetailsId")
    public SingleResponse<Boolean> closeDetailsId(@RequestBody ReqVerifyLogIdDTO dto) {
        return SingleResponse.of(shuntLogAppService.closeDetailsId(dto));
    }

    /**
     * 获取指定用户  - 所有技能
     */
    @GetMapping("/getWorkUserSettingSkill")
    public SingleResponse<List<WorkUserSettingSkillPageListRespDTO>> getWorkUserSettingSkill(@RequestParam(value = "pkUserId", required = true) String pkUserId) {
        List<WorkUserSettingSkillPageListRespDTO> outPage = workUserSettingSkillAppService.getWorkUserSettingSkillByPkUserId(pkUserId);
        return SingleResponse.of(outPage);
    }

    /**
     * 通知类型： 指定固定用户技能  工作量+1 结束会话的时候，还是需要调用 closeDetailsId 来结束会话
     */
    @PostMapping("/addWorkloadToUser")
    public SingleResponse<GetWorkShuntRespDTO> addWorkloadToUser(@RequestBody ReqAddGroupWorkShuntDTO dto) {
        return SingleResponse.of(shuntLogAppService.addWorkloadToUser(dto));
    }

    /**
     * 通知类型：A转B -> A退出任务 B增加任务 B 结束任务 closeDetailsId 结束任务 A 需要调用 closeDetailsId 来结束会话
     */
    @PostMapping("/groupTransferShunt")
    public SingleResponse<GetWorkShuntRespDTO> groupTransferShunt(@RequestBody ReqGroupTransferShuntShuntDTO dto) {
        return SingleResponse.of(shuntLogAppService.groupTransferShunt(dto));
    }

    /**
     * 获取技能下下有所有用户 -- 不限制游戏条件
     */
    @GetMapping("/getSkillWorkUserByCode")
    public MultiResponse<WorkUserSelectDeptUserRespDTO> getSkillWorkUserByCode(@RequestParam(name = "skillCode", required = true) String skillCode) {
        List<WorkUserSelectDeptUserRespDTO> outPage = workUserSettingSkillAppService.getSkillWorkUserByCode(skillCode);
        return MultiResponse.of(outPage);
    }

    /**
     * 获取指定技能下的在线客服
     * @param skillCode
     * @return
     */

    @GetMapping("/getOnlineWorkUserByCode")
    public MultiResponse<WorkUserOnlineRespDTO> getOnlineWorkUser(@RequestParam(name = "skillCode", required = true) String skillCode,
                                                                  @RequestParam(name="gameId") String gameId) {
        List<WorkUserOnlineRespDTO> outPage = workUserSettingSkillAppService.getOnlineWorkUser(QueryOnlineWorkUserReqDTO.builder()
            .skillCode(skillCode)
            .gameId(gameId)
            .build());
        return MultiResponse.of(outPage);
    }

    /**
     * 获取指定技能+指定游戏的客服 -- 所有客服
     */
    @PostMapping("/getSkillWorkUserByCodeAndGameId")
    public MultiResponse<WorkUserDeptUserRespDTO> getSkillWorkUserByCodeAndGameId(@RequestBody ReqWorkShuntGroupDTO reqWorkShuntGroupDTO) {
        List<WorkUserDeptUserRespDTO> outPage = workUserAppService.getSkillWorkUserByCodeAndGameId(reqWorkShuntGroupDTO);
        return MultiResponse.of(outPage);
    }


    /**
     * 判断业务域提供的客服id和技能编码是否匹配
     * 并返回相关技能的客服(如果没有匹配到，就重新排班新的客服)
     */
    @PostMapping("/validationShunt")
    public MultiResponse<GetWorkShuntRespDTO> validationShunt(@RequestBody ValidationWorkerReqDTO dto) {
        List<GetWorkShuntRespDTO> outPage = shuntValidationService.validationWorkUser(dto);
        return MultiResponse.of(outPage);
    }

    /**
     * 获取所有技能
     */
    @GetMapping("/skills")
    public MultiResponse<SkillRepDTO> queryAllWorkSkillBindGame(){
        return MultiResponse.of(workSkillAppService.selectAllSKills());
    }

    /**
     * 获取技能下的游戏
     */
    @GetMapping("/skill/games")
    public MultiResponse<SkillGameRepDTO> queryBindGameBySkillCode(String code){
        return MultiResponse.of(workSkillAppService.queryWorkSkillBindGameBySkillCode(code));
    }

    /**
     * 获取商号下的客服
     */
    @PostMapping("/merchant/users")
    public MultiResponse<QueryWorkUserRepDTO> queryMerchantWorkUser(@RequestBody QueryWorkUserReqDTO dto){
        return MultiResponse.of(workUserAppService.queryMerchantWorkUser(dto));
    }

    /**
     * 获取具有该技能的客服下的有游戏
     */
    @GetMapping("/skill/user/games")
    public MultiResponse<GetSkillGameRespDTO> queryBindGameBySkillCodeAndUserId(@RequestParam(name = "skillCode", required = true) String skillCode,
                                                                                @RequestParam(name = "userId", required = true) String userId) {
        return MultiResponse.of(workUserSettingSkillAppService.getSkillRelatedGames(GetSkillGameReqDTO.builder()
            .userId(userId)
            .code(skillCode)
            .build()));
    }

}
