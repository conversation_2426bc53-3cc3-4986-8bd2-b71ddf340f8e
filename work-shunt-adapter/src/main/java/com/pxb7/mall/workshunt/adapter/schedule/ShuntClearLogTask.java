package com.pxb7.mall.workshunt.adapter.schedule;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.pxb7.mall.workshunt.domain.service.ShuntLogDomainService;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class ShuntClearLogTask implements BasicProcessor {

    @Resource
    private ShuntLogDomainService shuntLogDomainService;

    private final Integer DEFAULT_DAYS = 3;
    @Override
    public ProcessResult process(TaskContext context) throws Exception {

        Integer days = Option.of(context.getJobParams())
            .filter(CharSequenceUtil::isNotBlank)
            .filter(CharSequenceUtil::isNumeric)
            .map(Integer::valueOf)
            .getOrElse(DEFAULT_DAYS);

        LocalDate currentDate = LocalDate.now().minusDays(days);

        String date = LocalDateTimeUtil.format(currentDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("Processing clear log :{}, param {}", date, days);
        Try<Void> clear = Try.run(() -> shuntLogDomainService.deleteByTime(date));
        if (clear.isFailure()) {
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }
}
