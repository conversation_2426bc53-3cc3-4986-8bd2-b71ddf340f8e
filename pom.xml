<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.pxb7.mall.workshunt</groupId>
    <artifactId>work-shunt</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>work-shunt</name>
    <properties>
        <revision>1.0</revision>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>${maven.compiler.source}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${project.build.sourceEncoding}</project.reporting.outputEncoding>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <maven.source.skip>true</maven.source.skip>
        <skipSpotless>false</skipSpotless>
        <maven.deploy.skip>false</maven.deploy.skip>
        <cola.components.version>4.3.2</cola.components.version>
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
        <spring-cloud-starter-bootstrap-version>4.1.2</spring-cloud-starter-bootstrap-version>
        <spring-boot.version>3.2.4</spring-boot.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mybatis-plus-starter.version>3.5.6</mybatis-plus-starter.version>
        <dynamic-datasource-spring-boot-starter.version>3.6.0</dynamic-datasource-spring-boot-starter.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <redission-spring-boot-version>3.29.0</redission-spring-boot-version>
        <spring-context-support-version>1.0.11</spring-context-support-version>
        <hutool.version>5.8.27</hutool.version>
        <guava.version>33.2.0-jre</guava.version>
        <lombok.version>1.18.32</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <fastjson.version>2.0.50</fastjson.version>
        <okhttp.version>4.12.0</okhttp.version>
        <retrofit.version>2.11.0</retrofit.version>
        <converter-jackson-version>2.11.0</converter-jackson-version>
        <jakarta.validation-api-version>3.0.2</jakarta.validation-api-version>
        <jakarta.el-api-version>6.0.0</jakarta.el-api-version>
        <skywalking.version>9.2.0</skywalking.version>
        <shardingsphere-jdbc-version>5.5.0</shardingsphere-jdbc-version>
        <maven-resources-plugin-version>3.3.1</maven-resources-plugin-version>
        <maven-clean-plugin-version>3.3.2</maven-clean-plugin-version>
        <maven.compiler-plugin.version>3.13.0</maven.compiler-plugin.version>
        <maven-deploy-plugin.version>3.1.2</maven-deploy-plugin.version>
        <sonar-maven-plugin-version>3.11.0.3922</sonar-maven-plugin-version>
        <spotless-maven-plugin-version>2.43.0</spotless-maven-plugin-version>
        <logback-level-change-version>1.0.3</logback-level-change-version>
        <spring-kafka-version>3.1.10</spring-kafka-version>
        <common-collection-version>4.4</common-collection-version>
        <sslcontext-kickstart>8.3.5</sslcontext-kickstart>
        <idgen-spring-boot-starter-version>1.1.3</idgen-spring-boot-starter-version>
        <sa-token.version>1.37.0</sa-token.version>
        <exception-spring-boot-starter.version>1.11.9</exception-spring-boot-starter.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <sentryext.version>1.0.2</sentryext.version>
        <sentinel.version>1.0.1</sentinel.version>
        <dynamic-tp-spring-cloud-starter-nacos-version>1.1.7-3.x</dynamic-tp-spring-cloud-starter-nacos-version>
        <powerjob.version>4.3.9</powerjob.version>
        <auth-satoken-c-starter.version>1.0.2</auth-satoken-c-starter.version>
        <auth-satoken-admin-starter.version>1.0.10</auth-satoken-admin-starter.version>
        <pxb7.rocketmqext.version>1.0.2</pxb7.rocketmqext.version>
        <rocketmq.boot.version>2.3.0</rocketmq.boot.version>
        <dubbo.boot.version>3.2.12</dubbo.boot.version>
        <dubbo-instance-register-starter.verion>1.0.7</dubbo-instance-register-starter.verion>
        <spring-retry.version>2.0.5</spring-retry.version>
        <aspectjweaver.version>1.9.21</aspectjweaver.version>
        <app-config-starter.version>1.0.13</app-config-starter.version>
        <easyexcel.version>4.0.1</easyexcel.version>
        <oss.version>3.17.4</oss.version>
        <!--其他域 api版本-->
        <common-support-client.version>1.0.15</common-support-client.version>
        <user-c-client.version>1.0.5</user-c-client.version>
        <user-admin-client.version>1.0.3</user-admin-client.version>
        <product-c-client-version>1.0.0</product-c-client-version>
        <product-admin-client-version>1.0.0</product-admin-client-version>
        <im-c-client-version>1.0.23</im-c-client-version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>exception-spring-boot-starter</artifactId>
            <version>${exception-spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>0.9.0</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>app-config-starter</artifactId>
                <version>${app-config-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>sentryext-spring-boot-starter</artifactId>
                <version>${sentryext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>auth-satoken-c-starter</artifactId>
                <version>${auth-satoken-c-starter.version}</version>
            </dependency>
          <!--  <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>auth-satoken-admin-starter</artifactId>
                <version>${auth-satoken-admin-starter.version}</version>
            </dependency>-->
            <!-- 解决通过SSL链接es验证证书失败的问题-->
            <dependency>
                <groupId>io.github.hakky54</groupId>
                <artifactId>sslcontext-kickstart</artifactId>
                <version>${sslcontext-kickstart}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>sentinel-component-starter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>dubbo-instance-register-starter</artifactId>
                <version>${dubbo-instance-register-starter.verion}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-common</artifactId>
                <version>${dubbo.boot.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${spring-cloud-starter-bootstrap-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
            </dependency>
            <!--Project modules-->
            <dependency>
                <groupId>com.pxb7.mall.workshunt</groupId>
                <artifactId>work-shunt-adapter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.workshunt</groupId>
                <artifactId>work-shunt-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.workshunt</groupId>
                <artifactId>work-shunt-app</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.workshunt</groupId>
                <artifactId>work-shunt-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.workshunt</groupId>
                <artifactId>work-shunt-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--Project modules End-->
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>rocketmqext-spring-boot-starter</artifactId>
                <version>${pxb7.rocketmqext.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
                <version>${rocketmq.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-level-change-version}</version>
            </dependency>
            <!--Sa-Token 权限认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.alibaba.cola/cola-component-extension-starter -->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-extension-starter</artifactId>
                <version>5.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-nacos</artifactId>
                <version>${dynamic-tp-spring-cloud-starter-nacos-version}</version>
            </dependency>
            <!-- powerJob -->
            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-worker-spring-boot-starter</artifactId>
                <version>${powerjob.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redission-spring-boot-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>idgen-spring-boot-starter</artifactId>
                <version>${idgen-spring-boot-starter-version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-test-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-oracle</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-sqlserver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-opengauss</artifactId>
                    </exclusion>
                </exclusions>
                <version>${shardingsphere-jdbc-version}</version>
            </dependency>
            <!--rocketmq-->
            <!--spring cloud nacos start-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <!--spring cloud nacos end-->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${jakarta.el-api-version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${converter-jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-exception</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka-version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${common-collection-version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel-core</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!--            oss-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.common</groupId>
                <artifactId>common-support-client</artifactId>
                <version>${common-support-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.user</groupId>
                <artifactId>user-c-client</artifactId>
                <version>${user-c-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.user</groupId>
                <artifactId>user-admin-client</artifactId>
                <version>${user-admin-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.product</groupId>
                <artifactId>product-admin-client</artifactId>
                <version>${product-admin-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.product</groupId>
                <artifactId>product-c-client</artifactId>
                <version>${product-c-client-version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.im</groupId>
                <artifactId>im-c-client</artifactId>
                <version>${im-c-client-version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>pxb7-releases</id>
            <url>http://nexus.pxb7.internal/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <snapshotRepository>
            <id>pxb7-snapshots</id>
            <url>http://nexus.pxb7.internal/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>pxb7-nexus</id>
            <url>http://nexus.pxb7.internal/repository/maven-public/</url>
            <releases></releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <defaultGoal>compile</defaultGoal>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin-version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.source}</target>
                    <compilerArgument>-parameters</compilerArgument>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>                                                                                                                                        <!-- Lombok 在编译时插件 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--<plugin>              <groupId>org.sonarsource.scanner.maven</groupId>              <artifactId>sonar-maven-plugin</artifactId>              <version>${sonar-maven-plugin-version}</version>              <executions>                <execution>                  <goals>                    <goal>sonar</goal>                  </goals>                  <phase>verify</phase>                </execution>              </executions>            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin-version}</version>
                <configuration>
                    <filesets>
                        <fileset>                                                                                                                                                                    <!--要清理的目录位置-->
                            <directory>${user.home}/data/weblog/${name}
                            </directory>                                                                                                                                                                    <!--是否跟随符号链接,默认false-->
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>                                                                                                                                                                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>work-shunt-client</module>
        <module>work-shunt-adapter</module>
        <module>work-shunt-app</module>
        <module>work-shunt-domain</module>
        <module>work-shunt-infrastructure</module>
        <module>start</module>
    </modules>
</project>