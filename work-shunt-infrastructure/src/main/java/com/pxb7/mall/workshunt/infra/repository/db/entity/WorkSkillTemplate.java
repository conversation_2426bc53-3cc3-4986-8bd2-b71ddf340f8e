package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技能模版(WorkSkillTemplate)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 10:39:13
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_skill_template")
public class WorkSkillTemplate implements Serializable {
    private static final long serialVersionUID = 852850398830027269L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务模板ID
     */
    @TableField(value = "skill_template_id")
    private String skillTemplateId;
    /**
     * 模版标题
     */
    @TableField(value = "template_title")
    private String templateTitle;

    /**
     * 是否覆盖接待上限
     */
    @TableField(value = "is_override_max_limit")
    private Boolean overrideMaxLimit;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

