package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作时间模版(WorkTimeTemplate)实体类
 *
 * <AUTHOR>
 * @since 2024-08-01 20:35:03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_time_template")
public class WorkTimeTemplate implements Serializable {
    private static final long serialVersionUID = 164578637025751409L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务ID
     */
    @TableField(value = "time_template_id")
    private String timeTemplateId;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private String startTime;
    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private String endTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 是否删除 0:否, 1:是
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "create_user_id")
    private String createUserId;

    @TableField(value = "update_user_id")
    private String updateUserId;

}

