package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRule;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 分流规则(WorkShuntRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 15:05:37
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntRuleDbRepository extends ServiceImpl<WorkShuntRuleMapper, WorkShuntRule>
    implements WorkShuntRuleRepository {

}
