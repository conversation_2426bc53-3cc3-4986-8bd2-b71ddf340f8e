package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
* 技能类型 (WorkSkillType)表服务实现类
*
*/
@Slf4j
@Repository
@DS("mysql")
public class WorkSkillTypeDbRepository extends ServiceImpl<WorkSkillTypeMapper, WorkSkillType> implements WorkSkillTypeRepository {


}
