package com.pxb7.mall.workshunt.infra.repository.gateway.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.im.client.api.AdminImUserServiceI;
import com.pxb7.mall.im.client.dto.response.CustomerCareSessionRespDTO;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>功能描述:</p>
 * 作者：rookie
 * 创建日期：2025/07/28
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */

@Slf4j
@Service
public class ImGateway {

    @DubboReference
    private AdminImUserServiceI adminImUserServiceI;



    public Integer getImStatus(String userId) {
        return Try.of(() -> adminImUserServiceI.getAdminImUserOnlineStatus(userId))
                .onFailure(ex -> log.error("调用 getAdminImUserOnlineStatus 接口异常, userId: {}, error: {}", userId, ex.getMessage()))
                .flatMap(response -> {
                    if (response == null || !response.isSuccess()) {
                        log.warn("获取用户在线状态业务失败, userId: {}, response: {}", userId, response);
                        return Try.failure(new RuntimeException("Business logic failed"));
                    }
                    return Try.success(response);
                })
                .map(SingleResponse::getData)
                .getOrElse(2);
    }



    public List<DTO> getConsumerSessionCount(List<String> userIds) {

        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        return Try.of(() -> adminImUserServiceI.getCustomerCareSessionNum(userIds))
                .onFailure(ex -> log.error("调用 getCustomerCareSessionNum 接口异常, userIds: {}, error: {}", userIds, ex.getMessage(), ex))

                .flatMap(response -> {
                    if (response == null) {
                        log.warn("getCustomerCareSessionNum 接口返回了 null 响应, userIds: {}", userIds);
                        return Try.success(null);
                    }
                    if (!response.isSuccess()) {
                        log.error("获取客服会话数业务失败, userIds: {}, code: {}, msg: {}",
                                userIds, response.getErrCode(), response.getErrMessage());
                        return Try.failure(new RuntimeException("Business logic failed: " + response.getErrMessage()));
                    }
                    return Try.success(response);
                })
                .map(MultiResponse::getData)
                .getOrElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .map(this::buildFromCustomerCareSessionRespDTO)
                .toList();


    }

    private DTO buildFromCustomerCareSessionRespDTO(CustomerCareSessionRespDTO r) {
        return DTO.builder().userId(r.getCustomerCareId()).sessionCount(r.getSessionNum()).build();
    }


    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DTO{
        private String userId;
        private Integer sessionCount;
    }


}
