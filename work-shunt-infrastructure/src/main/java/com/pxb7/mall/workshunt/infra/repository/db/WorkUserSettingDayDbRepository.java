package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingDayMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* 用户时间 (WorkUserSettingDay)表服务实现类
*
*/
@Slf4j
@Repository
@DS("mysql")
public class WorkUserSettingDayDbRepository extends ServiceImpl<WorkUserSettingDayMapper, WorkUserSettingDay> implements WorkUserSettingDayRepository {

    @Resource
    private WorkUserSettingDayMapper workUserSettingDayMapper;


    /**
    * 用户时间 获取一个详情
    * @param key
    * @return
    */
    public WorkUserSettingDay getTableOneDataByParamKey(String key){
        return workUserSettingDayMapper.selectOne(new LambdaQueryWrapper<WorkUserSettingDay>()
            .eq(WorkUserSettingDay::getId, key));
    }

    @Override
    public boolean deleteUserSettingDay(String userId, String month, String day) {
        LambdaQueryWrapper<WorkUserSettingDay> query = new LambdaQueryWrapper<>();
        query.eq(WorkUserSettingDay::getWorkUserId, userId)
            .eq(WorkUserSettingDay::getMonth, month)
            .eq(WorkUserSettingDay::getDay, day);
        return workUserSettingDayMapper.delete(query) != 0;
    }

    @Override
    public WorkUserSettingDay query(String userId, String month, String day) {
        LambdaQueryChainWrapper<WorkUserSettingDay> query = new LambdaQueryChainWrapper<>(workUserSettingDayMapper);
        query.eq(WorkUserSettingDay::getWorkUserId, userId)
            .eq(WorkUserSettingDay::getMonth, month)
            .eq(WorkUserSettingDay::getDay, day);
        return query.one();
    }

    @Override
    public List<WorkUserSettingDay> query(List<String> workUserIds, String month, String day) {
        LambdaQueryChainWrapper<WorkUserSettingDay> query = new LambdaQueryChainWrapper<>(workUserSettingDayMapper);
        query.in(WorkUserSettingDay::getWorkUserId, workUserIds)
            .eq(WorkUserSettingDay::getMonth, month)
            .eq(WorkUserSettingDay::getDay, day);
        return query.list();
    }

    @Override
    public List<WorkUserSettingDay> listWorkUserSettingDayByMonthUserId(String month, String workUserId) {
        return workUserSettingDayMapper.selectList(new LambdaQueryWrapper<WorkUserSettingDay>()
            .eq(WorkUserSettingDay::getMonth, month)   //改为表业务ID
            .eq(WorkUserSettingDay::getWorkUserId, workUserId));    //改为表业务ID
    }

    @Override
    public List<WorkUserSettingDay> query(List<String> workUserIds, String month, List<String> day) {
        LambdaQueryChainWrapper<WorkUserSettingDay> query = new LambdaQueryChainWrapper<>(workUserSettingDayMapper);
        query.in(WorkUserSettingDay::getWorkUserId, workUserIds)
                .eq(WorkUserSettingDay::getMonth, month)
                .in(WorkUserSettingDay::getDay, day);
        return query.list();
    }
}
