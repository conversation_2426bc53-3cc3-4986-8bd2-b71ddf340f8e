package com.pxb7.mall.workshunt.infra.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.Properties;

/**
 * 过滤测试账号
 * 配置黑名单
 */
@Getter
@Configuration
@Slf4j
public class WorkShuntBlacklistConfig extends AbstractAppConfig {

    private static final String SHUNT_FILTER_USERS = "shunt.filter.users";

    @Value("${shunt.filter.users:null}")
    private String filterUsers;

    public static final String DEFAULT_NULL = "null";
    @Override
    protected void loadProperties(Properties properties) {
        if (Objects.isNull(properties)) {
            log.warn("拉取的配置为空");
            return;
        }

        String filterUsers = properties.getProperty(SHUNT_FILTER_USERS);
        if (StringUtils.isBlank(filterUsers)) {
            log.warn("nacos的{}配置变更为空字符串，不更新", SHUNT_FILTER_USERS);
            return;
        }
        this.filterUsers = filterUsers;
        log.info("nacos动态更新黑名单，filterUsers:{}", filterUsers);
    }
}
