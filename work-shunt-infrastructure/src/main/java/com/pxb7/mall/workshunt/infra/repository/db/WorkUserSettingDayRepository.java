package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay;

import java.util.List;

/**
* 用户时间 (WorkUserSettingDay) 表服务接口
*/
public interface WorkUserSettingDayRepository extends IService<WorkUserSettingDay> {


    /**
    * 用户时间 获取一个详情
    * @param key
    * @return
    */
    WorkUserSettingDay getTableOneDataByParamKey(String key);

    /**
     * 删除员工班次数据
     * @param userId
     * @param month
     * @param day
     * @return
     */
    boolean deleteUserSettingDay(String userId, String month, String day);

    /**
     *
     * @param userId
     * @param month
     * @param day
     * @return
     */
    WorkUserSettingDay query(String userId, String month, String day);

    List<WorkUserSettingDay> query(List<String> workUserIds, String month, String day);

    List<WorkUserSettingDay> query(List<String> workUserIds, String month, List<String> day);

    List<WorkUserSettingDay> listWorkUserSettingDayByMonthUserId(String month,String workUserId);


}

