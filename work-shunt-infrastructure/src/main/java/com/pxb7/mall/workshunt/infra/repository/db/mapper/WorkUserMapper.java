package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkSkillCodeGroupShuntPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作用户(WorkUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-02 16:29:56
 */
@Mapper
public interface WorkUserMapper extends BaseMapper<WorkUser> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkUser> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkUser> entities);


    List<WorkUser> getWorkUserBySkillNotNUll(@Param("workUserId") String workUserId);

    List<WorkUser> getWorkUserBySkillCode(@Param("skillCode") String skillCode);

    List<WorkUser> getWorkUserBySkillCodeAndGameId(@Param("skillCode") String skillCode, @Param("gameId") String gameId);

    List<WorkUser> workSkillCodeGroupShunt(@Param("po") WorkSkillCodeGroupShuntPO po);

}

