package com.pxb7.mall.workshunt.infra.repository.db.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
public class WorkUserSettingSkillBoard {
    @Getter
    @Setter
    private String settingSkillId;
    @Getter
    @Setter
    private String skillId;
    @Getter
    @Setter
    private String skillName;
    @Getter
    @Setter
    private Long maxLimit;

    @Getter
    @Setter
    private Long sessionMaxLimit;

    @Getter
    @Setter
    private String workUserId;

    @Getter
    @Setter
    private Integer imStatus;

    @Getter
    @Setter
    private String userId;
    @Getter
    @Setter
    private String deptId;

    public Boolean getIsOnline() {
        return (getImStatus() == null || getImStatus() == 2 || getImStatus() == 3) ? false :true;
    }

}
