package com.pxb7.mall.workshunt.infra.util;


import javax.crypto.*;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description aes加密算法
 * @date 2024-11-25 15:45
 **/
public class AesUtil {
    private AesUtil() {
    }

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";

    // AES密钥长度
    private static final int AES_KEY_SIZE = 256;
    // GCM推荐的IV长度为12字节
    private static final int IV_SIZE = 12;

    // GCM认证标签部分的长度
    private static final int TAG_LENGTH_BIT = 128;

    //生成iv初始向量
    public static byte[] generateIv() {
        byte[] iv = new byte[IV_SIZE];
        SecureRandom prng = new SecureRandom();
        prng.nextBytes(iv);
        return iv;
    }

    /**
     * 加密
     * @param plainText 明文
     * @param secret 密钥
     * @param iv 初始化向量
     * @return 密文
     * @throws NoSuchPaddingException
     * @throws NoSuchAlgorithmException
     * @throws InvalidAlgorithmParameterException
     * @throws InvalidKeyException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     */
    public static String encrypt(String plainText, String secret, byte[] iv) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        byte[] secretBytes = Base64.getUrlDecoder().decode(secret);
        SecretKeySpec keySpec = new SecretKeySpec(secretBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getUrlEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 解密
     * @param cipherText 密文
     * @param secret 密钥
     * @param iv 初始化向量
     * @return 明文
     * @throws Exception 异常
     */
    public static String decrypt(String cipherText, String secret, byte[] iv) throws Exception {
        byte[] secretBytes = Base64.getUrlDecoder().decode(secret);
        SecretKeySpec keySpec = new SecretKeySpec(secretBytes, ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH_BIT, iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParameterSpec);
        byte[] decrypted = cipher.doFinal(Base64.getUrlDecoder().decode(cipherText));
        return new String(decrypted);
    }


    /**
     * 加密
     * @return
     * @throws Exception
     */
    public static String generateKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(AES_KEY_SIZE, SecureRandom.getInstanceStrong());
        return new String(Base64.getUrlEncoder().encode(keyGenerator.generateKey().getEncoded()));
    }
}