package com.pxb7.mall.workshunt.infra.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 技能模版(WorkSkillTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 09:56:24
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkSkillTemplateDbRepository extends ServiceImpl<WorkSkillTemplateMapper, WorkSkillTemplate>
    implements WorkSkillTemplateRepository {
    /**
     * 技能模板 获取一个详情
     * @param skillTemplateId
     * @return
     */
    public WorkSkillTemplate getTableOneDataByParamKey(String skillTemplateId){
        return this.getOne(new LambdaQueryWrapper<WorkSkillTemplate>()
            .eq(WorkSkillTemplate::getSkillTemplateId, skillTemplateId));
    }

    @Override
    public List<WorkSkillTemplate> allDataOrderByCreateDate() {
        return this.list(new LambdaQueryWrapper<WorkSkillTemplate>().orderByDesc(WorkSkillTemplate::getCreateTime));
    }

    @Override
    public boolean deleteByTemplateId(String templateId) {
        return this.remove(new LambdaQueryWrapper<WorkSkillTemplate>().eq(WorkSkillTemplate::getSkillTemplateId, templateId));
    }

    @Override
    public WorkSkillTemplate getWorkSkillTemplateByName(String templateName) {
        return this.getOne(new LambdaQueryWrapper<WorkSkillTemplate>()
           .eq(WorkSkillTemplate::getTemplateTitle, templateName));
    }

    @Override
    public List<WorkSkillTemplate> queryByTemplateIds(List<String> templateIds) {
        return this.list(new LambdaQueryWrapper<WorkSkillTemplate>().in(CollUtil.isNotEmpty(templateIds), WorkSkillTemplate::getSkillTemplateId, templateIds));
    }
}
