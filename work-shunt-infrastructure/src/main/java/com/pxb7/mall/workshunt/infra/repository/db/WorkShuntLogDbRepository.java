package com.pxb7.mall.workshunt.infra.repository.db;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.enums.ReqTypeEnums;
import com.pxb7.mall.workshunt.infra.model.workShuntLog.WorkShuntLogPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLog;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
* 请求日志记录 (WorkShuntLog)表服务实现类
*
*/
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntLogDbRepository extends ServiceImpl<WorkShuntLogMapper, WorkShuntLog> implements WorkShuntLogRepository {

    @Resource
    private WorkShuntLogMapper workShuntLogMapper;

    /**
    * 请求日志记录 分页查询
    * @return
    */
    @Override
    public Page<WorkShuntLog> listPageWorkShuntLog(WorkShuntLogPageListPO po) {
         Page<WorkShuntLog> page = new Page<WorkShuntLog>(po.getPageIndex(),po.getPageSize());
         return workShuntLogMapper.selectPage(page,
             new LambdaQueryWrapper<WorkShuntLog>()
                .eq(StrUtil.isNotEmpty(po.getShuntType()), WorkShuntLog::getShuntType, po.getShuntType())
                .eq(StrUtil.isNotEmpty(po.getWorkGameId()), WorkShuntLog::getWorkGameId, po.getWorkGameId())
                .like(StrUtil.isNotEmpty(po.getWorkSkillId()), WorkShuntLog::getSkillIds, po.getWorkSkillId())  //技能ID
                .like(StrUtil.isNotEmpty(po.getWorkOutData()), WorkShuntLog::getPkUserNames, po.getWorkOutData()) //员工姓名
                .gt(StrUtil.isNotEmpty(po.getWorkStartTime()), WorkShuntLog::getWorkTime, po.getWorkStartTime())
                .lt(StrUtil.isNotEmpty(po.getWorkEndTime()), WorkShuntLog::getWorkTime, po.getWorkEndTime())
                .eq( WorkShuntLog::getReqType, ReqTypeEnums.reqType1.getType() )
                .orderByDesc(WorkShuntLog::getCreateTime));
    }

    /**
    * 请求日志记录 获取一个详情
    * @param key
    * @return
    */
    public WorkShuntLog getTableOneDataByParamKey(String key){
        WorkShuntLog workShuntLog = workShuntLogMapper.selectOne(new LambdaQueryWrapper<WorkShuntLog>()
            .eq(WorkShuntLog::getId, key));    //改为表业务ID
        return workShuntLog;
    }

    @Override
    public Page<WorkShuntLog> pages(WorkShuntLogPageListPO po) {
        Page<WorkShuntLog> page = new Page<WorkShuntLog>(po.getPageIndex(),po.getPageSize());
        return workShuntLogMapper.pages(page, po);
    }

    @Override
    public void deleteByTime(String date) {
        this.remove(Wrappers.<WorkShuntLog>query().lambda().lt(WorkShuntLog::getCreateTime, date));
    }

}
