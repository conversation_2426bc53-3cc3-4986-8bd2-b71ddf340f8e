//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.pxb7.mall.workshunt.infra.template;

import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

@Component
public class RocketMQEnhanceTemplate {
    private final RocketMQClientTemplate rocketMQClientTemplate;

    public String buildDestination(String topic, String tag) {
        return topic + ":" + tag;
    }

    public String sendFifo(String destination, Object payload, String orderItemId){
        SendReceipt sendReceipt = rocketMQClientTemplate.syncSendFifoMessage(destination, payload, orderItemId);
        return sendReceipt.getMessageId().toString();
    }

    public String send(String topic, String tag, Object payload) {
        return this.send(this.buildDestination(topic, tag), payload);
    }

    public String send(String destination, Object payload) {
        SendReceipt sendReceipt = this.rocketMQClientTemplate.syncSendNormalMessage(destination, payload);
        return sendReceipt.getMessageId().toString();
    }

    public CompletableFuture<String> asyncSend(String topic, String tag, Object payload) {
        return this.asyncSend(this.buildDestination(topic, tag), payload);
    }

    public CompletableFuture<String> asyncSend(String destination, Object payload) {
        CompletableFuture<SendReceipt> completableFuture = this.rocketMQClientTemplate.asyncSendNormalMessage(destination, payload, (CompletableFuture)null);
        return completableFuture.thenApply((sendReceipt) -> {
            return sendReceipt.getMessageId().toString();
        });
    }

    public String sendSyncDelay(String topic, String tag, Object payload, Duration messageDelayTime) {
        return this.sendSyncDelay(this.buildDestination(topic, tag), payload, messageDelayTime);
    }

    public String sendSyncDelay(String destination, Object payload, Duration messageDelayTime) {
        SendReceipt sendReceipt = this.rocketMQClientTemplate.syncSendDelayMessage(destination, payload, messageDelayTime);
        return sendReceipt.getMessageId().toString();
    }

    public CompletableFuture<String> asyncSendDelay(String topic, String tag, Object payload, Duration messageDelayTime) {
        return this.asyncSendDelay(this.buildDestination(topic, tag), payload, messageDelayTime);
    }

    public CompletableFuture<String> asyncSendDelay(String destination, Object payload, Duration messageDelayTime) {
        CompletableFuture<SendReceipt> completableFuture = this.rocketMQClientTemplate.asyncSendDelayMessage(destination, payload, messageDelayTime, (CompletableFuture)null);
        return completableFuture.thenApply((sendReceipt) -> {
            return sendReceipt.getMessageId().toString();
        });
    }

    public String syncSendOrder(String topic, String tag, Object payload, String group) {
        return this.syncSendOrder(this.buildDestination(topic, tag), payload, group);
    }

    public String syncSendOrder(String destination, Object payload, String group) {
        SendReceipt sendReceipt = this.rocketMQClientTemplate.syncSendFifoMessage(destination, payload, group);
        return sendReceipt.getMessageId().toString();
    }

    public CompletableFuture<String> asyncSendOrder(String topic, String tag, Object payload, String group) {
        return this.asyncSendOrder(this.buildDestination(topic, tag), payload, group);
    }

    public CompletableFuture<String> asyncSendOrder(String destination, Object payload, String group) {
        CompletableFuture<SendReceipt> completableFuture = this.rocketMQClientTemplate.asyncSendFifoMessage(destination, payload, group, (CompletableFuture)null);
        return completableFuture.thenApply((sendReceipt) -> {
            return sendReceipt.getMessageId().toString();
        });
    }

    public String sendInTransaction(String topic, String tag, Object payload, Consumer<String> callback) {
        return this.sendInTransaction(this.buildDestination(topic, tag), payload, callback);
    }

    public String sendInTransaction(String destination, Object payload, Consumer<String> callback) {
        if (callback == null) {
            throw new RuntimeException("callback is null");
        } else {
            Pair pair;
            try {
                pair = this.rocketMQClientTemplate.sendMessageInTransaction(destination, payload);
            } catch (ClientException var10) {
                throw new RuntimeException(var10);
            }

            try {
                callback.accept(((SendReceipt)pair.getSendReceipt()).toString());
            } catch (Exception var9) {
                try {
                    ((Transaction)pair.getTransaction()).rollback();
                } catch (ClientException var7) {
                }

                throw new RuntimeException(var9);
            }

            try {
                ((Transaction)pair.getTransaction()).commit();
            } catch (Exception var8) {
            }

            return ((SendReceipt)pair.getSendReceipt()).toString();
        }
    }

    @Autowired
    public RocketMQEnhanceTemplate(RocketMQClientTemplate rocketMQClientTemplate) {
        this.rocketMQClientTemplate = rocketMQClientTemplate;
    }
}
