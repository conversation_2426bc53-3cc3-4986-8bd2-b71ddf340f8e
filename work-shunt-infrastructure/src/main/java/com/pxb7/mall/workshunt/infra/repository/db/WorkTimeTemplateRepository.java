package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.model.workTimeTemplate.WorkTimeTemplatePageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;

import java.util.List;

/**
* 工作时间模版 (WorkTimeTemplate) 表服务接口
*/
public interface WorkTimeTemplateRepository extends IService<WorkTimeTemplate> {

    /**
    * 工作时间模版 分页查询
    * @param po
    * @return
    */
    Page<WorkTimeTemplate> listPageWorkTimeTemplate(WorkTimeTemplatePageListPO po);


    /**
    * 工作时间模版 获取一个详情
    * @param key
    * @return
    */
    WorkTimeTemplate getTableOneDataByParamKey(String key);

    boolean update(WorkTimeTemplate timeTemplate);

    List<WorkTimeTemplate> queryByIds(List<String> templateIds);

    boolean checkRepeat(String templateName);

    boolean checkRepeat(String templateName, String workTimeTemplateId);


}

