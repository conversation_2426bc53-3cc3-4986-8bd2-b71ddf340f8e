package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillBoard;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作用户的技能(WorkUserSettingSkill)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-03 16:30:45
 */
@Mapper
public interface WorkUserSettingSkillMapper extends BaseMapper<WorkUserSettingSkill> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkUserSettingSkill> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkUserSettingSkill> entities);

    Page<WorkUserSettingSkillBoard> queryWorkUserSettingPage(@Param("page") Page<WorkUserSettingSkillBoard> page,
                                                             @Param("deptId") String deptId,
                                                             @Param("userIds")List<String> userIds,
                                                             @Param("skillId") String skillId,
                                                             @Param("online") Integer online);


    Page<WorkUserSettingSkillBoard> queryWorkUserSettingByGameIdPage(@Param("page") Page<WorkUserSettingSkillBoard> page,
                                                             @Param("deptId") String deptId,
                                                             @Param("userIds")List<String> userIds,
                                                             @Param("skillId") String skillId,
                                                             @Param("online") Integer online,
                                                             @Param("gameId") String gameId        );

    List<WorkUserSettingSkillGameList> queryBySKillIdsAndGameId(@Param("skillIds") List<String> skillId,
                                                                @Param("gameId") String gameId);

}

