package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntOrderVolume;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntOrderVolumeMapper;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Repository
@DS("mysql")
public class WorkShuntOrderVolumeDbRepository extends ServiceImpl<WorkShuntOrderVolumeMapper, WorkShuntOrderVolume> implements WorkShuntOrderVolumeRepository {

    private static final String KEY_PREFIX = "common-support:work-shunt:%s:%s";

    @Resource
    private WorkShuntOrderVolumeMapper workShuntOrderVolumeMapper;

    @Override
    public WorkShuntOrderVolume queryBySkillIdAndUserId(String skillId, String userId) {
        LambdaQueryWrapper<WorkShuntOrderVolume> query = new LambdaQueryWrapper<>();
        query.eq(WorkShuntOrderVolume::getSkillId, skillId).eq(WorkShuntOrderVolume::getUserId, userId);
        return this.getOne(query);
    }

    @Override
    public List<WorkShuntOrderVolume> queryBySkillIdsAndUserIds(List<String> skillIds, List<String> userIds) {
        LambdaQueryWrapper<WorkShuntOrderVolume> query = new LambdaQueryWrapper<>();
        query.in(WorkShuntOrderVolume::getSkillId, skillIds).in(WorkShuntOrderVolume::getUserId, userIds);
        return this.list(query);
    }

    @Override
    public boolean addWorkShuntOrderVolume(String skillId, String userId) {
        return Try.of(()-> {
            WorkShuntOrderVolume order = queryBySkillIdAndUserId(skillId, userId);
            return RLockUtils.of(String.format(KEY_PREFIX, userId, skillId), () -> {
                LambdaUpdateWrapper<WorkShuntOrderVolume> update = new LambdaUpdateWrapper<>();
                update.eq(WorkShuntOrderVolume::getSkillId, skillId).eq(WorkShuntOrderVolume::getUserId, userId).set(WorkShuntOrderVolume::getVolume, order.getVolume()+1);
                return this.update(update);
            }).withWaitTime(3000).withTimeUnit(TimeUnit.MILLISECONDS).orElse(false);
        }).onFailure(t-> {
            log.warn("[添加客服的接单量异常:]{},{},{}",userId, skillId, t.getMessage());
        }).getOrElse(()->false);
    }

    @Override
    public boolean minusWorkShuntOrderVolume(String skillId, String userId) {
        return Try.of(()-> {
            WorkShuntOrderVolume order = queryBySkillIdAndUserId(skillId, userId);
            return RLockUtils.of(String.format(KEY_PREFIX, userId, skillId), () -> {
                LambdaUpdateWrapper<WorkShuntOrderVolume> update = new LambdaUpdateWrapper<>();
                update.eq(WorkShuntOrderVolume::getSkillId, skillId).eq(WorkShuntOrderVolume::getUserId, userId).set(WorkShuntOrderVolume::getVolume, order.getVolume() - 1);
                return this.update(update);
            }).withWaitTime(3000).withTimeUnit(TimeUnit.MILLISECONDS).orElse(false);
        }).onFailure(t-> {
            log.warn("[减少客服的接单量异常:]{},{},{}",userId, skillId, t.getMessage());
        }).getOrElse(()->false);
    }

    @Override
    public boolean batchMinusWorkShuntOrderVolume(List<String> skillCodes, List<String> userIds) {
        return Try.of(()->
            RLockUtils.of(String.format(KEY_PREFIX, String.join(",", userIds), String.join(",", skillCodes)), () -> {
                LambdaUpdateWrapper<WorkShuntOrderVolume> update = new LambdaUpdateWrapper<>();
                update.setSql("volume = volume -1");
                update.in(WorkShuntOrderVolume::getSkillCode, skillCodes).in(WorkShuntOrderVolume::getUserId, userIds);
                return this.update(update);
            }).withWaitTime(3000).withTimeUnit(TimeUnit.MILLISECONDS).orElse(false)
        ).onFailure(t-> {
            log.warn("[批量减少客服的接单量异常:]{},{},{}",String.join(",", userIds), String.join(",", skillCodes), t.getMessage());
        }).getOrElse(()->false);
    }

    @Override
    public List<WorkShuntOrderVolume> queryBySkillIdAndUserIds(String skillId, List<String> userIds) {
        LambdaQueryWrapper<WorkShuntOrderVolume> query = new LambdaQueryWrapper<>();
        query.eq(WorkShuntOrderVolume::getSkillId, skillId).in(WorkShuntOrderVolume::getUserId, userIds);
        return this.list(query);
    }

    @Override
    public List<WorkShuntOrderVolume> queryBySKillCode(String code) {
        LambdaQueryWrapper<WorkShuntOrderVolume> query = new LambdaQueryWrapper<>();
        query.eq(WorkShuntOrderVolume::getSkillCode, code);
        return Option.of(this.list(query)).getOrElse(ArrayList::new);
    }

    @Override
    public void updateAssignTime(WorkShuntOrderVolume workShuntOrderVolume) {
        LambdaUpdateWrapper<WorkShuntOrderVolume> update = new LambdaUpdateWrapper<>();
        update.eq(WorkShuntOrderVolume::getSkillId, workShuntOrderVolume.getSkillId())
            .eq(WorkShuntOrderVolume::getUserId, workShuntOrderVolume.getUserId())
            .set(WorkShuntOrderVolume::getAssignTime, workShuntOrderVolume.getAssignTime());
        this.update(update);
    }

    @Override
    public void cleanOrderVolume() {
        workShuntOrderVolumeMapper.clearOrderVolume();
    }
}
