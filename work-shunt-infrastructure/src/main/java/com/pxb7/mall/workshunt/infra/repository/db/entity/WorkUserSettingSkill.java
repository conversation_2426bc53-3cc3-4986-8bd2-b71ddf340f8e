package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作用户的技能(WorkUserSettingSkill)实体类
 *
 * <AUTHOR>
 * @since 2024-08-03 17:57:30
 */
@Getter
@Setter
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "work_user_setting_skill")
public class WorkUserSettingSkill implements Serializable {
    private static final long serialVersionUID = 370534189500212627L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 技能ID
     */
    @TableField(value = "setting_skill_id")
    private String settingSkillId;
    /**
     * 用户ID
     */
    @TableField(value = "work_user_id")
    private String workUserId;
    /**
     * 设置接待上线
     */
    @TableField(value = "reception_max_limit")
    private Long maxLimit;

    /**
     * 接待会话并发上限
     */
    @TableField(value = "reception_session_max_limit")
    private Long maxSessionLimit;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 技能ID
     */
    @TableField(value = "work_skill_id")
    private String workSkillId;


    @TableField(value = "user_id")
    private String userId;

    @TableField(value = "is_able")
    private boolean able;

    /**
     * 部门类型 1公司部门 2号商部门
     */
    @TableField(value = "dept_type")
    private String deptType;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

}

