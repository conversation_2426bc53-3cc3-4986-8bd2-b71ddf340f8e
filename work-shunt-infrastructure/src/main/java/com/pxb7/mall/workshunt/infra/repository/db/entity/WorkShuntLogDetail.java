package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 请求日志记录(WorkShuntLogDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-08-07 15:20:42
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_log_detail")
public class WorkShuntLogDetail implements Serializable {
    private static final long serialVersionUID = 476556750725872763L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分流日志ID
     */
    @TableField(value = "shunt_log_id")
    private String shuntLogId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 业务ID
     */
    @TableField(value = "shunt_log_detail_id")
    private String shuntLogDetailId;
    /**
     * 工单用户ID
     */
    @TableField(value = "work_user_id")
    private String workUserId;
    /**
     * 分流用户ID
     */
    @TableField(value = "pk_user_id")
    private String pkUserId;
    /**
     * 员工姓名
     */
    @TableField(value = "pk_user_name")
    private String pkUserName;
    /**
     * 技能ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 分流ID
     */
    @TableField(value = "skill_code")
    private String skillCode;
    /**
     * 接受状态0待接受1处理中2已完成
     */
    @TableField(value = "shunt_status")
    private String shuntStatus;

}

