package com.pxb7.mall.workshunt.infra.repository.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogSkillCodes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 请求日志记录(WorkShuntLogSkillCodes)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-07 14:54:33
 */
@Mapper
public interface WorkShuntLogSkillCodesMapper extends BaseMapper<WorkShuntLogSkillCodes> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkShuntLogSkillCodes> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkShuntLogSkillCodes> entities);

}

