package com.pxb7.mall.workshunt.infra.enums;

import lombok.Getter;

/**
 * <p>分流规则类型:</p>
 * 作者：rookie
 * 创建日期：2025/07/28
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Getter
public enum ShuntRuleTypeEnum {

    NO_OVERFLOW("1", "非溢出"),
    OVERFLOW("2", "溢出");

    private String shuntRuleType;
    private String desc;

    ShuntRuleTypeEnum(String shuntRuleType, String desc) {
        this.shuntRuleType = shuntRuleType;
        this.desc = desc;
    }
}
