package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSetting;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 技能模版配置(WorkSkillTemplateSetting)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 09:57:08
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkSkillTemplateSettingDbRepository
    extends ServiceImpl<WorkSkillTemplateSettingMapper, WorkSkillTemplateSetting>
    implements WorkSkillTemplateSettingRepository {

    @Override
    public List<WorkSkillTemplateSetting> querySkillTemplateSettingByTemplateId(String templateId) {
        LambdaQueryWrapper<WorkSkillTemplateSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkSkillTemplateSetting::getSkillTemplateId, templateId);
        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteByTemplateId(String templateId) {
        LambdaQueryWrapper<WorkSkillTemplateSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkSkillTemplateSetting::getSkillTemplateId, templateId);
        return this.remove(queryWrapper);
    }
}
