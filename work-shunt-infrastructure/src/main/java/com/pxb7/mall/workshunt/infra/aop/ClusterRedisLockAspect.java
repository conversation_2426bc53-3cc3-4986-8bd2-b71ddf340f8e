package com.pxb7.mall.workshunt.infra.aop;

import com.alibaba.cola.exception.SysException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁切片
 * <AUTHOR>
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
@Component
@Order(-200)
public class ClusterRedisLockAspect {

    private final RedissonClient redissonClient;

    @Around("@annotation(clusterRedisLock)")
    public Object aroundRedisLock(ProceedingJoinPoint point, ClusterRedisLock clusterRedisLock) throws Throwable {
        String prefix = clusterRedisLock.prefix();
        String value = clusterRedisLock.value();
        String lockKey = getCacheKey(point, value);
        log.info("========解析后的lockKey :{}，time = {}", prefix + lockKey, LocalDateTime.now());
        RLock lock = redissonClient.getLock(prefix + lockKey);
        boolean tryLock = lock.tryLock(5, TimeUnit.SECONDS);
        if (!tryLock) {
            throw new SysException("sys500", "获取并发锁失败");
        }
        try {
            return point.proceed();
        } catch (Throwable throwable) {
            log.error("aroundRedisLock 分布式锁异常：{}", throwable.getMessage());
            throw throwable;
        } finally {
            log.info("========解锁 lockKey :{}，time = {}", prefix + lockKey, LocalDateTime.now());
            lock.unlock();
        }
    }

    private String getCacheKey (ProceedingJoinPoint joinPoint, String key) {
        Object[] args = joinPoint.getArgs();
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        //获取被拦截方法参数名列表(使用Spring支持类库)
        StandardReflectionParameterNameDiscoverer localVariableTable = new StandardReflectionParameterNameDiscoverer();
        String[] paraNameArr = localVariableTable.getParameterNames(method);
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        if (paraNameArr != null) {
            for (int i = 0; i < paraNameArr.length; i++) {
                context.setVariable(paraNameArr[i], args[i]);
            }
        }
        StringBuilder builder = new StringBuilder();
        // 使用变量方式传入业务动态数据
        if (key.matches("^#.*.$")) {
            String[] split = key.split(",");
            for (String s : split) {
                if (builder.length() <= 0) {
                    builder.append(parser.parseExpression(s).getValue(context, String.class));
                } else {
                    builder.append(":").append(parser.parseExpression(s).getValue(context, String.class));
                }
            }
        }
        return builder.toString();
    }

}


