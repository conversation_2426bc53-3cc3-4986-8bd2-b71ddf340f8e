package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 请求日志记录(WorkShuntLog)实体类
 *
 * <AUTHOR>
 * @since 2024-08-10 17:03:57
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_log")
public class WorkShuntLog implements Serializable {
    private static final long serialVersionUID = -10145888474520181L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分流日志ID
     */
    @TableField(value = "shunt_log_id")
    private String shuntLogId;
    /**
     * ShuntTypeEnums.class
     * IM_REGION(1, "IM域"),
     * product_REGION(2, "商品工单域"),
     * After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    @TableField(value = "shunt_type")
    private String shuntType;
    /**
     * 请求游戏ID
     */
    @TableField(value = "work_game_id")
    private String workGameId;
    /**
     * 请求游戏名称
     */
    @TableField(value = "work_game_name")
    private String workGameName;
    /**
     * 请求人数
     */
    @TableField(value = "work_user_num")
    private Integer workUserNum;
    /**
     * 请求时间
     */
    @TableField(value = "work_time")
    private LocalDateTime workTime;
    /**
     * 返回时间
     */
    @TableField(value = "work_out_time")
    private LocalDateTime workOutTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 技能IDs,逗号隔开
     */
    @TableField(value = "skill_ids")
    private String skillIds;
    /**
     * 用户名子,逗号隔开
     */
    @TableField(value = "pk_user_names")
    private String pkUserNames;
    /**
     * 请求类型1:排班分流2通知类型
     */
    @TableField(value = "req_type")
    private String reqType;

}

