package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 请求日志记录(WorkShuntLogSkillCodes)实体类
 *
 * <AUTHOR>
 * @since 2024-08-07 15:08:28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_log_skill_codes")
public class WorkShuntLogSkillCodes implements Serializable {
    private static final long serialVersionUID = -41384386571130989L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分流日志ID
     */
    @TableField(value = "shunt_log_id")
    private String shuntLogId;
    /**
     * 技能ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 技能名称
     */
    @TableField(value = "skill_name")
    private String skillName;
    /**
     * 技能code
     */
    @TableField(value = "skill_code")
    private String skillCode;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

