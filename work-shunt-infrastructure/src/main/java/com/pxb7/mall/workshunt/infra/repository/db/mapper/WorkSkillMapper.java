package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 技能表(WorkSkill)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 20:34:07
 */
@Mapper
public interface WorkSkillMapper extends BaseMapper<WorkSkill> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkSkill> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkSkill> entities);

    Page<SkillPageListPO> listPage(IPage<SkillPageListPO> page);

    List<UserSkillAllPO> all();

}

