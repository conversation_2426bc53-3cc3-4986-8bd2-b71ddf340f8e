package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntRuleSettingConditionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分流规则设置参数业务(WorkShuntRuleSettingCondition)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 15:06:25
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntRuleSettingConditionDbRepository
    extends ServiceImpl<WorkShuntRuleSettingConditionMapper, WorkShuntRuleSettingCondition>
    implements WorkShuntRuleSettingConditionRepository {

    @Override
    public List<WorkShuntRuleSettingCondition> queryBy(String skillId, String shuntRuleType) {
        LambdaQueryWrapper<WorkShuntRuleSettingCondition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkShuntRuleSettingCondition::getSkillId, skillId).eq(WorkShuntRuleSettingCondition::getShuntRuleType, shuntRuleType);

        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteBy(String skillId) {
        LambdaQueryWrapper<WorkShuntRuleSettingCondition> delete = new LambdaQueryWrapper<>();
        delete.eq(WorkShuntRuleSettingCondition::getSkillId, skillId);
        return this.remove(delete);
    }

    @Override
    public List<WorkShuntRuleSettingCondition> queryBy(List<String> skillIds, String shuntRuleType) {
        LambdaQueryWrapper<WorkShuntRuleSettingCondition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkShuntRuleSettingCondition::getSkillId, skillIds).eq(WorkShuntRuleSettingCondition::getShuntRuleType, shuntRuleType);
        return this.list(queryWrapper);
    }
}
