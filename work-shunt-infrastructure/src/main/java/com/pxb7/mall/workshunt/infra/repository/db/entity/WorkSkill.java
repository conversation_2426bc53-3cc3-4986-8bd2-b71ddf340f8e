package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技能表(WorkSkill)实体类
 *
 * <AUTHOR>
 * @since 2024-08-01 20:34:07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_skill")
public class WorkSkill implements Serializable {
    private static final long serialVersionUID = -83940678976646333L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 技能ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 技能名称
     */
    @TableField(value = "skill_name")
    private String skillName;
    /**
     * 技能code
     */
    @TableField(value = "skill_code")
    private String skillCode;
    /**
     * 技能类型ID
     */
    @TableField(value = "skill_type_id")
    private String skillTypeId;
    /**
     * 是否关联游戏1：是0否
     */
    @TableField(value = "is_skill_relation_game")
    private Boolean skillRelationGame;
    /**
     * 排序值
     */
    @TableField(value = "skill_sort")
    private Integer skillSort;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 初开始数据
     */
    @TableField(value = "early_start")
    private Integer earlyStart;
    /**
     * 初结束数据
     */
    @TableField(value = "early_end")
    private Integer earlyEnd;
    /**
     * 中开始数据
     */
    @TableField(value = "middle_start")
    private Integer middleStart;
    /**
     * 中结束数据
     */
    @TableField(value = "middle_end")
    private Integer middleEnd;
    /**
     * 高开始数据
     */
    @TableField(value = "high_start")
    private Integer highStart;
    /**
     * 高结束数据
     */
    @TableField(value = "high_end")
    private Integer highEnd;
    /**
     * 专家开始数据
     */
    @TableField(value = "expert_start")
    private Integer expertStart;
    /**
     * 专家结束数据
     */
    @TableField(value = "expert_end")
    private Integer expertEnd;

    /**
     * 是否删除 0:否, 1:是
     */
    @TableField(value = "is_deleted")
    private Boolean deleted = false;

    /**
     * 修改用户ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "create_user_id")
    private String createUserId;
}

