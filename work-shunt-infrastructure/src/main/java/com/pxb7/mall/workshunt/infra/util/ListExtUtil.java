package com.pxb7.mall.workshunt.infra.util;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;

@Slf4j
public class ListExtUtil {
    private static final int MAX_SIZE = 500;

    private static final int CORE_POOL_SIZE = 30;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder()
            .setNameFormat("ListExtUtil-worker-%s")
            .setUncaughtExceptionHandler((t, e) -> log.error("unknown exception, thread:{}", t.getName(), e))
            .build();
    private static final ThreadPoolExecutor listExtUtilExecutorService = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_SIZE,
            60, TimeUnit.SECONDS,
            new SynchronousQueue<>(), THREAD_FACTORY, new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 分段查询数据，大list参数转化为小list查询
     *
     * @param paramList  所有的list参数集合
     * @param f          查询方法
     * @param size       分段尺寸，建议50
     * @param concurrent 是否并发查询
     * @param <T>        返回对象
     * @param <R>        参数对象
     * @return
     */
    public static <T, R> List<T> segmentQuery(List<R> paramList, Function<List<R>, List<T>> f,
                                              int size, boolean concurrent) {
        if (CollUtil.isEmpty(paramList)) {
            return Collections.emptyList();
        }
        if (paramList.size() < size) {
            return f.apply(paramList);
        }

        List<List<R>> list = Lists.partition(paramList, size);

        if (concurrent) {
            return list.stream()
                    .map(p -> CompletableFuture.supplyAsync(() -> f.apply(p), listExtUtilExecutorService))
                    .toList()
                    .stream().map(CompletableFuture::join)
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(Collection::stream)
                    .toList();
        } else {
            return list.stream().map(f)
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(Collection::stream).toList();
        }
    }
}
