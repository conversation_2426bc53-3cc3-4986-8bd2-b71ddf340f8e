package com.pxb7.mall.workshunt.infra.enums;

import lombok.Getter;

/**
 * <p>功能描述:</p>
 * 作者：rookie
 * 创建日期：2025/07/28
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Getter
public enum ShuntRuleConditionEnum {

    //未溢出
    NO_OVERFLOW_WORK_TIME_NOW(  "1", "1", "当前时间排班内"),
    NO_OVERFLOW_NO_OVER_MAX_LIMIT(  "1", "2", "未超接待上限制"),
    NO_OVERFLOW_IM_ONLINE(  "1", "3", "IM在线"),

    //溢出
    OVERFLOW_WORK_TIME_NOW(  "1", "1", "当前时间排班内"),
    OVERFLOW_NO_OVER_MAX_LIMIT(  "1", "2", "未超接待上限制"),
    OVERFLOW_IM_ONLINE(  "1", "3", "IM在线")

    ;

    private final String shuntRuleType;

    private final String conditionType;

    private final String desc;

    ShuntRuleConditionEnum(String shuntRuleType, String conditionType, String desc) {
        this.shuntRuleType = shuntRuleType;
        this.conditionType = conditionType;
        this.desc = desc;
    }
}
