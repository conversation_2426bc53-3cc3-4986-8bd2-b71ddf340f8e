package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技能模版关联游戏(WorkSkillTemplateSettingGameList)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 10:50:05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_skill_template_setting_game_list")
public class WorkSkillTemplateSettingGameList implements Serializable {
    private static final long serialVersionUID = 885365356779371494L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务模板ID
     */
    @TableField(value = "template_setting_id")
    private String templateSettingId;
    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 游戏名称
     */
    @TableField(value = "game_name")
    private String gameName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

