package com.pxb7.mall.workshunt.infra.enums;

import lombok.Getter;

@Getter
public enum ShuntTypeEnums {
    //    分流规则类型1非溢出状态2溢出状态
    IM_REGION(1, "IM域"),
    product_REGION(2, "商品工单域"),
    After_sale_REGION(3, "售后工单域"),
    complain_REGION(4, "客诉工单域");
    ShuntTypeEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    private Integer type;
    private String desc;
}
