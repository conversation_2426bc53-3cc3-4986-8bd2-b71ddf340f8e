package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkSkillCodeGroupShuntPO;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkUserPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;

import java.util.List;

/**
* 排班用户 (WorkUser) 表服务接口
*/
public interface WorkUserRepository extends IService<WorkUser> {

    /**
    * 排班用户 分页查询
    * @param po
    * @return
    */
    Page<WorkUser> listPageWorkUser(WorkUserPageListPO po);

    WorkUser getTableOneDataByPkUserId(String pkUserId);

    List<WorkUser> queryWorkUsers(List<String> userIds);

    /**
     * 查询所有状态的workUser
     * @param userId
     * @return
     */
    WorkUser queryAllStatusWorkUserByUserId(String userId);

    List<WorkUser> query(List<String> workerIds, String merchantId);


    List<WorkUser> getWorkUserBySkillCode(String skillCode);

    List<WorkUser> getWorkUserBySkillCodeAndGameId(String skillCode, String gameId);

    List<WorkUser> workSkillCodeGroupShunt(WorkSkillCodeGroupShuntPO bo);

    List<WorkUser> getWorkUserByMerchantId(String merchantId);


    void updateWorkUserActivity(String userId, String status);


    void updateWorkUserOnline(String userId, Integer imStatus);

    void updateWorkUserDepartment(String userId, String departmentId, String merchantId);


    void deleteWorkUser(String userId);

    /**
     * 查询不包括该用户的排班人员信息
     * @param userId
     * @return
     */
    List<WorkUser> queryWorkUsersExcludeSelfUserId(String userId);

    List<WorkUser> queryByWorkUserIds(List<String> workUserIds);


    void relateTemplate(String workUserId, String templateId);

    List<WorkUser> queryByTemplateId(String templateId);


    List<WorkUser> queryByUserIds(List<String> userIds);

}

