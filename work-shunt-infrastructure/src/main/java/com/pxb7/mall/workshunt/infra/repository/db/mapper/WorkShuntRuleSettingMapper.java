package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分流规则设置参数(WorkShuntRuleSetting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 15:05:59
 */
@Mapper
public interface WorkShuntRuleSettingMapper extends BaseMapper<WorkShuntRuleSetting> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkShuntRuleSetting> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkShuntRuleSetting> entities);

}

