package com.pxb7.mall.workshunt.infra.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillBoard;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillPagePO;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingSkillMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作用户的技能(WorkUserSettingSkill)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-03 16:30:46
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkUserSettingSkillDbRepository extends ServiceImpl<WorkUserSettingSkillMapper, WorkUserSettingSkill>
    implements UserSettingSkillRepository {

    @Resource
    private WorkUserSettingSkillMapper workUserSettingSkillMapper;
    @Override
    public List<WorkUserSettingSkill> queryAllByWorkUserId(String workUserId) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.eq(WorkUserSettingSkill::getWorkUserId,workUserId);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<WorkUserSettingSkill> queryBySkillId(String skillId) {
        return this.list(new LambdaQueryWrapper<WorkUserSettingSkill>().eq(WorkUserSettingSkill::getWorkSkillId, skillId).eq(WorkUserSettingSkill::isAble, true));
    }

    @Override
    public WorkUserSettingSkill queryBySkillIdAndWorkUserId(String skillId, String workUserId) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.eq(WorkUserSettingSkill::getWorkUserId, workUserId)
            .eq(WorkUserSettingSkill::getWorkSkillId, skillId).eq(WorkUserSettingSkill::isAble, true);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<WorkUserSettingSkill> queryBySkillIds(List<String> skilllIds, boolean enable) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.in(WorkUserSettingSkill::getWorkSkillId, skilllIds).eq(WorkUserSettingSkill::isAble, enable);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<WorkUserSettingSkill> queryBySettingSkillId(List<String> settingSkillIds) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.in(WorkUserSettingSkill::getSettingSkillId, settingSkillIds).eq(WorkUserSettingSkill::isAble, true);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public List<WorkUserSettingSkill> queryByWorkUserIds(List<String> workUserIds) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.in(WorkUserSettingSkill::getWorkUserId, workUserIds).eq(WorkUserSettingSkill::isAble, true);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public WorkUserSettingSkill queryByWorkUserIdAndSkillId(String workUserId, String skillId) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.eq(WorkUserSettingSkill::getWorkUserId, workUserId).eq(WorkUserSettingSkill::getWorkSkillId, skillId)
            .eq(WorkUserSettingSkill::isAble, true);

        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public Page<WorkUserSettingSkillBoard> queryWorkUserSettingPage(WorkUserSettingSkillPagePO po) {
        Page<WorkUserSettingSkillBoard> page = new Page<>(po.getPageIndex(), po.getPageSize());
        return workUserSettingSkillMapper.queryWorkUserSettingPage(page, po.getDeptId(), po.getUserIds(), po.getSkillId(), po.getImStatus());
    }

    @Override
    public Page<WorkUserSettingSkillBoard> queryWorkUserSettingByGamePage(WorkUserSettingSkillPagePO po) {
        Page<WorkUserSettingSkillBoard> page = new Page<>(po.getPageIndex(), po.getPageSize());
        return workUserSettingSkillMapper.queryWorkUserSettingByGameIdPage(page, po.getDeptId(), po.getUserIds(), po.getSkillId(), po.getImStatus(), po.getGameId());
    }

    @Override
    public Boolean removeUserSettingByWorkUserId(String workUserId) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.eq(WorkUserSettingSkill::getWorkUserId, workUserId);
        return this.remove(lambdaQueryWrapper);
    }

    public List<WorkUserSettingSkillGameList> queryBySKillIdsAndGameId(List<String> skillId, String gameId) {
        return workUserSettingSkillMapper.queryBySKillIdsAndGameId(skillId, gameId);
    }

    @Override
    public Map<String, Long> totalWorkUserRelateSkillCount(List<String> workUserIds) {
        List<WorkUserSettingSkill> workUserSettingSkills = this.list(
                new LambdaQueryWrapper<WorkUserSettingSkill>().in(WorkUserSettingSkill::getWorkUserId, workUserIds)
        );

        if(CollUtil.isEmpty(workUserSettingSkills)){
            return new HashMap<>();
        }
        return workUserSettingSkills.stream().collect(Collectors.groupingBy(WorkUserSettingSkill::getWorkUserId, Collectors.counting()));
    }


    @Override
    public List<WorkUserSettingSkill> queryAllStatusByWorkUserIds(List<String> workUserIds) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.in(WorkUserSettingSkill::getWorkUserId, workUserIds);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public void updateWorkUserRoomMaxLimit(String userSettingSkillId, Long maxLimit) {

        LambdaUpdateWrapper<WorkUserSettingSkill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WorkUserSettingSkill::getMaxLimit, maxLimit).eq(WorkUserSettingSkill::getSettingSkillId, userSettingSkillId);
        this.update(updateWrapper);
    }

    @Override
    public void updateWorkUserSessionMaxLimit(String userSettingSkillId, Long maxLimit) {
        LambdaUpdateWrapper<WorkUserSettingSkill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WorkUserSettingSkill::getMaxSessionLimit, maxLimit).eq(WorkUserSettingSkill::getSettingSkillId, userSettingSkillId);
        this.update(updateWrapper);
    }

    @Override
    public List<WorkUserSettingSkill> queryByWorkUserIdsAndSkillId(List<String> workUserIds, String skillIds) {
        LambdaQueryWrapper<WorkUserSettingSkill> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingSkill>();
        lambdaQueryWrapper.in(CollUtil.isNotEmpty(workUserIds), WorkUserSettingSkill::getWorkUserId, workUserIds)
                .eq(CharSequenceUtil.isNotEmpty(skillIds), WorkUserSettingSkill::getWorkSkillId, skillIds).eq(WorkUserSettingSkill::isAble, true);

        return this.list(lambdaQueryWrapper);
    }
}
