package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作用户(WorkUser)实体类
 *
 * <AUTHOR>
 * @since 2024-08-02 16:29:56
 */

@Accessors(chain = true)
@TableName(value = "work_user")
public class WorkUser implements Serializable {
    private static final long serialVersionUID = 542175744613807392L;
    /**
     * 主键
     */
    @Getter
    @Setter
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工作用户ID
     */
    @Getter
    @Setter
    @TableField(value = "work_user_id")
    private String workUserId;
    /**
     * 部门ID
     */
    @Getter
    @Setter
    @TableField(value = "dept_id")
    private String deptId;
    /**
     * 用户ID
     */
    @Getter
    @Setter
    @TableField(value = "user_id")
    private String userId;
    /**
     * 创建时间
     */
    @Getter
    @Setter
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 部门类型1公司部门2号商部门
     */
    @Getter
    @Setter
    @TableField(value = "dept_type")
    private String deptType;
    /**
     * 号商ID
     */
    @Getter
    @Setter
    @TableField(value = "merchant_id")
    private String merchantId;

    /**
     * 用户是否激活:当前用户在用户中心中的状态: 事件驱动：新增 2修改 3删除 4禁用 5启用
     */
    @Getter
    @Setter
    @TableField(value = "is_active")
    private Boolean isActive;

    /**
     * 用户是否在IM在线: 1在线 0 离线
     */
    private Boolean isOnline;

    /**
     * 用户在IM的状态: 1=在线 2=离线 3=暂离
     */
    @Getter
    @Setter
    private Integer imStatus;

    @Getter
    @Setter
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;

    @Getter
    @Setter
    @TableField(value = "template_id")
    private String templateId;

    public Boolean getIsOnline() {
        return getImStatus() != null && getImStatus() != 2 && getImStatus() != 3;
    }

    public void setOnline(Boolean online) {
        isOnline = online;
    }
}

