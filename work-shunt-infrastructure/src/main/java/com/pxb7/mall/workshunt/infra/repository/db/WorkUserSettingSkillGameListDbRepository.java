package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingSkillGameListMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
@DS("mysql")
public class WorkUserSettingSkillGameListDbRepository
    extends ServiceImpl<WorkUserSettingSkillGameListMapper, WorkUserSettingSkillGameList>
    implements WorkUserSettingSkillGameListRepository{

    @Resource
    private WorkUserSettingSkillGameListMapper workUserSettingSkillGameListMapper;

    @Override
    public void removeBatchBySkillId(List<String> skillId) {
        LambdaQueryWrapper<WorkUserSettingSkillGameList> query = new LambdaQueryWrapper<>();
        query.in(WorkUserSettingSkillGameList::getSetttingSkillId, skillId);
        this.remove(query);
    }

    @Override
    public List<WorkUserSettingSkillGameList> queryBySettingSkillId(String settingSkillId) {
        LambdaQueryWrapper<WorkUserSettingSkillGameList> query = new LambdaQueryWrapper<>();
        query.eq(WorkUserSettingSkillGameList::getSetttingSkillId, settingSkillId);
        return this.list(query);
    }


    @Override
    public List<WorkUserSettingSkillGameList> queryBySettingSkillId(List<String> ids) {
        return workUserSettingSkillGameListMapper.queryBySettingSkillId(ids) ;
    }

    @Override
    public List<WorkUserSettingSkillGameList> queryBySettingSkillIdByGameIds(List<String> gameIds) {
        LambdaQueryWrapper<WorkUserSettingSkillGameList> query = new LambdaQueryWrapper<>();
        query.in(WorkUserSettingSkillGameList::getGameId, gameIds);
        return this.list(query);
    }
}
