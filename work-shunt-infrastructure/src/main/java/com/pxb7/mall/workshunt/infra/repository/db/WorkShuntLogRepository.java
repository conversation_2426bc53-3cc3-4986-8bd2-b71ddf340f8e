package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.model.workShuntLog.WorkShuntLogPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLog;

/**
* 请求日志记录 (WorkShuntLog) 表服务接口
*/
public interface WorkShuntLogRepository extends IService<WorkShuntLog> {

    /**
    * 请求日志记录 分页查询
    * @param po
    * @return
    */
    Page<WorkShuntLog> listPageWorkShuntLog(WorkShuntLogPageListPO po);


    /**
    * 请求日志记录 获取一个详情
    * @param key
    * @return
    */
    WorkShuntLog getTableOneDataByParamKey(String key);

    Page<WorkShuntLog> pages(WorkShuntLogPageListPO po);


    /**
     * 物理删除
     * @param date
     */
    void deleteByTime(String date);


}

