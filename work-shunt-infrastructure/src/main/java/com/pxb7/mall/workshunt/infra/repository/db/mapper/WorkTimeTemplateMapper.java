package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作时间模版(WorkTimeTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-01 20:35:03
 */
@Mapper
public interface WorkTimeTemplateMapper extends BaseMapper<WorkTimeTemplate> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkTimeTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkTimeTemplate> entities);

}

