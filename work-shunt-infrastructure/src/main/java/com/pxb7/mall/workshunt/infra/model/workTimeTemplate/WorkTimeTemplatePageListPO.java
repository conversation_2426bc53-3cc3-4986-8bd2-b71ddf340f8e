package com.pxb7.mall.workshunt.infra.model.workTimeTemplate;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


                        
@Data
public class WorkTimeTemplatePageListPO implements Serializable {

    /**
    * 第几页
    */
    private Integer pageIndex;
    /**
    * 每页条数
    */
    private Integer pageSize;

    /**
     * 主键
     */
    private Long id;
    /**
     * 业务ID
     */
    private String workTemplateId;
    /**
     * 标题
     */
    private String title;
        /**
         *
         *
        * 开始时间
        */
        private String startTime ;
        /**
        * 结束时间
        */
        private String endTime ;
        /**
        * 创建时间
        */
        private LocalDateTime createTime ;
    }