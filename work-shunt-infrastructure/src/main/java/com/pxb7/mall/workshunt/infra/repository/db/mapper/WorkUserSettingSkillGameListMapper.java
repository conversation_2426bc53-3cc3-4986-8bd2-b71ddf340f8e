package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作用户关联游戏(WorkUserSettingSkillGameList)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-03 16:31:39
 */
@Mapper
public interface WorkUserSettingSkillGameListMapper extends BaseMapper<WorkUserSettingSkillGameList> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkUserSettingSkillGameList> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkUserSettingSkillGameList> entities);

    List<WorkUserSettingSkillGameList> queryBySettingSkillId(@Param("ids") List<String> ids);

}

