package com.pxb7.mall.workshunt.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorCode {

    //错误码规则 公共支撑模块-消息中心 comm60001 -comm60500 排班分流 comm60501-601000 风控601001-601500
    NO_DATA_MSG("500", "数据不存在或已被删除"),
    EXITS_DATA_MSG("500", "数据已存在，请勿重复操作"),
    UPDATE_DB_FAILED("500", "更新数据库失败"),
    DB_ERROR("500", "数据库执行失败"),
    DB_TOO_MANY_RESULTS_ERROR("500", "查询到多条数据"),

    SUCCESS("00000", "成功"),
    SIGN_ERROR("00001", "认证失败"),

    NO_PARAMS("00013", "缺少参数|不能为空"),

    SYSTEM_ERROR("00009", "系统内部错误"),

    DUBBO_USER_ERROR("00010", "调用用户域dubbo异常"),
    DUBBO_PRODUCT_ERROR("00011", "调用商品域失败"),

    USER_NOT_FOUND("50000", "用户不存在"),
    USER_NOT_WECHAT_NOT_OFFICIAL("50001", "用户不存在或未关注公众号"),


    //  公共支撑模块-消息中心 comm60001 -comm60500
    MESSAGE_CHANNEL_ERROR("comm60001", "消息渠道错误"),

    CALL_SEATS_BUSY("comm60002", "坐席繁忙，请更换坐席"),
    CALL_SEATS_NOT_EXIST("comm60003", "坐席不存在请联系管理员"),

    REDIRECT_SEATS_ERROR("60011", "链接id不能为空"),
    REDIRECT_SEATS_NOT_EXIST("comm60004", "没有找到该数据"),
    REDIRECT_EXITS_DATA_MSG("comm60005", "数据已存在，请勿重复操作"),
    REDIRECT_REPLACE_PARAMS_NOT_NULL("comm60008", "跳转链接不能为空"),

    REDIRECT_REPLACE_PARAM_NO_PARAMS("comm60006", "替换参数不能为空"),
    REDIRECT_REPLACE_PARAM_ABNORMAL("comm60007", "替换参数异常"),


    //外呼中心
    EXISTS_SAME_DATA("comm60004", "已有此名称"),
    PHONE_BINDING_SAME_TYPE("comm60005", "当前号码已经添加了该类型，请勿重复操作"),


    // 消息组合配置
    NOTICE_TASK_CONFIG_SUPPORT_TYPE_DUPLICATE_ERROR("comm60006", "消息组合配置所选类型有重复"),
    NOTICE_TASK_CONFIG_SUPPORT_TYPE_NOT_EQUALS_ERROR("comm60007", "消息组合配置所选类型和具体配置类型列表有出入"),
    NOTICE_TASK_CONFIG_MAX_DELAY_EXCEED_LIMIT_ERROR("comm60008", "消息组合配置累计间隔分钟超过最大值%s"),
    NOTICE_TASK_CONFIG_CANNOT_REMOVE_ERROR("comm60009", "消息组合配置被上游业务引用，无法被删除"),
    NOTICE_TASK_CONFIG_DELAY_MINUTE_INVALID_ERROR("comm60010", "消息组合配置多业务类型下最低优先级间隔时间不能都为0"),


    EXISTS_SAME_PHONE_NUMBER("comm60009", "已有重复号码，请勿重复添加"),
    EXISTS_SAME_CNO("comm60010", "已有重复号码，请勿重复添加"),
    EXISTS_SAME_TEMPLATE("comm60011", "当前模板ID已使用，请勿重复操作"),
    EXISTS_SAME_TAGE("comm60012", "已有标识，请重新填写"),
    EXISTS_SAME_MSG_CHANNEL("comm60013", "已存在该类型，请勿重复添加"),
    NECESSARY_PARAMETER_LOSS("comm60014", "缺失必要参数，请检查"),
    UPLOAD_FILE_FORMAT_ERROR("comm60015", "上传文件错误，请上传以.xls或.xlsx结尾的Excel文件"),

    MSG_TEMPLATE_NOT_EXIST("comm60016", "消息模板不存在"),
    CALL_SEAT_USE_UP("comm60017", "暂无空闲外呼坐席，请稍后重新拨打！"),

    NOT_FOUND_USER("comm60018", "获取用户openid失败"),

    CHOSE_MSG_CHANNEL("comm60019", "选择发送消息渠道"),

    UPLOAD_FILE_FAILURE("comm60020", "上传文件失败"),
    IMPORT_MAX_USERID_SIZE_ERROR("comm60045", "excel上传行数超出上限值%s"),
    FILE_CONTENT_ERROR("comm60046", "内容解析失败"),
    FILE_NAME_NOT_EXISTS("comm60047", "文件名为空"),
    EXISTS_SAME_APP_VERSION("comm60021", "已有此版本号"),
    EXISTS_SAME_TITLE("comm60022", "已有此标题"),
    EXISTS_SAME_JOB_NAME("comm60023", "已有此岗位名称"),
    EXISTS_SAME_SINGLE_DATA("comm60024", "已存在相同数据"),
    EXISTS_SAME_BUSINESS_DATA("comm60025", "已存在相同业务名称"),
    APP_VERSION_ERROR("comm60026", "版本号错误，不是数字型"),
    EXISTS_SAME_SINGLE_CODE("comm60027", "已存在相同别名"),
    EXISTS_SAME_SINGLE_NAME("comm60028", "已存在相同名称"),
    OUTBOUND_SERVER_EXCEPTION("comm60029", "外呼服务异常"),
    EXISTS_SUBLEVEL_DATA("comm60030", "当前数据下存在关联数据,不能删除"),
    RETRY_GET_IDLE_SEAT("comm60031", "坐席暂不可用，请重新拨打"),
    NOT_SETTING_AUTO_SEAT("comm60032", "请设置自动外呼坐席"),
    ADMIN_ACCOUNT_USED("comm60032", "该管理员已被使用"),
    BATCH_CONFIG_INTERVAL("comm60033", "时间间隔必须填写"),
    SMS_TEMPLATE_CLOSE("comm60034", "短信模板已关闭"),

    MESSAGE_BIZ_TYPE_USED("comm60035", "已有模板使用此类型，无法删除"),

    NOT_ALLOW_CALL_WITHIN_TIME("comm60036", "当前时间内不允许拨打"),
    PHONE_NUMBER_IN_BLACKLIST("comm60037", "黑名单号码禁止外呼"),

    USER_CLOSE_PHONE_NOTIFICATION("comm60038", "用户已关闭通话通知"),

    NOT_IN_TIME_USER_ANSWER_PHONE("comm60039", "该用户不在接听时间范围内，请稍后重新拨打！ （ 建议拨打时间%s）"),

    USER_CALL_LIMIT_DAILY("common60400", "自动外呼%s:配置上线：%s，实际接通次数：%s"),

    USER_CALL_LIMIT_DAILY_CLOSE("common60401", "自动外呼%s:每天最高受限为0"),
    OSS_SIGN_EXCEPTION("comm60040", "oss签名异常"),
    OSS_STS_EXCEPTION("comm60041", "oss获取临时凭证异常"),

    SMS_NO_ACCESS_EXCEPTION("comm60042", "sms密钥不存在"),
    SMS_TEMPLATE_EMPTY("comm60043", "短信模版code为空"),
    SMS_PHONE_EMPTY("comm60044", "短信手机号为空"),

    AUTO_OUTBOUND_PHONE_FORMAT("comm60045", "自动外呼仅支持中国国内手机号"),
    MSG_TEMPLATE_USERID_TEMPLATE_NAME_NOT_EXIST("comm60046", "用户id或模版名称不能为空"),
    MSG_TEMPLATE_JG_REGISTRATION_ID_NOT_EXIST("comm60047", "用户极光id不能为空"),

    WECHAT_MESSAGE_TEMPLATE_EXIST("comm60048", "微信模版id或模版名称已存在"),
    WECHAT_MESSAGE_TEMPLATE_NOT_EXIST("comm60049", "微信模板不存在"),
    WECHAT_MESSAGE_TEMPLATE_PARAMS_ERROR("comm60050", "微信模板参数错误"),
    WECHAT_MESSAGE_TEMPLATE_PARAMS_ABNORMAL("comm60051", "DUBBO接口参数异常"),
    WECHAT_MESSAGE_TEMPLATE_FIELD_PARAM_ERROR("comm60052", "上游参数和默认值必选其一"),
    WECHAT_MESSAGE_TEMPLATE_FIELD_EXIST("comm60053", "微信模板字段已存在"),
    WECHAT_MESSAGE_TEMPLATE_FIELD_NOT_EXIST("comm60054", "微信模板字段不存在"),
    WECHAT_MESSAGE_TEMPLATE_FIELD_ID_NOT_NULL("comm60055", "微信模板字段ID不能为空"),
    WECHAT_MESSAGE_TEMPLATE_ID_NOT_NULL("comm60056", "微信模板ID不能为空"),
    WECHAT_MESSAGE_TEMPLATE_STATUS_NOT("comm60057", "微信模板未启用"),

    //风控601001-601500


    RISK_VIRTUAL_NUMBER_EXIST("601001", "虚拟号段已经存在,无需重复添加"),
    RISK_VIRTUAL_BLACKLIST_TYPE_EXIST("601002", "黑名单类型已经存在,请勿重复操作"),
    RISK_CHECK_NUM_GT_ZERO("601003", "值必须大于0"),
    RISK_CHECK_NAME_EXITS("601004", "名称已经存在"),
    RISK_CHECK_WHITELIST_NAME_EXITS("601005", "渠道+账号重复"),
    RISK_CHECK_WHITELIST_PHONE_NOT_REG_EXITS("601071", "手机号未注册"),


    RISK_CHECK_SCENE_RULE_SCENE_EXITS("601006", "场景已添加，请勿重复添加"),

    RISK_CHECK_SCENE_RULE_STATUS_TRUE("601007", "风控场景为开启状态，无法删除"),
    RISK_CHECK_SCENE_RULE_SCENE_STATUS_TRUE("601008", "状态为开启中，无法删除"),
    RISK_CHECK_SCENE_RULE_SERVER_STATUS_TRUE("601009", "业务域状态为开启中，无法删除"),

    RISK_CHECK_SCENE_EXIST("601010", "业务场景已经存在，请勿重复操作"),
    RISK_CHECK_SCENE_ROUTE_EXIST("601011", "渠道已存在，请勿重复操作"),

    RISK_CHECK_ROUTE_CALL_NUM_EXIST("601012", "渠道访问超上线"),

    RISK_BLACKLISTTYPE_REF_OR_OPEN_STATUS("601013", "类型启用中或被黑名单引用无法删除"),
    RISK_BLACKLISTTYPE_REF_OR_CLOSE_STATUS("601014", "黑名单类型被引用无法关闭"),

    RISK_CHECK_ROUTE_SCENE_NOT_EXIST("601015", "场景未配置不进行查询"),

    RISK_BLACKLIST_TYPE_NOT_ENABLE("601016", "黑名单类型未启用，无法添加黑名单"),


    RISK_USER_QUERY_COUNT("601030", "24小时内,只查询一次"),
    RISK_USER_QUERY_GAME_ACCOUNT("601032", "查询游戏账号不能为空"),

    RISK_CHECK_OTHER_PLAT_EXITS("601050", "配置参数已经存在，请勿重复添加"),
    RISK_CHECK_EXPORT_DATA_EMPTY("601051", "导出数据为空"),
    RISK_CHECK_ACCOUNT_EXIST_MSG("601052", "当前账号已添加"),

    RISK_CHECK_ACCOUNT_500("601053", "上传数据不能超过500"),

    RISK_CHECK_BLACKLIST_ACCOUNT_EXIST("601054", "黑名单信息已经存在，请不要重复操作"),

    RISK_CHECK_ARGUMENT_OPEN_STATE("601055", "状态开启中无法删除"),
    RISK_CHECK_ARGUMENT_REF_SERVER("601056", "规则被引用，无法被删除"),
    RISK_CHECK_ARGUMENT_REF_CHECK("601057", "规则被场景引用,无法关闭"),

    RISK_CHECK_EXCEL_FORMAT("601058", "excel文件格式错误"),
    RISK_CHECK_FILE_NOT_EXISTS("601059", "文件名为空"),
    RISK_CHECK_EXCEL_PROCESS_ERROR("601060", "excel文件处理出错"),

    RISK_CHECK_GET_SELL_USERINFO_ERROR("601070", "查找卖家信息失败"),
    RISK_CHECK_GET_SELL_PRODUCT_ERROR("601071", "查询商品信息不存在"),
    RISK_EXTERNAL_QUERY_ERROR("601072", "查询外部黑号出错"),
    RISK_BLACK_RULE_COMPILE_ERROR("601073", "黑号规则编译出错"),

    RISK_CHECK_EXTERNAL_CODE_EXITS("601081", "渠道已存在"),

    API_QUERY_ERROR_PARAMS("10001", "参数错误"),
    API_QUERY_ERROR_SING("10002", "验签不正确"),
    API_QUERY_ERROR_SING_TIME_OUT("10003", "验签已过期"),
    API_QUERY_ERROR_CHANNEL_ACCOUNT_LENGTH("10004", "游戏账号长度超过30"),
    API_QUERY_ERROR_CHANNEL_QUERY_TRANSFINITE("10005", "渠道日访问超限"),

    API_QUERY_ERROR_CHANNEL_QUERY_RATE_LIMITER("10006", "访问频率超过限制"),
    API_QUERY_ERROR_CHANNEL_CLOSE("10007", "渠道关闭"),
    API_QUERY_ERROR_NOT_CALL_NUMBER("10008", "未配置次数限制"),
    API_QUERY_ERROR_EXCEED_CALL_NUMBER("10009", "超出次数限制"),

    //排班分流
    WORK_TIME_TEMPLATE_SAVE("common600501", "保存班次异常"),
    WORK_TIME_CHECK_REPEAT_TEMPLATE_SAVE("common600501", "班次名称已存在"),
    WORK_TIME_TEMPLATE_EDIT("common600502", "编辑班次异常"),
    WORK_TIME_TEMPLATE_DEL("common600503", "编辑班次异常"),
    WORK_TIME_TEMPLATE_EXIST("common600504", "班次不存在"),

    WORK_SKILL_SAVE("common600511", "保存技能异常"),
    WORK_SKILL_DEL("common600512", "删除技能异常"),
    WORK_SKILL_EDIT("common600513", "编辑技能异常"),
    WORK_SKILL_EXIST("common600514", "技能异常不存在"),
    WORK_SKILL_NAME_DUPLICATE("common600515", "技能已存在"),

    WORK_SKILL_SORT_IN_SKILL_TYPE_DUPLICATE("common600516", "同技能分类中已存在该排序值"),
    WORK_SKILL_NAME_DETAIL("common600517", "获取技能详情失败"),
    NO_DATA_MESSAGE("common600518", "数据不存在或已被删除"),
    MOBILE_SKILL_SHUNT_DATA_ERROR_ERROR("common600516", "结束会话数据不符合，请联系平台"),
    MOBILE_SKILL_SHUNT_BUSY_ERROR("common600517", "当前客服繁忙，请稍后再试"),

    WORK_SKILL_NO_EXITS("common600518", "不存在该技能"),
    WORK_SKILL_NO_CLIENT("common600519", "没有该技能的客服"),
    WORK_SKILL_USER_NO_MATCH_SKILL_COUNT("common600519", "客服和技能数量不匹配"),

    WORK_SKILL_NO_MATCH_GAME("common600520", "该技能没有匹配到游戏"),

    WORK_SKILL_USER_COUNT_NO_MATCH_SKILL("common600521", "请求分配该技能的客服数与实际拥有该技能客服数不匹配"),

    WORK_SKILL_NEED_USER_COUNT("common600522", "需要提供需要客服的数量"),

    WORK_SKILL_SETTING_USER_SKILL_NEED_GAME("common600536", "该技能需要关联游戏"),

    WORK_SKILL_SETTING_USER_NO_DATA("common600524", "客服设置技能：设置的技能没有关联游戏和设置接待上限”"),

    WORK_SKILL_SETTING_RELATION_NO_GAME("common600523", "客服设置的技能没有设置游戏"),

    WORK_SKILL_SETTING_SIZE_LIMIT("common600523", "客服设置的技能不能超20个"),

    WORK_SKILL_SETTING_NO_MAX_LIMIT("common600524", "技能没有设置接待上限"),

    WORK_SKILL_SETTING_HAVE_VOLUME("common600525", "该技能已有客服正在处理，无法将接单量设置关闭"),

    WORK_SKILL_ASSIGNED_SEARCH_PARAM("common600525", "查询条件: 部门和人员不能同时为空值"),

    WORK_SKILL_SETTING_NO_LIMIT_MAX("common600526", "需要设置房间接待量上限"),

    WORK_SKILL_SETTING_NO_SESSION_LIMIT_MAX("common700526", "需要设置会话并发上限"),

    WORK_SKILL_TIME_TEMPLATE_DELETE_USER_SETTING("common600527", "班次模版已绑定到客服班次上，无法删除"),

    WORK_SKILL_TIME_TEMPLATE_EDIT_USER_SETTING("common600535", "班次模版已绑定到客服班次上，无法编辑"),

    WORK_SKILL_ORDER_VOLUME_NO_DATA("common600528", "不存接单数据"),

    WORK_SKILL_ORDER_VOLUME_ZERO("common600529", "当前该技能已无接单任务可关闭"),

    WORK_SKILL_NO_WORK_USER("common600530", "客服不存在"),

    WORK_SKILL_USER_NO_MATCH_RULE("common600531", "该客服无法增加工作量，不符合排班规则"),

    WORK_SKILL_TEMPLATE_NAME_EXITS("common600532", "模版名称重复"),

    WORK_SKILL_TEMPLATE_ERRORS("common600533", "保存模版异常"),

    WORK_SKILL_TEMPLATE_NO_SKILL("common600535", "模版中不存在技能"),

    WORK_SKILL_TEMPLATE_NO_TEMPLATED_ID("common600536", "模版ID不存在"),

    WORK_SKILL_SETTING_MERCHANT_USER_ERROR("common600537", "该客服为号商客服，请将客服和商号进行绑定"),

    WORK_SKILL_SETTING_ERROR("common600538", "设置技能异常"),

    WORK_SKILL_SETTING_ROLE_ERROR("common600539", "当前账号无法分流，请先关联官方客服/号商客服权限"),


    //易盾
    YIDUN_RETURN_ERROR("comm603001", "易盾返回失败"),

    TEXT_INPUT_EMPTY_ERROR("comm603002", "传入的文本数据为空"),
    IMAGE_INPUT_EMPTY_ERROR("comm603003", "传入的图片数据为空"),
    NOT_SUPPORTED_TYPE("comm603004", "不支持的类型"),
    OVER_CENSOR_COUNT_LIMIT("comm603005", "超出最大数量限制"),

    //短链接
    SHORT_URL_HAS_ONE_ERROR("comm603006", "短链接已存在,请稍后重试"),
    SHORT_URL_ADD_ERROR("comm603007", "添加失败"),
    SHORT_URL_UPDATE_ERROR("comm603007", "编辑失败"),
    SHORT_URL_NO_INFO_ERROR("comm603008", "链接信息不存在"),
    SHORT_URL_NO_PARAM_CODE("comm603010", "摩西摩西"),
    SHORT_URL_NO_SHORT_URL("comm603009", "链接不存在"),

    // GIO
    GIO_INTERNAL_ORDER_NO_NOT_NULL("comm600701", "消息唯一标识不能为空"),
    GIO_USER_ID_NOT_NULL("comm600702", "用户ID不能为空"),
    GIO_GLOBAL_PARAM_NOT_NULL("comm600703", "全局参数不能为空"),
    GIO_SIGNATURE_EXCEPTION("comm600704", "签名异常"),
    GIO_ONE_MAX_NUM("comm600705", "单次最多发送%s条"),
    GIO_DATA_EXISTS("comm600706", "数据已存在"),
    GIO_MAX_EXCEEDING_TIME("comm600707", "请求时间超过最大限制"),
    GIO_PARAM_EXCEPTION("comm600708", "参数异常"),


    // 频繁请求
    TOO_MANY_REQUESTS("comm60050", "请求过于频繁，请稍后再试"),

    ID_CARD_REQUESTS("comm60050", "实名认证失败"),
    ;

    private final String errCode;
    private final String errDesc;

}
