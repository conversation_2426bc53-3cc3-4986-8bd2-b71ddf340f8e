package com.pxb7.mall.workshunt.infra.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.Properties;

@Getter
@Configuration
@Slf4j
public class ShuntScheduleConfig extends AbstractAppConfig {


    private static final String SHUNT_SCHEDULE_FEISHU = "shunt.schedule.feishu";


    @Value("${shunt.schedule.feishu}")
    private String feiShuRebootUrl;

    @Override
    protected void loadProperties(Properties properties) {
        if (Objects.isNull(properties)) {
            log.warn("拉取的配置为空");
            return;
        }
        String value = properties.getProperty(SHUNT_SCHEDULE_FEISHU);
        if (StringUtils.isBlank(value)) {
            log.warn("nacos的shunt.schedule.feishu配置变更为空字符串，不更新");
            return;
        }
        feiShuRebootUrl = value;
        log.info("nacos的shunt.schedule.feishue配置变更为{}", feiShuRebootUrl);
    }
}
