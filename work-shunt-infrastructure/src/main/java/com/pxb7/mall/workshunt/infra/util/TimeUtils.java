package com.pxb7.mall.workshunt.infra.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/9 21:19
 */
public class TimeUtils {


    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * Date转换成LocalDateTime
     * @param dateTime
     * @return
     */
    public static LocalDateTime convertDateTimeToLocal(Date dateTime) {
        if (Objects.isNull(dateTime)) {
            return null;
        }
        return dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转换成时间字符串
     * @param localDateTime
     * @return
     */
    public static String convertLocalDateTimeToDateStr(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        return localDateTime.format(DATE_FORMATTER);
    }

    /**
     * 时间字符串转换成LocalDateTime
     * @param dateTimeStr
     * @return
     */
    public static LocalDateTime parseDateTimeStr(String dateTimeStr) {
        // 将时间字符串转换成LocalDateTime
        return LocalDateTime.parse(dateTimeStr, DATE_TIME_FORMATTER);
    }

    /**
     * 获取当前时间字符串
     * @return
     */
    public static String getNowStr() {
        return LocalDateTime.now().format(DATE_TIME_FORMATTER);
    }

    /**
     * 获取当天剩余时间
     */
    public static long getTodayRemainingTime() {
        // 获取当前日期时间
        LocalDateTime now = LocalDateTime.now();

        // 计算当天结束时间（23:59:59.999）的时间戳
        long endOfDayTimestamp = now.withHour(23).withMinute(59).withSecond(59).withNano(999_000_000)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        // 返回剩余时间（毫秒）
        return endOfDayTimestamp - System.currentTimeMillis();
    }

    /**
     * 计算增加指定时间后的时间
     * @param hours
     * @param minutes
     * @param seconds
     * @return
     */
    public static LocalDateTime addSpecificTime(Integer hours, Integer minutes, Integer seconds) {
        // 获取当前日期时间
        LocalDateTime newDateTime = LocalDateTime.now();
        // 增加指定时间
        if (Objects.nonNull(hours)) {
           newDateTime = newDateTime.plusHours(hours);
        }
        if (Objects.nonNull(minutes)) {
            newDateTime = newDateTime.plusMinutes(minutes);
        }
        if (Objects.nonNull(seconds)) {
            newDateTime = newDateTime.plusSeconds(seconds);
        }
        return newDateTime;
    }

    /**
     * 计算增加指定时间后的时间
     * @param hours
     * @param minutes
     * @param seconds
     * @return
     */
    public static LocalDateTime addSpecificTime(LocalDateTime dateTime, Integer hours, Integer minutes, Integer seconds) {
        LocalDateTime newDateTime = dateTime;
        // 增加指定时间
        if (Objects.nonNull(hours)) {
            newDateTime = newDateTime.plusHours(hours);
        }
        if (Objects.nonNull(minutes)) {
            newDateTime = newDateTime.plusMinutes(minutes);
        }
        if (Objects.nonNull(seconds)) {
            newDateTime = newDateTime.plusSeconds(seconds);
        }
        return newDateTime;
    }

    /**
     * 计算减去指定时间后的时间
     * @param hours
     * @param minutes
     * @param seconds
     * @return
     */
    public static LocalDateTime subtractSpecificTime(LocalDateTime dateTime, Integer hours, Integer minutes, Integer seconds) {
        LocalDateTime newDateTime = dateTime;
        // 增加指定时间
        if (Objects.nonNull(hours)) {
            newDateTime = newDateTime.minusHours(hours);
        }
        if (Objects.nonNull(minutes)) {
            newDateTime = newDateTime.minusMinutes(minutes);
        }
        if (Objects.nonNull(seconds)) {
            newDateTime = newDateTime.minusSeconds(seconds);
        }
        return newDateTime;
    }

    /**
     * 计算剩余时间
     * @param dateTime 时间
     * @return
     */
    public static Long leftSeconds(LocalDateTime dateTime) {
        return ChronoUnit.SECONDS.between(LocalDateTime.now(), dateTime);
    }

    /**
     * 计算剩余时间
     * @param dateTime 时间
     * @return
     */
    public static Long leftMinutes(LocalDateTime dateTime) {
        return ChronoUnit.MINUTES.between(LocalDateTime.now(), dateTime);
    }

    /**
     * 计算已经过去的时间
     * @param dateTime 时间
     * @return
     */
    public static Long pastSeconds(LocalDateTime dateTime) {
        return ChronoUnit.SECONDS.between(dateTime, LocalDateTime.now());
    }

    /**
     * 计算已经过去的时间
     * @param dateTime 时间
     * @return
     */
    public static Long pastMinutes(LocalDateTime dateTime) {
        return ChronoUnit.MINUTES.between(dateTime, LocalDateTime.now());
    }

    /**
     * 计算时间差（分钟）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public static Long lastMinutes(LocalDateTime startTime, LocalDateTime endTime) {
        return ChronoUnit.MINUTES.between(startTime, endTime);
    }

    /**
     * 将分钟格式化为xx天xx小时xx分钟
     * @param totalMinutes 分钟
     * @return 格式化后的字符串
     */
    public static String formatDuration(long totalMinutes) {
        long days = totalMinutes / (24 * 60);
        long hours = (totalMinutes % (24 * 60)) / 60;
        long minutes = totalMinutes % 60;
        String formateDuration = "";
        if (days > 0) {
            formateDuration += days + "天";
        }
        if (hours > 0) {
            formateDuration += hours + "小时";
        }
        if (minutes > 0) {
            formateDuration += minutes + "分钟";
        }
        return formateDuration;
    }

}
