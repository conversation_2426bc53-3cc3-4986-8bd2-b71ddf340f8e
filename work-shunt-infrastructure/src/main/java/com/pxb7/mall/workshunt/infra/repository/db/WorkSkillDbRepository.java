package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.model.skill.WorkSkillPageListPo;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 技能管理 (WorkSkill)表服务实现类
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkSkillDbRepository extends ServiceImpl<WorkSkillMapper, WorkSkill> implements WorkSkillRepository {

    @Resource
    private WorkSkillMapper workSkillMapper;

    /**
     * 技能管理 分页查询
     *
     * @param dto
     * @return
     */
    @Override
    public Page<SkillPageListPO> listPage(WorkSkillPageListPo dto) {
        IPage<SkillPageListPO> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        return workSkillMapper.listPage(page);
    }


    @Override
    public List<UserSkillAllPO> all() {
        return workSkillMapper.all();
    }

    /**
     * 技能管理 获取一个详情
     *
     * @param key
     * @return
     */
    public WorkSkill getTableOneDataByParamKey(String key) {
        return workSkillMapper.selectOne(new LambdaQueryWrapper<WorkSkill>().eq(WorkSkill::getSkillId, key).eq(WorkSkill::getDeleted, false));
    }

    @Override
    public WorkSkill getTableOneDataByParamKeyCode(String keyCode) {
        return workSkillMapper.selectOne(new LambdaQueryWrapper<WorkSkill>().eq(WorkSkill::getSkillCode, keyCode).eq(WorkSkill::getDeleted, false));
    }

    @Override
    public List<WorkSkill> queryBySkillCode(List<String> cdoe) {
        LambdaQueryWrapper<WorkSkill> queryWrapper = new LambdaQueryWrapper<WorkSkill>().in(WorkSkill::getSkillCode, cdoe)
            .eq(WorkSkill::getDeleted, false);

        return workSkillMapper.selectList(queryWrapper);
    }

    @Override
    public List<WorkSkill> getWorkSkillAll() {
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkSkill::getDeleted,false);
        lambdaQueryWrapper.orderByDesc(WorkSkill::getCreateTime);
        return list(lambdaQueryWrapper);
    }

    @Override
    public boolean isExitsSkillStoreInSkillType(String skillId, String skillTypeId, Integer skillSort) {
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkSkill::getDeleted,false);
        lambdaQueryWrapper.eq(WorkSkill::getSkillTypeId, skillTypeId).eq(WorkSkill::getSkillSort, skillSort).ne(WorkSkill::getSkillId, skillId);
        return this.workSkillMapper.selectCount(lambdaQueryWrapper) > 0;
    }

    @Override
    public boolean isExitsSkillStoreInSkillType(String skillTypeId, Integer skillSort) {
        LambdaQueryWrapper<WorkSkill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkSkill::getDeleted,false);
        lambdaQueryWrapper.eq(WorkSkill::getSkillTypeId, skillTypeId).eq(WorkSkill::getSkillSort, skillSort);
        return this.workSkillMapper.selectCount(lambdaQueryWrapper) > 0;
    }
}
