package com.pxb7.mall.workshunt.infra.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cola.exception.BizException;
import com.alibaba.nacos.api.config.listener.AbstractListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ByteArrayResource;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 动态更新nacos配置抽象类，需要支持更新继承它
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractAppConfig {

    @Resource
    private NacosConfigManager nacosConfigManager;

    @Value("#{'${spring.application.name}' + '-' + '${spring.profiles.active}' + '.' + '${spring.cloud.nacos.config.file-extension}'}")
    private String dataId;

    private static final String GROUP_ID = "DEFAULT_GROUP";

    @PostConstruct
    public void init() throws Exception {
        // 启动时先拉取
        boolean connected = checkConnection(dataId, nacosConfigManager.getConfigService().getConfig(dataId, GROUP_ID, 5000));
        if (!connected) {
            throw new BizException(String.format("拉取nacos配置失败,dataId:%s,groupId:%s", dataId, GROUP_ID));
        }
        // 然后注册监听器变化时拉取
        nacosConfigManager.getConfigService().addListener(dataId, GROUP_ID, new AbstractListener() {
            @Override
            public void receiveConfigInfo(String configInfo) {
                listenWithConfig(dataId, configInfo);
            }
        });
    }

    private boolean checkConnection(String dataId, String configInfo){
        log.info("[receiveConfigInfo]:{}", configInfo);
        try {
            YamlPropertiesFactoryBean factoryBean = new YamlPropertiesFactoryBean();
            factoryBean.setResources(new ByteArrayResource(configInfo.getBytes(StandardCharsets.UTF_8)));
            return true;
        } catch (Exception e) {
            log.warn("获取nacos配置异常,dataId:{},groupId:{}", dataId, GROUP_ID, e);
            return false;
        }
    }

    private void listenWithConfig(String dataId, String configInfo) {
        log.info("[receiveConfigInfo]:{}", configInfo);
        try {
            YamlPropertiesFactoryBean factoryBean = new YamlPropertiesFactoryBean();
            factoryBean.setResources(new ByteArrayResource(configInfo.getBytes(StandardCharsets.UTF_8)));
            loadProperties(factoryBean.getObject());
        } catch (Exception e) {
            log.warn("获取nacos配置异常,dataId:{},groupId:{}", dataId, GROUP_ID, e);
        }
    }

    protected abstract void loadProperties(Properties properties);
}
