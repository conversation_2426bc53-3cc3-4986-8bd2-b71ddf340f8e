package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;

import java.util.List;

/**
 * 工作用户关联游戏(WorkUserSettingSkillGameList)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-03 16:31:39
 */
public interface WorkUserSettingSkillGameListRepository extends IService<WorkUserSettingSkillGameList> {

    void removeBatchBySkillId(List<String> skillId);

    List<WorkUserSettingSkillGameList> queryBySettingSkillId(String settingSkillId);


    List<WorkUserSettingSkillGameList> queryBySettingSkillId(List<String> ids);

    List<WorkUserSettingSkillGameList> queryBySettingSkillIdByGameIds(List<String> gameIds);
}

