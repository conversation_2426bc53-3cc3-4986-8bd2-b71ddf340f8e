package com.pxb7.mall.workshunt.infra.enums;

import com.alibaba.cola.exception.BizException;
import lombok.Getter;

/**
 * <p>功能描述:</p>
 * 作者：rookie
 * 创建日期：2025/07/28
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Getter
public enum ShuntAssignAlgorithmEnum {

    NO_ASSIGN("0", "0", "不分配"),

    ROOM_AVERAGE("1", "1", "平均分配(按房间)"),
    RATIO("2", "1", "比例分配"),
    SESSION_AVERAGE("3", "1", "平均分配(按会话)"),

    ;
    final String shuntAlgorithmType;
    final String shuntRuleType;
    final String desc;

    ShuntAssignAlgorithmEnum(String shuntAlgorithmType, String shuntRuleType, String desc) {
        this.shuntAlgorithmType = shuntAlgorithmType;
        this.shuntRuleType = shuntRuleType;
        this.desc = desc;
    }

    public static ShuntAssignAlgorithmEnum getByShuntAlgorithmType(String shuntAlgorithmType) {
        for (ShuntAssignAlgorithmEnum value : ShuntAssignAlgorithmEnum.values()) {
            if (value.getShuntAlgorithmType().equals(shuntAlgorithmType)) {
                return value;
            }
        }
        throw new BizException("work-shunt003", "无效的分配策略");
    }
}
