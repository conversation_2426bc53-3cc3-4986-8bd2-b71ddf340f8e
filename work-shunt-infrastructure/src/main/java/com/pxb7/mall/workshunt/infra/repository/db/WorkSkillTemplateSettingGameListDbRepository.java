package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSettingGameList;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateSettingGameListMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 技能模版关联游戏(WorkSkillTemplateSettingGameList)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 09:57:55
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkSkillTemplateSettingGameListDbRepository
    extends ServiceImpl<WorkSkillTemplateSettingGameListMapper, WorkSkillTemplateSettingGameList>
    implements WorkSkillTemplateSettingGameListRepository {

    @Override
    public List<WorkSkillTemplateSettingGameList> queryByTemplateSettingId(String id) {
        return this.list(new LambdaQueryWrapper<WorkSkillTemplateSettingGameList>().eq(WorkSkillTemplateSettingGameList::getTemplateSettingId, id));
    }

    @Override
    public boolean deleteByTemplateSettingId(List<String> ids) {
        return this.remove(new LambdaQueryWrapper<WorkSkillTemplateSettingGameList>().in(WorkSkillTemplateSettingGameList::getTemplateSettingId, ids));
    }
}
