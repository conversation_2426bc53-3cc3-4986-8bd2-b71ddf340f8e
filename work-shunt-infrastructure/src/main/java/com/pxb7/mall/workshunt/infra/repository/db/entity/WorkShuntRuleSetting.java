package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分流规则设置参数(WorkShuntRuleSetting)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 17:50:36
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_rule_setting")
public class WorkShuntRuleSetting implements Serializable {
    private static final long serialVersionUID = 376414011847575737L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分流类型ID
     */
    @TableField(value = "shunt_rule_id")
    private String shuntRuleId;
    /**
     * 分流规则ID
     */
    @TableField(value = "shunt_setting_rule_id")
    private String shuntSettingRuleId;
    /**
     * 分流规则类型1非溢出状态2溢出状态
     */
    @TableField(value = "shunt_rule_type")
    private String shuntRuleType;
    /**
     * 分流规则:0不分配-1轮流分配 2 饱和度分配
     */
    @TableField(value = "shunt_type")
    private String shuntType;

    @TableField(value="skill_id")
    private String skillId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

