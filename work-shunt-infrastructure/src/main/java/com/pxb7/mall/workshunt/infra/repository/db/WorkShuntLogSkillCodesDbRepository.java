package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogSkillCodes;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogSkillCodesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 请求日志记录(WorkShuntLogSkillCodes)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-07 14:54:33
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntLogSkillCodesDbRepository
    extends ServiceImpl<WorkShuntLogSkillCodesMapper, WorkShuntLogSkillCodes>
    implements WorkShuntLogSkillCodesRepository {

    @Override
    public List<WorkShuntLogSkillCodes> getListWorkShuntLogSkillCodesByShuntLogId(String shuntLogId) {
        LambdaQueryWrapper<WorkShuntLogSkillCodes> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkShuntLogSkillCodes::getShuntLogId,shuntLogId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<WorkShuntLogSkillCodes> getListWorkShuntLogSkillCodesByShuntLogIds(List<String> shuntLogIds) {
        LambdaQueryWrapper<WorkShuntLogSkillCodes> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(WorkShuntLogSkillCodes::getShuntLogId, shuntLogIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public void deleteByTime(String date) {
        this.remove(Wrappers.<WorkShuntLogSkillCodes>query().lambda().lt(WorkShuntLogSkillCodes::getCreateTime, date));
    }

}
