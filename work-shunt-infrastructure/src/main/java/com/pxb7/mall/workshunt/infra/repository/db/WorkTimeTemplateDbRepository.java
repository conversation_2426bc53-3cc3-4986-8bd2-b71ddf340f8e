package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.model.workTimeTemplate.WorkTimeTemplatePageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkTimeTemplateMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* 工作时间模版 (WorkTimeTemplate)表服务实现类
*
*/
@Slf4j
@Repository
@DS("mysql")
public class WorkTimeTemplateDbRepository extends ServiceImpl<WorkTimeTemplateMapper, WorkTimeTemplate> implements WorkTimeTemplateRepository {

    @Resource
    private WorkTimeTemplateMapper workTimeTemplateMapper;


    /**
    * 工作时间模版 分页查询
    * @param po
    * @return
    */
    @Override
    public Page<WorkTimeTemplate> listPageWorkTimeTemplate(WorkTimeTemplatePageListPO po) {
        Page<WorkTimeTemplate> page = new Page<WorkTimeTemplate>(po.getPageIndex(), po.getPageSize());
        return workTimeTemplateMapper.selectPage(page, new LambdaQueryWrapper<WorkTimeTemplate>().eq(WorkTimeTemplate::getDeleted, false)
            .orderByDesc(WorkTimeTemplate::getCreateTime));
    }


    /**
    * 工作时间模版 获取一个详情
    * @param key
    * @return
    */
    public WorkTimeTemplate getTableOneDataByParamKey(String key){
        return workTimeTemplateMapper.selectOne(new LambdaQueryWrapper<WorkTimeTemplate>()
            .eq(WorkTimeTemplate::getTimeTemplateId, key));
    }

    @Override
    public boolean update(WorkTimeTemplate timeTemplate) {
        LambdaUpdateChainWrapper<WorkTimeTemplate> update = new LambdaUpdateChainWrapper<>(workTimeTemplateMapper);
        update.eq(WorkTimeTemplate::getTimeTemplateId, timeTemplate.getTimeTemplateId())
            .set(WorkTimeTemplate::getTitle, timeTemplate.getTitle())
            .set(WorkTimeTemplate::getStartTime, timeTemplate.getStartTime())
            .set(WorkTimeTemplate::getUpdateTime, timeTemplate.getUpdateTime())
            .set(WorkTimeTemplate::getEndTime, timeTemplate.getEndTime());

        return update.update();
    }

    @Override
    public List<WorkTimeTemplate> queryByIds(List<String> templateIds) {
        LambdaQueryWrapper<WorkTimeTemplate> query = new LambdaQueryWrapper<>();
        query.in(WorkTimeTemplate::getTimeTemplateId, templateIds);
        return this.list(query);
    }

    @Override
    public boolean checkRepeat(String templateName) {
        LambdaQueryWrapper<WorkTimeTemplate> query = new LambdaQueryWrapper<>();
        query.eq(WorkTimeTemplate::getTitle, templateName).eq(WorkTimeTemplate::getDeleted, false);
        return baseMapper.selectCount(query) > 0;
    }

    @Override
    public boolean checkRepeat(String templateName, String workTimeTemplateId) {
        LambdaQueryWrapper<WorkTimeTemplate> query = new LambdaQueryWrapper<>();
        query.eq(WorkTimeTemplate::getTitle, templateName).eq(WorkTimeTemplate::getDeleted, false)
            .ne(WorkTimeTemplate::getTimeTemplateId, workTimeTemplateId);
        return baseMapper.selectCount(query) > 0;
    }
}
