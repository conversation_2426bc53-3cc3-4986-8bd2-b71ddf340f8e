package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作用户设置时间模版(WorkUserSettingDayRelationWorkTemplat)实体类
 *
 * <AUTHOR>
 * @since 2024-08-02 20:50:04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_user_setting_day_relation_work_templat")
public class WorkUserSettingDayRelationWorkTemplat implements Serializable {
    private static final long serialVersionUID = 954100396488340316L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 配置时间ID
     */
    @TableField(value = "setting_day_id")
    private String settingDayId;
    /**
     * 关联模版ID
     */
    @TableField(value = "work_template_id")
    private String workTemplateId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

