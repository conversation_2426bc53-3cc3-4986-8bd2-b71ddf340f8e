package com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.common;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.api.message.FeishuService;
import com.pxb7.mall.common.client.request.message.BotMessageTextReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:08
 */

@Repository
@Slf4j
public class MessageGateway {

    @DubboReference
    private FeishuService feishuService;

    /**
     * 发送飞书消息
     * @param msgReqDTO
     * @return
     */
    public Boolean botTextMessage(BotMessageTextReqDTO msgReqDTO) {
        try {
            SingleResponse<Boolean> response = feishuService.botTextMessage(msgReqDTO);
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
            log.error("Fail to send message to the feishuService#botTextMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        } catch (Exception e) {
            log.error("Fail to send message to the feishuService#botTextMessage,msgReqDTO:{}", JSON.toJSONString(msgReqDTO));
            return null;
        }
    }
}
