package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDayRelationWorkTemplat;

import java.util.List;

/**
 * 工作用户设置时间模版(WorkUserSettingDayRelationWorkTemplat)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-02 20:50:06
 */
public interface WorkUserSettingDayRelationWorkTemplatRepository
    extends IService<WorkUserSettingDayRelationWorkTemplat> {


    boolean deleteByTimeTemplate(String settingId);


    List<WorkUserSettingDayRelationWorkTemplat> queryBy(List<String> settingIds);

    List<WorkUserSettingDayRelationWorkTemplat> queryBy(String templateId);


    List<WorkUserSettingDayRelationWorkTemplat> listWorkUserSettingDayRelationWorkTemplatBySettingDdyId(String settingDayId);

}

