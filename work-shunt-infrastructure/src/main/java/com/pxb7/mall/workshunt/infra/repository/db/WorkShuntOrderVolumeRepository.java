package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntOrderVolume;

import java.util.List;

/**
 * (com.pxb7.mall.common.infra.repository.db.mysql.entity.WorkShuntOrderVolume)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-10 17:23:13
 */

public interface WorkShuntOrderVolumeRepository extends IService<WorkShuntOrderVolume> {

    /**
     * 根据技能和客服获取接单量
     *
     * @param skillId
     * @param userId
     * @return
     */
    WorkShuntOrderVolume queryBySkillIdAndUserId(String skillId, String userId);

    List<WorkShuntOrderVolume> queryBySkillIdsAndUserIds(List<String> skillIds, List<String> userIds);

    /**
     * 增加接单量
     * @param skillId
     * @param userId
     * @return
     */
    boolean addWorkShuntOrderVolume(String skillId, String userId);

    /**
     * 递减接单
     * @param skillId
     * @param userId
     * @return
     */
    boolean minusWorkShuntOrderVolume(String skillId, String userId);

    boolean batchMinusWorkShuntOrderVolume(List<String> skillCodes, List<String> userId);


    List<WorkShuntOrderVolume> queryBySkillIdAndUserIds(String skillId, List<String> userIds);

    /**
     * 更具技能编码获取接单量数据
     * @param code
     * @return
     */
    List<WorkShuntOrderVolume> queryBySKillCode(String code);

    void updateAssignTime(WorkShuntOrderVolume workShuntOrderVolume);

    /**
     * 清除接单量
     */
    void cleanOrderVolume();

}
