package com.pxb7.mall.workshunt.infra.config;

import com.alibaba.fastjson2.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.pxb7.mall.workshunt.infra.enums.LocalOrRedisCacheEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
@Slf4j
public class CacheManagerConfig {
    @Bean("localCacheManager")
    public CacheManager localCacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        List<CaffeineCache> caches = new ArrayList<>();
        for (LocalOrRedisCacheEnum item : LocalOrRedisCacheEnum.values()) {
            if (item.isLocalFlag()) {
                caches.add(new CaffeineCache(item.getName(),
                        Caffeine.newBuilder().recordStats()
                                .expireAfterWrite(item.getTtl() <= 0 ? Integer.MAX_VALUE : item.getTtl(), TimeUnit.MILLISECONDS)
                                .maximumSize(item.getCapacity())
                                .removalListener((key, value, cause) -> {
                                    if (cause.wasEvicted()) {
                                        log.info("caffeine cache evicted, key={}, value={}", key, JSON.toJSONString(value));
                                    }
                                })
                                .build()));
            }
        }
        cacheManager.setCaches(caches);
        return cacheManager;
    }

    @Primary
    @Bean("distributedCacheManager")
    public CacheManager distributedCacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();
        for (LocalOrRedisCacheEnum item : LocalOrRedisCacheEnum.values()) {
            if (!item.isLocalFlag()) {
                CacheConfig cacheConfig = new CacheConfig(item.getTtl(), item.getMaxIdleTime());
                cacheConfig.setMaxSize(item.getCapacity());
                config.put(item.getName(), cacheConfig);
            }
        }
        return new RedissonSpringCacheManager(redissonClient, config, new JsonJacksonCodec());
    }
}
