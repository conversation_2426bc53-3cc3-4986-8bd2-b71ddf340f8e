package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSetting;

import java.util.List;

/**
 * 技能模版配置(WorkSkillTemplateSetting)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-05 09:57:08
 */
public interface WorkSkillTemplateSettingRepository extends IService<WorkSkillTemplateSetting> {

    List<WorkSkillTemplateSetting> querySkillTemplateSettingByTemplateId(String templateId);

    boolean deleteByTemplateId(String templateId);


}

