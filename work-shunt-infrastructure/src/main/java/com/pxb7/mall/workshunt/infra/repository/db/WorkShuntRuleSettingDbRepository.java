package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntRuleSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分流规则设置参数(WorkShuntRuleSetting)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-05 15:05:59
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntRuleSettingDbRepository extends ServiceImpl<WorkShuntRuleSettingMapper, WorkShuntRuleSetting>
    implements WorkShuntRuleSettingRepository {

    @Override
    public boolean deleteBySkillId(String skillId) {
        LambdaQueryWrapper<WorkShuntRuleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkShuntRuleSetting::getSkillId, skillId);
        return this.remove(queryWrapper);
    }

    @Override
    public WorkShuntRuleSetting queryBy(String skillId, String shuntRuleType) {
        LambdaQueryWrapper<WorkShuntRuleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkShuntRuleSetting::getSkillId, skillId).eq(WorkShuntRuleSetting::getShuntRuleType, shuntRuleType);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<WorkShuntRuleSetting> queryBy(List<String> skillIds, String shuntRuleType) {
        LambdaQueryWrapper<WorkShuntRuleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkShuntRuleSetting::getSkillId, skillIds).eq(WorkShuntRuleSetting::getShuntRuleType, shuntRuleType);
        return this.list(queryWrapper);
    }

    @Override
    public List<WorkShuntRuleSetting> queryBy(List<String> skillIds) {
        LambdaQueryWrapper<WorkShuntRuleSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkShuntRuleSetting::getSkillId, skillIds);
        return this.list(queryWrapper);
    }
}
