package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate;

import java.util.List;

/**
 * 技能模版(WorkSkillTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-05 09:56:24
 */
public interface WorkSkillTemplateRepository extends IService<WorkSkillTemplate> {

    /**
     * 技能模板 获取一个详情
     * @param skillTemplateId
     * @return
     */
    WorkSkillTemplate getTableOneDataByParamKey(String skillTemplateId);

    List<WorkSkillTemplate> queryByTemplateIds(List<String> templateIds);

    WorkSkillTemplate getWorkSkillTemplateByName(String templateName);

    List<WorkSkillTemplate> allDataOrderByCreateDate();

    boolean deleteByTemplateId(String templateId);

}

