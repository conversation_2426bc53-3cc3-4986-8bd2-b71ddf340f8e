package com.pxb7.mall.workshunt.infra.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "oss.aliyun")
public class AliYunOssProperties {
    private String stsEndpoint;
    private String endpoint;
    private String bucketName;
    private String acsRam;
    private String regionId;
    private String roleSessionName;

    private String cdnDomain;
    private String uploadDomain;
}
