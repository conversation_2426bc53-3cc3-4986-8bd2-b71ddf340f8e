package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作用户设置时间(WorkUserSettingDay)实体类
 *
 * <AUTHOR>
 * @since 2024-08-02 16:34:19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_user_setting_day")
public class WorkUserSettingDay implements Serializable {
    private static final long serialVersionUID = -79624705688417292L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 配置时间ID
     */
    @TableField(value = "setting_day_id")
    private String settingDayId;
    /**
     * 工作用户ID
     */
    @TableField(value = "work_user_id")
    private String workUserId;
    /**
     * 月份
     */
    @TableField(value = "month")
    private String month;
    /**
     * 天
     */
    @TableField(value = "day")
    private String day;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;


    @TableField(value = "user_id")
    private String userId;


}

