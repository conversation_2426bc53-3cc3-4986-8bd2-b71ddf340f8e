package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (com.pxb7.mall.common.infra.repository.db.mysql.entity.WorkShuntOrderVolume)实体类
 *
 * <AUTHOR>
 * @since 2024-09-10 17:23:00
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_order_volume")
public class WorkShuntOrderVolume implements Serializable {
    private static final long serialVersionUID = -29135694039927061L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "volume_id")
    private String volumeId;
    /**
     * 技能ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 技能编码
     */
    @TableField(value = "skill_code")
    private String skillCode;
    /**
     * 客服名称
     */
    @TableField(value = "user_id")
    private String userId;
   
    /**
     * 接单量
     */
    @TableField(value = "volume")
    private Integer volume;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 分配时间
     */
    @TableField(value = "assign_time")
    private LocalDateTime assignTime;

}

