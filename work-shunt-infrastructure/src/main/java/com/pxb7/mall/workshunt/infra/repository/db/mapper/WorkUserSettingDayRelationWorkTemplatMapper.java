package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDayRelationWorkTemplat;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作用户设置时间模版(WorkUserSettingDayRelationWorkTemplat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-02 20:50:05
 */
@Mapper
public interface WorkUserSettingDayRelationWorkTemplatMapper extends BaseMapper<WorkUserSettingDayRelationWorkTemplat> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkUserSettingDayRelationWorkTemplat> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkUserSettingDayRelationWorkTemplat> entities);

}

