package com.pxb7.mall.workshunt.infra.model.skill;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSkillAllPO {
    /**
     * 技能类型名称
     */
    private String skillTypeName;

    /**
     * 技能接待人数上限
     */
    private Long maxLimit;

    /**
     * 会话并发上限
     */
    private Long maxSessionLimit;

    private boolean hasMaxLimitRule;

    private boolean hasMaxSessionLimitRule;

    /**
     * 该技能是否禁用
     */
    private boolean able;

    /**
     * 关联游戏数量
     */
    private int relationGameCount;


    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 技能名称
     */
    private String skillName;
    /**
     * 技能code
     */
    private String skillCode;
    /**
     * 技能类型ID
     */
    private String skillTypeId;
    /**
     * 是否关联游戏1：是0否
     */
    private boolean skillRelationGame;


    /**
     * 初开始数据
     */
    private Integer earlyStart;
    /**
     * 初结束数据
     */
    private Integer earlyEnd;
    /**
     * 中开始数据
     */
    private Integer middleStart;
    /**
     * 中结束数据
     */
    private Integer middleEnd;
    /**
     * 高开始数据
     */
    private Integer highStart;
    /**
     * 高结束数据
     */
    private Integer highEnd;
    /**
     * 专家开始数据
     */
    private Integer expertStart;
    /**
     * 专家结束数据
     */
    private Integer expertEnd;


    private List<GamePO> games;

    private Integer sort;

}
