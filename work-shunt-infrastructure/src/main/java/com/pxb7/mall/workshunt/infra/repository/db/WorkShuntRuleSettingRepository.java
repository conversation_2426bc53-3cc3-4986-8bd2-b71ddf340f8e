package com.pxb7.mall.workshunt.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSetting;

import java.util.List;

/**
 * 分流规则设置参数(WorkShuntRuleSetting)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-05 15:05:59
 */
public interface WorkShuntRuleSettingRepository extends IService<WorkShuntRuleSetting> {

    boolean deleteBySkillId(String skillId);

    WorkShuntRuleSetting queryBy(String skillId, String shuntRuleType);

    List<WorkShuntRuleSetting> queryBy(List<String> skillIds, String shuntRuleType);

    List<WorkShuntRuleSetting> queryBy(List<String> skillIds);
}

