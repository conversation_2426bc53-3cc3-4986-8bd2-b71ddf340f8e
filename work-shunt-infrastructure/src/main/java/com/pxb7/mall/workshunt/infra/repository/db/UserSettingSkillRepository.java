package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillBoard;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillPagePO;

import java.util.List;
import java.util.Map;

/**
 * 工作用户的技能(WorkUserSettingSkill)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-03 16:30:46
 */
public interface UserSettingSkillRepository extends IService<WorkUserSettingSkill> {

    List<WorkUserSettingSkill> queryAllByWorkUserId(String userId);

    List<WorkUserSettingSkill> queryBySkillId(String skillId);

    WorkUserSettingSkill queryBySkillIdAndWorkUserId(String skillId, String workUserId);

    List<WorkUserSettingSkill> queryBySkillIds(List<String> skillIds, boolean enable);

    List<WorkUserSettingSkill> queryBySettingSkillId(List<String> settingSkillIds);

    List<WorkUserSettingSkill> queryByWorkUserIds(List<String> workUserIds);

    WorkUserSettingSkill queryByWorkUserIdAndSkillId(String workUserId, String skillId);

    Page<WorkUserSettingSkillBoard> queryWorkUserSettingPage(WorkUserSettingSkillPagePO po);

    Page<WorkUserSettingSkillBoard> queryWorkUserSettingByGamePage(WorkUserSettingSkillPagePO po);

    Boolean removeUserSettingByWorkUserId(String workUserId);

    List<WorkUserSettingSkillGameList> queryBySKillIdsAndGameId(List<String> skillId, String gameId);

    Map<String, Long> totalWorkUserRelateSkillCount(List<String> workUserIds);

    List<WorkUserSettingSkill> queryAllStatusByWorkUserIds(List<String> workUserIds);

    List<WorkUserSettingSkill> queryByWorkUserIdsAndSkillId(List<String> workUserIds, String skillIds);

    void updateWorkUserRoomMaxLimit(String userSettingSkillId, Long maxLimit);

    void updateWorkUserSessionMaxLimit(String userSettingSkillId, Long maxLimit);

}

