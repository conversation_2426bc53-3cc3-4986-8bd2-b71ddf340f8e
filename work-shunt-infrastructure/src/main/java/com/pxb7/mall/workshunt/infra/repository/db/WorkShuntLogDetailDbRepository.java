package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogDetail;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 请求日志记录(WorkShuntLogDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-07 14:48:00
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkShuntLogDetailDbRepository extends ServiceImpl<WorkShuntLogDetailMapper, WorkShuntLogDetail>
    implements WorkShuntLogDetailRepository {

}
