package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSettingGameList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 技能模版关联游戏(WorkSkillTemplateSettingGameList)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 09:57:53
 */
@Mapper
public interface WorkSkillTemplateSettingGameListMapper extends BaseMapper<WorkSkillTemplateSettingGameList> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkSkillTemplateSettingGameList> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkSkillTemplateSettingGameList> entities);

}

