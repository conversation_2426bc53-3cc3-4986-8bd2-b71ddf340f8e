package com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.user;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.user.admin.api.SysRoleServiceI;
import com.pxb7.mall.user.admin.api.UserDeptServiceI;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetUserRoleInfoRespDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.ListSysDeptDubboRespDTO;
import com.pxb7.mall.user.api.*;
import com.pxb7.mall.user.dto.request.setting.MsgSettingRpcRespDTO;
import com.pxb7.mall.user.dto.request.sys.SysUserReqDTO;
import com.pxb7.mall.user.dto.request.user.UserExtendOfficialDTO;
import com.pxb7.mall.user.dto.response.sys.SysUserRespDTO;
import com.pxb7.mall.user.dto.response.user.UserExistRespDTO;
import com.pxb7.mall.user.dto.response.user.UserLoginIPLogRpcRespDTO;
import com.pxb7.mall.user.dto.response.user.UserRiskControlDTO;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import io.vavr.control.Option;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.pxb7.mall.workshunt.infra.enums.ErrorCode.DUBBO_USER_ERROR;


@Service
@Slf4j
public class UserCenterGateway {

    @DubboReference
    private UserDeptServiceI userDeptServiceI;

    @DubboReference
    private SysUserServiceI sysUserServiceI;

    @DubboReference
    private com.pxb7.mall.user.admin.api.SysUserServiceI adminUserService;

    @DubboReference
    private UserServiceI userServiceI;

    @DubboReference
    private MsgSettingServiceI msgSettingServiceI;

    @DubboReference
    private UserLogStubServiceI userLogStubService;

    @DubboReference
    private SysRoleServiceI sysRoleServiceI;

    @DubboReference
    private UserExtendInfoServiceI userExtendInfoServiceI;


    //官方客服
    public static final String PRIVATE_CUSTOMER_CARE_CODE = "gfkf";

    //号商客服
    public static final String MERCHANT_CUSTOMER_CARE_CODE = "hskf";


    /**
     * 查询用户近3次登录ip
     *
     * @param userIds 用户id集合
     */
    public List<UserLoginIPLogRpcRespDTO> selectIPListByUserIds(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BizException("查询用户近3次登录ip，入参为空");
        }

        PxResponse<List<UserLoginIPLogRpcRespDTO>> response = null;
        try {
            response = userLogStubService.selectIPListByUserIds(userIds, 3, true);
            log.info("查询用户近3次登录ip, userIds={}, response={}", userIds, response);
        } catch (Exception e) {
            log.error("查询用户近3次登录ip的dubbo调用异常! userIds={}, response={}", userIds, response, e);
            throw new BizException("查询用户近3次登录ip的dubbo调用异常");
        }
        if (response == null) {
            log.error("查询用户近3次登录ip的响应对象为空! userIds={}, response={}", userIds, response);
            throw new BizException("查询用户近3次登录ip的响应对象为空！");
        }
        if (!response.isSuccess()) {
            log.error("查询用户近3次登录ip的响应异常! userIds={}, response={}", userIds, response);
            throw new BizException("查询用户近3次登录ip的响应异常！" + response.getErrMessage());
        }
        return response.getData();
    }

    public MsgSettingRpcRespDTO getUserMsgSettingByPhone(String phoneNumber) {
        try {
            SingleResponse<MsgSettingRpcRespDTO> resp = msgSettingServiceI.getMsgSettingByTelphone(phoneNumber);
            if (resp.isSuccess()) {
                return resp.getData();
            }
            log.info("Failed to get user:{} message setting failure", phoneNumber);
            return null;
        } catch (Exception e) {
            log.error("Failed to get user:{} message setting error", phoneNumber, e);
            return null;
        }

    }

    public List<UserShortInfoDTO> getUserListByTelPhoneList(String phoneNumber) {
        List<String> phoneNumbers = new ArrayList<>();
        phoneNumbers.add(phoneNumber);
        MultiResponse<UserShortInfoDTO> resp = userServiceI.getUserInfoListByTelephoneList(phoneNumbers, null);
        if (resp.isSuccess()) {
            return resp.getData();
        }

        log.error(
                "Failed to get user info  from the userDeptServiceI.getUserListByTelPhoneList,errorCode:{},errorMsg:{}",
                resp.getErrCode(), resp.getErrMessage());
        return new ArrayList<>();
    }

    public Map<String, ListSysDeptDubboRespDTO> getDeptInfo(Collection<String> deptIds) {

        MultiResponse<ListSysDeptDubboRespDTO> resp = userDeptServiceI.queryDeptListByIds(new ArrayList<>(deptIds));
        if (resp.isSuccess()) {
            return resp.getData().stream()
                    .collect(Collectors.toMap(ListSysDeptDubboRespDTO::getDeptId, Function.identity()));

        }
        log.error("Failed to get dept info  from the userDeptServiceI.queryDeptListByIds,errorCode:{},errorMsg:{}",
                resp.getErrCode(), resp.getErrMessage());
        return new HashedMap<>();
    }

    public Map<String, String> getAdminUserName(Collection<String> sysUserIds) {
        if (CollectionUtils.isEmpty(sysUserIds)) {
            return new HashMap<>();
        }
        SingleResponse<Map<String, String>> resp = adminUserService.getAdminNameMapByIds(new ArrayList<>(sysUserIds));
        if (resp.isSuccess()) {
            return resp.getData();
        }
        log.error(
                "Failed to retrieve admin name from the sysUserServiceI.getAdminNameMapByIds,errorCode:{},errorMsg:{}",
                resp.getErrCode(), resp.getErrMessage());
        return new HashedMap<>();
    }

    public Map<String, SysUserRespDTO> getAdminInfo(Collection<String> sysUserIds) {
        SysUserReqDTO sysUserReqDTO = new SysUserReqDTO();
        sysUserReqDTO.setUserIds(new ArrayList<>(sysUserIds));
        MultiResponse<SysUserRespDTO> resp = sysUserServiceI.listByCondition(sysUserReqDTO);

        if (resp.isSuccess()) {
            List<SysUserRespDTO> data = resp.getData();
            return data.stream().collect(Collectors.toMap(SysUserRespDTO::getUserId, Function.identity()));

        }
        log.error("Failed to retrieve admin name from the sysUserServiceI.listByCondition,errorCode:{},errorMsg:{}",
                resp.getErrCode(), resp.getErrMessage());
        return new HashedMap<>();
    }

    /**
     * user-c 端dubbo
     *
     * @param userIds
     * @return
     */
    public Map<String, UserShortInfoDTO> getUserInfo(Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashedMap<>();
        }
        MultiResponse<UserShortInfoDTO> userShortInfoResp = userServiceI.getUserShortInfo(new ArrayList<>(userIds));
        if (userShortInfoResp.isSuccess()) {
            return userShortInfoResp.getData().stream()
                    .collect(Collectors.toMap(UserShortInfoDTO::getUserId, Function.identity()));

        }
        log.error("Failed to retrieve user information from the userServiceI.getUserShortInfo,errorCode:{},errorMsg:{}",
                userShortInfoResp.getErrCode(), userShortInfoResp.getErrMessage());
        return new HashedMap<>();
    }

    public UserShortInfoDTO getUserInfo(String userId) {
        List<String> userIds = new ArrayList<>();
        userIds.add(userId);
        Map<String, UserShortInfoDTO> userInfoMap = getUserInfo(userIds);
        if (MapUtils.isEmpty(userInfoMap)) {
            return null;
        }
        return userInfoMap.get(userId);
    }

    /**
     * 获取部门
     *
     * @return
     */
    public List<ListSysDeptDubboRespDTO> listSysDept() {
        return Try.of(() -> {
            MultiResponse<ListSysDeptDubboRespDTO> response = userDeptServiceI.listSysDept();
            return Option.when(response.isSuccess() && response.isNotEmpty(), response::getData)
                    .getOrElse(ArrayList::new);
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用部门的dubbo异常{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }

    /**
     * 获取人员
     *
     * @param dto
     * @return
     */
    public List<GetUserRoleInfoRespDTO> listUserInfo(Supplier<GetUserInfoReqDTO> dto) {
        return Try.of(() -> {
            MultiResponse<GetUserRoleInfoRespDTO> result = adminUserService.getUserRoleInfoByCondition(dto.get());
            return Option.when(result.isSuccess() && result.isNotEmpty(), result::getData).getOrElse(ArrayList::new);
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用用户的dubbo异常{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }


    /**
     * 获取用户状态
     *
     * @return
     */
    public String getUserAccountState(String account) {
        return Try.of(() -> {
            SingleResponse<UserExistRespDTO> singleResponse = userServiceI.checkPhoneExistAndStatus(account);
            if (singleResponse.isSuccess()) {
                UserExistRespDTO data = singleResponse.getData();
                if (!data.getExistFlag()) {
                    return "1"; // 未注册
                }
                // 用户状态 日-正常 ,1-锁定，2-注销
                if (!"0".equals(String.valueOf(data.getUserStatus()))) {
                    return "2"; // 统一返回禁用
                }
            }
            return "0"; // 正常
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用部门的dubbo异常{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }

    /**
     * 获取用户状态
     *
     * @return
     */
    public boolean existRegAccount(String account) {
        return Try.of(() -> {
            SingleResponse<UserExistRespDTO> singleResponse = userServiceI.checkPhoneExistAndStatus(account);
            if (singleResponse.isSuccess()) {
                UserExistRespDTO data = singleResponse.getData();
                if (!data.getExistFlag()) {
                    return false;
                }
                // 用户状态 日-正常 ,1-锁定，2-注销
                if (!"0".equals(String.valueOf(data.getUserStatus()))) {
                    return true;
                }
            }
            return true; // 正常
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用部门的dubbo异常{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }

    public String getUserInfoPhone(String loginUserId) {
        return Try.of(() -> {
            MultiResponse<UserShortInfoDTO> response = userServiceI.getUserShortInfo(Arrays.asList(loginUserId));
            if (response.isSuccess() && response.getData() != null) {
                UserShortInfoDTO userShortInfoDTO = response.getData().get(0);
                return userShortInfoDTO.getPhone();
            }
            return null;
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用部门的dubbo异常{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }

    public UserRiskControlDTO getRiskControlDTO(String loginUserId) {
        return Try.of(() -> {
            SingleResponse<UserRiskControlDTO> response = userServiceI.getRiskControlUserInfoByUserId(loginUserId);
            if (response.isSuccess() && response.getData() != null) {
                UserRiskControlDTO riskControlDTO = response.getData();
                return riskControlDTO;
            }
            return null;
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:获取用户基础信息失败{}", e.getMessage());
            return new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc());
        });
    }


    public boolean isCustomer(String userId) {
        return Try.of(() -> {
            SingleResponse<Boolean> response = sysUserServiceI.isValidByCustomerCareId(userId);
            if (response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
            return false;
        }).getOrElseThrow(() -> new BizException(DUBBO_USER_ERROR.getErrCode(), DUBBO_USER_ERROR.getErrDesc()));
    }

    /**
     * 用户关注公众号
     * @param unionId 用户unionId
     * @param openId 用户openId
     * @param type 1:关注，0：取消关注
     */
    public void wechatSubscribe(String unionId, String openId, Integer type) {
        log.info("微信公众号关注事件调用用户域dubbo接口 unionId:{} openId:{}, type:{}", unionId, openId, type);
        UserExtendOfficialDTO userExtendOfficialDTO = new UserExtendOfficialDTO();
        userExtendOfficialDTO.setType(type);
        userExtendOfficialDTO.setUnionId(unionId);
        userExtendOfficialDTO.setOpenId(openId);
        userExtendInfoServiceI.bindOfficial(userExtendOfficialDTO);
    }

}

