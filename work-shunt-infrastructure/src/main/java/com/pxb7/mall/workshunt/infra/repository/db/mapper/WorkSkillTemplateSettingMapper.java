package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 技能模版配置(WorkSkillTemplateSetting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 09:57:08
 */
@Mapper
public interface WorkSkillTemplateSettingMapper extends BaseMapper<WorkSkillTemplateSetting> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkSkillTemplateSetting> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkSkillTemplateSetting> entities);

}

