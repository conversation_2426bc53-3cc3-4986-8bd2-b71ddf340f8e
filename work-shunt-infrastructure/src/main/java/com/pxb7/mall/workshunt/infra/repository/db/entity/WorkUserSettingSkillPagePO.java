package com.pxb7.mall.workshunt.infra.repository.db.entity;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NotNull
@AllArgsConstructor
public class WorkUserSettingSkillPagePO implements Serializable {
    @NotNull(message = "分页参数不能为空")
    private Integer pageIndex;
    @NotNull(message = "分页参数不能为空")
    private Integer pageSize;
    private String deptId;
    private List<String> userIds;
    private String skillId;
    //0 不在线  1  在线
    private Integer imStatus;

    private String gameId;
}
