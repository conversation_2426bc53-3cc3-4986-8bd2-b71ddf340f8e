package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分流规则设置参数业务(WorkShuntRuleSettingCondition)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 15:06:24
 */
@Mapper
public interface WorkShuntRuleSettingConditionMapper extends BaseMapper<WorkShuntRuleSettingCondition> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkShuntRuleSettingCondition> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkShuntRuleSettingCondition> entities);

}

