package com.pxb7.mall.workshunt.infra.util;

import com.alibaba.cola.exception.SysException;
import com.pxb7.mall.components.idgen.IdGen;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;

import java.util.Objects;

public class IdGenUtil {
    private IdGenUtil() {}

    private static IdGen idGen = SpringUtils.getBean(IdGen.class);

    public static String getId() {
        if (Objects.isNull(idGen)) {
            throw new SysException(ErrorCode.SYSTEM_ERROR.getErrCode(), "IdGen is null");
        }
        return String.valueOf(idGen.nextId());
    }

}
