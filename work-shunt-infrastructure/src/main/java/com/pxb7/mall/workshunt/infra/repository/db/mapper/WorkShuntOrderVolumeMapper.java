package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntOrderVolume;
import org.apache.ibatis.annotations.Mapper;

/**
 * (com.pxb7.mall.common.infra.repository.db.mysql.entity.WorkShuntOrderVolume)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-10 17:23:09
 */
@Mapper
public interface WorkShuntOrderVolumeMapper extends BaseMapper<WorkShuntOrderVolume> {

    /**
     * 清除所有接单量
     */
    @InterceptorIgnore(blockAttack = "true")
    void clearOrderVolume();

}

