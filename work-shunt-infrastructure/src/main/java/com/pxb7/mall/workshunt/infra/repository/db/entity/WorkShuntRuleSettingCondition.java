package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分流规则设置参数业务(WorkShuntRuleSettingCondition)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 17:50:46
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_rule_setting_condition")
public class WorkShuntRuleSettingCondition implements Serializable {
    private static final long serialVersionUID = -34979523399042599L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 条件ID
     */
    @TableField(value = "setting_condition_id")
    private String settingConditionId;
    /**
     * 分流规则ID
     */
    @TableField(value = "shunt_rule_id")
    private String shuntRuleId;
    /**
     * 条件类型1当前时间排班内2未超接待上限制3IM在线
     */
    @TableField(value = "condition_type")
    private String conditionType;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 分流规则类型1非溢出状态2溢出状态
     */
    @TableField(value = "shunt_rule_type")
    private String shuntRuleType;

    @TableField(value = "skill_id")
    private String skillId;

}

