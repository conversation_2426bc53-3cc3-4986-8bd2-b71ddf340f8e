package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.workshunt.infra.model.workShuntLog.WorkShuntLogPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 请求日志记录(WorkShuntLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 20:26:59
 */
@Mapper
public interface WorkShuntLogMapper extends BaseMapper<WorkShuntLog> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkShuntLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkShuntLog> entities);


    Page<WorkShuntLog> pages(@Param("page") Page<WorkShuntLog> page, @Param("po") WorkShuntLogPageListPO po);

}

