package com.pxb7.mall.workshunt.infra.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.Properties;

/**
 * 服务切换开关,true为切换到新服务
 * <AUTHOR>
 */
@Getter
@Configuration
@Slf4j
public class WorkShuntServiceSwitchConfig extends AbstractAppConfig {

    private static final String SWITCH_NEW = "shunt.service.switch.new";

    @Value("${shunt.service.switch.new}")
    private String switchNew;

    public static final String DEFAULT_FALSE = "false";
    @Override
    protected void loadProperties(Properties properties) {
        if (Objects.isNull(properties)) {
            log.warn("拉取的配置为空");
            return;
        }

        String switchNew = properties.getProperty(SWITCH_NEW);
        if (StringUtils.isBlank(switchNew)) {
            log.warn("nacos的{}配置变更为空字符串，不更新", switchNew);
            return;
        }
        this.switchNew = switchNew;
        log.info("nacos动态更新服务切换开关，switch new:{}", switchNew);
    }
}
