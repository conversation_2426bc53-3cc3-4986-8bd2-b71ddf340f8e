package com.pxb7.mall.workshunt.infra.repository.gateway.dubbo.product;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.product.client.api.GameServiceI;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.workshunt.infra.enums.ErrorCode;
import io.vavr.CheckedFunction0;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductCenterGateway {

    @DubboReference
    private GameServiceI gameServiceI;

    public String getGameName(String gameId) {
        return Try.of(() -> {
            MultiResponse<GameBaseDTO> response = gameServiceI.selectGameBaseList(List.of(gameId));
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                List<GameBaseDTO> data = response.getData();
                if (data == null || data.isEmpty()) {
                    return null;
                }
                return data.get(0).getGameName();
            }
            return null;
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用商品服务查询游戏信息异常，请求参数gameId:{}", gameId, e);
            return new BizException(ErrorCode.DUBBO_PRODUCT_ERROR.getErrCode(), ErrorCode.DUBBO_PRODUCT_ERROR.getErrDesc());
        });
    }

    public Map<String, GameBaseDTO> getGameMap(List<String> gameIdList) {
        return Try.of(() -> {
            if (Objects.isNull(gameIdList) || gameIdList.isEmpty()) {
                return null;
            }
            MultiResponse<GameBaseDTO> response = gameServiceI.selectGameBaseList(gameIdList);
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                List<GameBaseDTO> data = response.getData();
                if (Objects.isNull(data) || data.isEmpty()) {
                    return null;
                }
                return data.stream().collect(Collectors.toMap(GameBaseDTO::getGameId, obj -> obj));
            }
            return null;
        }).getOrElseThrow(e -> {
            log.error("[Dubbo]:调用商品服务批量查询游戏信息异常，请求参数gameIdList:{}", gameIdList, e);
            return new BizException(ErrorCode.DUBBO_PRODUCT_ERROR.getErrCode(), ErrorCode.DUBBO_PRODUCT_ERROR.getErrDesc());
        });
    }

    /**
     * 获取所有游戏
     * @return
     */
    public Try<List<GameBaseDTO>> queryAllGame() {
        CheckedFunction0<List<GameBaseDTO>> queryAllGame = () -> {
            MultiResponse<GameBaseDTO> response = gameServiceI.selectGameAll();
            if (!response.isSuccess() || response.isEmpty()) {
                return new ArrayList<>();
            }
            return response.getData();
        };

        return Try.of(queryAllGame);
    }
}

