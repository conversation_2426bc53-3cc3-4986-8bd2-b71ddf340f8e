package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技能类型(WorkSkillType)实体类
 *
 * <AUTHOR>
 * @since 2024-08-02 16:32:55
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_skill_type")
public class WorkSkillType implements Serializable {
    private static final long serialVersionUID = -57497793083798768L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 技能ID
     */
    @TableField(value = "skill_type_id")
    private String skillTypeId;
    /**
     * 技能名称
     */
    @TableField(value = "skill_type_name")
    private String skillTypeName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Short sort;

}

