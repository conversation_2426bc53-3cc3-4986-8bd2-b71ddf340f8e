package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogSkillCodes;

import java.util.List;

/**
 * 请求日志记录(WorkShuntLogSkillCodes)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-07 14:54:33
 */
public interface WorkShuntLogSkillCodesRepository extends IService<WorkShuntLogSkillCodes> {

    List<WorkShuntLogSkillCodes> getListWorkShuntLogSkillCodesByShuntLogId(String shuntLogId);

    List<WorkShuntLogSkillCodes> getListWorkShuntLogSkillCodesByShuntLogIds(List<String> shuntLogIds);

    /**
     * 物理删除
     * @param date
     */
    void deleteByTime(String date);
}

