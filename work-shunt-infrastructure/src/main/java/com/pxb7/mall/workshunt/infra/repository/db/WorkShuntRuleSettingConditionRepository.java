package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition;

import java.util.List;

/**
 * 分流规则设置参数业务(WorkShuntRuleSettingCondition)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-05 15:06:25
 */
public interface WorkShuntRuleSettingConditionRepository extends IService<WorkShuntRuleSettingCondition> {

    List<WorkShuntRuleSettingCondition> queryBy(String skillId, String shuntRuleType);


    List<WorkShuntRuleSettingCondition> queryBy(List<String> skillIds, String shuntRuleType);

    boolean deleteBy(String skillId);


}

