package com.pxb7.mall.workshunt.infra.repository.gateway.sdk;

import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.exceptions.ClientException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.pxb7.mall.workshunt.infra.config.AliYunOssProperties;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Component
@Slf4j
public class OssGateway {
    /**
     * oss access key
     */
    private static final String OSS_ACCESS_KEY_ID = "OSS_ACCESS_KEY_ID";

    /**
     * oss access secret
     */
    private static final String OSS_ACCESS_KEY_SECRET = "OSS_ACCESS_KEY_SECRET";

    private static final String OSS_BUCKET_KEY = "bucket";

    private static final String OSS_SIGNATURE_VERSION_KEY = "x-oss-signature-version";
    private static final String OSS_SIGNATURE_VERSION_VALUE = "OSS4-HMAC-SHA256";
    private static final String OSS_CREDENTIAL_KEY = "x-oss-credential";
    private static final String OSS_SECURITY_TOKEN_KEY = "x-oss-security-token";
    private static final String OSS_DATE_KEY = "x-oss-date";

    // 上传文件是否可覆盖配置
    private static final String OSS_FORBID_OVERWRITE = "x-oss-forbid-overwrite";

    // ISO 8601日期和时间标准，例 20231203T121212Z
    private final DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");

    //sts临时凭证过期时间减去秒数，为了给前端预留请求的时间，并且要保证OSS policy 签名有效时间(OSS_POST_V4_SIGNATURE_DURATION_SECONDS)内token不会过期
    private static final long STS_TOKEN_EXPIRATION_TIME_MINUS_SECONDS = 122;

    /**
     * sts临时凭证有效期，单位秒, The Min/Max value of DurationSeconds is 15min/1hr.
     */
    private static final long STS_DURATION_SECONDS = 3600;

    /**
     * OSS POST V4签名有效期，单位秒
     */
    private static final long OSS_POST_V4_SIGNATURE_DURATION_SECONDS = 120;

    /**
     * 阿里云机房地域id
     */
    private static final String REGION_ID = "cn-hangzhou";

    /**
     * url连接超时时间，单位毫秒
     */
    private static final int URL_CONNECT_TIMEOUT_MILLISECONDS = 1000;

    /**
     * url读取超时时间，单位毫秒
     */
    private static final int URL_READ_TIMEOUT_MILLISECONDS = 3000;

    @Resource
    private AliYunOssProperties ossProperties;

    /**
     * oss客户端实例
     */
    private static final AtomicReference<OSS> OSS_CLIENT_INSTANCE = new AtomicReference<>();

    /**
     * @param file     上传文件
     * @param fileName 文件路径名称
     */
    public String uploadFile(MultipartFile file, String fileName) {
        try {
            InputStream inputStream = file.getInputStream();
            return uploadFile(inputStream, fileName);
        } catch (IOException e) {
            log.error("An IO exception occurred during file upload");
        }
        return null;

    }

    public String uploadFile(InputStream inputStream, String fileName) {
        try {
            PutObjectRequest putObjectRequest =
                    new PutObjectRequest(ossProperties.getBucketName(), fileName, inputStream);
            PutObjectResult result = getOssClientInstance().putObject(putObjectRequest);
            log.info("upload success result:{}", JSONUtil.toJsonStr(result));
            return ossProperties.getCdnDomain() + "/" + fileName;
        } catch (ClientException e) {
            log.error("Failed to upload file, client encountered an exception", e);
        }
        return null;

    }

    public String uploadFile(String url, String fileName) throws IOException {
        InputStream inputStream = null;
        try {
            Instant now = Instant.now();
            inputStream = downloadInputStream(url);
            log.info("url下载图片链接，cost {} ms", Duration.between(now, Instant.now()).toMillis());
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), fileName, inputStream);
            // 创建PutObject请求
            getOssClientInstance().putObject(putObjectRequest);

            String fullPath = ossProperties.getCdnDomain() + StringPool.SLASH + fileName;
            log.info("upload success fullPath:{}", fullPath);
            return fullPath;
        } catch (Exception e) {
            log.warn("url转链失败, url={}, err={}", url, e.getMessage(), e);
            return null;
        } finally {
            if (Objects.nonNull(inputStream)) {
                inputStream.close();
            }
        }
    }

    /**
     * 根据上传时创建的文件名称下载
     *
     * @param fileName 文件名称
     * @return 输入流, 注意使用完后关闭输入流
     */
    public InputStream downloadFileStream(String fileName) {
        try {
            OSS ossClient = getOssClientInstance();
            // 生成下载 URL
            GeneratePresignedUrlRequest request =
                    new GeneratePresignedUrlRequest(ossProperties.getBucketName(), fileName);
            // 设置 URL 的有效期为 1 小时
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
            request.setExpiration(expiration);
            URL url = ossClient.generatePresignedUrl(request);
            // 使用签名URL发送请求。
            OSSObject ossObject = ossClient.getObject(url, new HashMap<>());
            if (ossObject != null) {
                return ossObject.getObjectContent();
            }
        } catch (ClientException e) {
            log.error("从OSS下载文件失败，", e);
        }
        return null;
    }

    public List<String> batchUploadFile(List<String> urls, List<String> targetPaths) throws ClientException {
        int size = Math.min(urls.size(), targetPaths.size());
        List<String> list = new ArrayList<>();
        String bucketName = ossProperties.getBucketName();
        // 获取OSSClient单例
        OSS ossClient = getOssClientInstance();
        for (int i = 0; i < size; i++) {
            String url = urls.get(i);
            String targetPath = targetPaths.get(i);
            try {
                InputStream inputStream = new URL(url).openStream();
                // 创建PutObjectRequest对象。
                PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, targetPath, inputStream);
                // 创建PutObject请求。
                ossClient.putObject(putObjectRequest);
                list.add(ossProperties + "/" + targetPath);
                continue;
            } catch (MalformedURLException e) {
                log.error("Failed to upload  from URL this time,network url encountered an exception url:{}", url, e);
            } catch (Exception e) {
                log.error("Failed to upload  from URL this time,system encountered an exception", e);
            }
            list.add(null);

        }
        return list;
    }


    /**
     * 判断是否过期
     *
     * @param expiration 过期时间,@Nullable
     * @return true 未过期，如果过期时间为空或者异常默认返回false
     */
    private boolean nonExpired(@Nullable String expiration) {
        // 为空返回false
        if (StringUtils.isBlank(expiration)) {
            return false;
        }
        try {
            Instant instant = Instant.parse(expiration);
            LocalDateTime expirationTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            // 过期时间减去修正时间
            return LocalDateTime.now().isBefore(expirationTime.minusSeconds(STS_TOKEN_EXPIRATION_TIME_MINUS_SECONDS));
        } catch (Exception e) {
            log.error("过期时间转换出错,expiration={}", expiration, e);
        }
        return false;
    }

    private @NotNull InputStream downloadInputStream(String url) throws IOException {
        URL imageUrl = new URL(url);
        // 打开连接
        URLConnection connection = imageUrl.openConnection();
        connection.setConnectTimeout(URL_CONNECT_TIMEOUT_MILLISECONDS);
        connection.setReadTimeout(URL_READ_TIMEOUT_MILLISECONDS);
        try {
            return connection.getInputStream();
        } catch (Exception e) {
            log.warn("根据链接下载图片出错,url={}, err={}", url, e.getMessage());
            throw e;
        }
    }

    private OSS getOssClientInstance() throws ClientException {
        OSS instance = OSS_CLIENT_INSTANCE.get();
        if (Objects.nonNull(instance)) {
            return instance;
        }
        try {
            // 构建oss客户端
            EnvironmentVariableCredentialsProvider credentialsProvider =
                    CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
            instance = new OSSClientBuilder().build(ossProperties.getEndpoint(), credentialsProvider);
            if (OSS_CLIENT_INSTANCE.compareAndSet(null, instance)) {
                // 如果设置成功，返回新创建的实例
                return instance;
            } else {
                // 如果设置失败，说明其他线程已经创建了实例，返回当前实例
                return OSS_CLIENT_INSTANCE.get();
            }
        } catch (Exception e) {
            log.error("构建oss client出现问题,error:{}", e.getMessage());
            throw e;
        }
    }
}
