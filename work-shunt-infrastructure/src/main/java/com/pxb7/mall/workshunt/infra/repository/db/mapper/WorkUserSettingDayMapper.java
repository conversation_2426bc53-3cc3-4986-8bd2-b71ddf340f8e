package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作用户设置时间(WorkUserSettingDay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-02 16:34:19
 */
@Mapper
public interface WorkUserSettingDayMapper extends BaseMapper<WorkUserSettingDay> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkUserSettingDay> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkUserSettingDay> entities);

}

