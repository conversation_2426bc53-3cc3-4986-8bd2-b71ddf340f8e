package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作用户关联游戏(WorkUserSettingSkillGameList)实体类
 *
 * <AUTHOR>
 * @since 2024-08-03 18:04:32
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
@TableName(value = "work_user_setting_skill_game_list")
public class WorkUserSettingSkillGameList implements Serializable {
    private static final long serialVersionUID = -67327540558165621L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 技能ID
     */
    @TableField(value = "settting_skill_id")
    private String setttingSkillId;
    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 游戏名称
     */
    @TableField(value = "game_name")
    private String gameName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

