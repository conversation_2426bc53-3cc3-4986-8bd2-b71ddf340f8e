package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 技能模版配置(WorkSkillTemplateSetting)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 10:45:23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_skill_template_setting")
public class WorkSkillTemplateSetting implements Serializable {
    private static final long serialVersionUID = 697571440319939152L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务ID
     */
    @TableField(value = "template_setting_id")
    private String templateSettingId;
    /**
     * 业务模板ID
     */
    @TableField(value = "skill_template_id")
    private String skillTemplateId;
    /**
     * 技能ID
     */
    @TableField(value = "work_skill_id")
    private String workSkillId;
    /**
     * 设置接待上线
     */
    @TableField(value = "reception_max_limit")
    private Long maxLimit;

    /**
     * 设置会话并发接待上线
     */
    @TableField(value = "reception_max_session_limit")
    private Long maxSessionLimit;


    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "is_able")
    private boolean able;

}

