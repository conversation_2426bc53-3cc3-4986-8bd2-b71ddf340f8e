package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分流规则(WorkShuntRule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-05 15:05:36
 */
@Mapper
public interface WorkShuntRuleMapper extends BaseMapper<WorkShuntRule> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkShuntRule> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkShuntRule> entities);

}

