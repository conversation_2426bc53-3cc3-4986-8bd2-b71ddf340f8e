package com.pxb7.mall.workshunt.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 技能类型(WorkSkillType)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-02 16:32:55
 */
@Mapper
public interface WorkSkillTypeMapper extends BaseMapper<WorkSkillType> {
    /**
     * 批量新增数据
     *
     * @param entities List<WorkSkillType> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<WorkSkillType> entities);

}

