package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDayRelationWorkTemplat;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingDayRelationWorkTemplatMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 工作用户设置时间模版(WorkUserSettingDayRelationWorkTemplat)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02 20:50:07
 */
@Slf4j
@Repository
@DS("mysql")
public class WorkUserSettingDayRelationWorkTemplatDbRepository
    extends ServiceImpl<WorkUserSettingDayRelationWorkTemplatMapper, WorkUserSettingDayRelationWorkTemplat>
    implements WorkUserSettingDayRelationWorkTemplatRepository {

    @Override
    public boolean deleteByTimeTemplate(String settingId) {
        LambdaQueryWrapper<WorkUserSettingDayRelationWorkTemplat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkUserSettingDayRelationWorkTemplat::getSettingDayId, settingId);
        return this.remove(queryWrapper);
    }

    @Override
    public List<WorkUserSettingDayRelationWorkTemplat> queryBy(List<String> settingIds) {
        LambdaQueryWrapper<WorkUserSettingDayRelationWorkTemplat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkUserSettingDayRelationWorkTemplat::getSettingDayId, settingIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<WorkUserSettingDayRelationWorkTemplat> queryBy(String templateId) {
        LambdaQueryWrapper<WorkUserSettingDayRelationWorkTemplat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkUserSettingDayRelationWorkTemplat::getWorkTemplateId, templateId);
        return this.list(queryWrapper);
    }

    @Override
    public List<WorkUserSettingDayRelationWorkTemplat> listWorkUserSettingDayRelationWorkTemplatBySettingDdyId(
        String settingDayId) {
        LambdaQueryWrapper<WorkUserSettingDayRelationWorkTemplat> lambdaQueryWrapper = new LambdaQueryWrapper<WorkUserSettingDayRelationWorkTemplat>();
        lambdaQueryWrapper.eq(WorkUserSettingDayRelationWorkTemplat::getSettingDayId,settingDayId);
        return list(lambdaQueryWrapper);
    }
}
