package com.pxb7.mall.workshunt.infra.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO;
import com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO;
import com.pxb7.mall.workshunt.infra.model.skill.WorkSkillPageListPo;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill;

import java.util.List;

/**
 * 技能管理 (WorkSkill) 表服务接口
 */
public interface WorkSkillRepository extends IService<WorkSkill> {

    /**
     * 技能管理 分页查询
     *
     * @param dto
     * @return
     */
    Page<SkillPageListPO> listPage(WorkSkillPageListPo dto);

    List<UserSkillAllPO> all();

    /**
     * 技能管理 获取一个详情
     *
     * @param key
     * @return
     */
    WorkSkill getTableOneDataByParamKey(String key);

    WorkSkill getTableOneDataByParamKeyCode(String keyCode);

    List<WorkSkill> queryBySkillCode(List<String> cdoe);

    List<WorkSkill> getWorkSkillAll();

    boolean isExitsSkillStoreInSkillType(String skillId, String skillTypeId, Integer skillSort);

    boolean isExitsSkillStoreInSkillType(String skillTypeId, Integer skillSort);
}

