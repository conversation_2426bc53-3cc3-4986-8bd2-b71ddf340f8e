package com.pxb7.mall.workshunt.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分流规则(WorkShuntRule)实体类
 *
 * <AUTHOR>
 * @since 2024-08-05 17:50:21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "work_shunt_rule")
public class WorkShuntRule implements Serializable {
    private static final long serialVersionUID = -20970259893834804L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 分流规则ID
     */
    @TableField(value = "shunt_rule_id")
    private String shuntRuleId;
    /**
     * 技能ID
     */
    @TableField(value = "skill_id")
    private String skillId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

}

