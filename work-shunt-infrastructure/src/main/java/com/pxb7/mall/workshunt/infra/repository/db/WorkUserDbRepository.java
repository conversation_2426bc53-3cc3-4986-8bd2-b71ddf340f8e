package com.pxb7.mall.workshunt.infra.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkSkillCodeGroupShuntPO;
import com.pxb7.mall.workshunt.infra.model.workUser.WorkUserPageListPO;
import com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser;
import com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserMapper;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* 排班用户 (WorkUser)表服务实现类
*
*/
@Slf4j
@Repository
@DS("mysql")
public class WorkUserDbRepository extends ServiceImpl<WorkUserMapper, WorkUser> implements WorkUserRepository {

    private static final String INNER_DEPT_TYPE = "1";

    private static final String MERCHANT_TYPE = "2";


    @Resource
    private WorkUserMapper workUserMapper;


    /**
    * 排班用户 分页查询
    * @return
    */
    @Override
    public Page<WorkUser> listPageWorkUser(WorkUserPageListPO po) {
         Page<WorkUser> page = new Page<WorkUser>(po.getPageIndex(),po.getPageSize());
         return workUserMapper.selectPage(page,
             new LambdaQueryWrapper<WorkUser>()
                .in(CollUtil.isNotEmpty(po.getUserIds()), WorkUser::getUserId, po.getUserIds())
                .orderByDesc(WorkUser::getCreateTime));
    }


    @Override
    public List<WorkUser> queryWorkUsers(List<String> userIds) {
        return workUserMapper.selectList(new LambdaQueryWrapper<WorkUser>().in(WorkUser::getUserId, userIds).eq(WorkUser::getIsActive, 1));
    }

    @Override
    public WorkUser queryAllStatusWorkUserByUserId(String userId) {
        return workUserMapper.selectOne(new LambdaQueryWrapper<WorkUser>()
            .eq(WorkUser::getUserId, userId).eq(WorkUser::getIsDeleted, 0));
    }

    @Override
    public WorkUser getTableOneDataByPkUserId(String pkUserId) {
        return workUserMapper.selectOne(new LambdaQueryWrapper<WorkUser>()
            .eq(WorkUser::getUserId, pkUserId).eq(WorkUser::getIsActive, 1).eq(WorkUser::getIsDeleted, 0));
    }


    @Override
    public List<WorkUser> query(List<String> workerIds, String merchantId) {
        LambdaQueryWrapper<WorkUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkUser::getWorkUserId, workerIds).eq(WorkUser::getIsActive, 1).eq(WorkUser::getIsDeleted, 0);
        if(CharSequenceUtil.isNotBlank(merchantId)){
            queryWrapper.eq(WorkUser::getMerchantId, merchantId);
        }else{
            queryWrapper.eq(WorkUser::getDeptType, INNER_DEPT_TYPE);
        }
        return workUserMapper.selectList(queryWrapper);
    }

    @Override
    public List<WorkUser> getWorkUserBySkillCode(String skillCode) {
        return workUserMapper.getWorkUserBySkillCode(skillCode);
    }

    @Override
    public List<WorkUser> getWorkUserBySkillCodeAndGameId(String skillCode, String gameId) {
        return workUserMapper.getWorkUserBySkillCodeAndGameId(skillCode, gameId);
    }

    @Override
    public List<WorkUser> workSkillCodeGroupShunt(WorkSkillCodeGroupShuntPO bo) {
        return workUserMapper.workSkillCodeGroupShunt(bo);
    }

    @Override
    public List<WorkUser> getWorkUserByMerchantId(String merchantId) {
        return this.list(new LambdaQueryWrapper<WorkUser>().eq(WorkUser::getMerchantId, merchantId));
    }

    @Override
    public void updateWorkUserActivity(String userId, String status) {
        LambdaUpdateWrapper<WorkUser> update = new LambdaUpdateWrapper<>();
        update.eq(WorkUser::getUserId, userId).set(WorkUser::getIsActive, status);

        this.update(update);
    }

    @Override
    public void deleteWorkUser(String userId) {
        LambdaUpdateWrapper<WorkUser> update = new LambdaUpdateWrapper<>();
        update.eq(WorkUser::getUserId, userId).set(WorkUser::getIsDeleted, 1);
        this.update(update);
    }

    @Override
    public void updateWorkUserOnline(String userId, Integer imStatus) {
        LambdaUpdateWrapper<WorkUser> update = new LambdaUpdateWrapper<>();
        update.eq(WorkUser::getUserId, userId).set(WorkUser::getImStatus, imStatus);
        this.update(update);
    }

    @Override
    public void updateWorkUserDepartment(@NotBlank String userId, @NotBlank  String departmentId, String merchantId) {
        String deptType = INNER_DEPT_TYPE;
        if(CharSequenceUtil.isNotEmpty(merchantId)){
            deptType = MERCHANT_TYPE;
        }
        LambdaUpdateWrapper<WorkUser> update = new LambdaUpdateWrapper<>();
        update.eq(WorkUser::getUserId, userId)
                .set(CharSequenceUtil.isNotEmpty(merchantId), WorkUser::getMerchantId, merchantId)
                .set(WorkUser::getDeptType, deptType)
                .set(WorkUser::getDeptId, departmentId);
        this.update(update);
    }

    @Override
    public List<WorkUser> queryWorkUsersExcludeSelfUserId(String userId) {
        LambdaQueryWrapper<WorkUser> query = new LambdaQueryWrapper<>();
        query.ne(WorkUser::getUserId, userId);
        query.eq(WorkUser::getIsDeleted, 0);
        query.eq(WorkUser::getIsActive, 1);
        return this.list(query);
    }

    @Override
    public List<WorkUser> queryByWorkUserIds(List<String> workUserIds) {
        LambdaQueryWrapper<WorkUser> query = new LambdaQueryWrapper<>();
        query.in(WorkUser::getWorkUserId, workUserIds);
        query.eq(WorkUser::getIsActive, 1);
        return this.list(query);
    }

    @Override
    public void relateTemplate(String workUserId, String templateId) {
        LambdaUpdateWrapper<WorkUser> update = new LambdaUpdateWrapper<>();
        update.eq(WorkUser::getWorkUserId, workUserId).set(WorkUser::getTemplateId, templateId);
        this.update(update);
    }

    @Override
    public List<WorkUser> queryByTemplateId(String templateId) {
        LambdaQueryWrapper<WorkUser> query = new LambdaQueryWrapper<>();
        query.eq(WorkUser::getTemplateId, templateId);
        return this.list(query);
    }

    @Override
    public List<WorkUser> queryByUserIds(List<String> userIds) {
        return this.list(new LambdaQueryWrapper<WorkUser>().in(WorkUser::getUserId, userIds));
    }

}
