<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntOrderVolumeMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntOrderVolume">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="volume_id" jdbcType="VARCHAR" property="volumeId"/>
        <result column="skill_id" jdbcType="VARCHAR" property="skillId"/>
        <result column="skill_code" jdbcType="VARCHAR" property="skillCode"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="volume" jdbcType="INTEGER" property="volume"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_shunt_order_volume(volume_id,skill_id,skill_code,user_id,user_name,volume)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.volumeId},#{entity.skillId},#{entity.skillCode},#{entity.userId},#{entity.userName},#{entity.volume})
        </foreach>
    </insert>

    <update id="clearOrderVolume">
        update  work_shunt_order_volume set volume = 0
    </update>
</mapper>

