<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTypeMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillType">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="skill_type_id" jdbcType="VARCHAR" property="skillTypeId"/>
        <result column="skill_name" jdbcType="VARCHAR" property="skillTypeName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_skill_type(skill_type_id,skill_name,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.skillTypeId},#{entity.skillName},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

