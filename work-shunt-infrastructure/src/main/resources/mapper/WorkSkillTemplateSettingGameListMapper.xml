<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateSettingGameListMapper">
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSettingGameList">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="template_setting_id" jdbcType="VARCHAR" property="templateSettingId"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_skill_template_setting_game_list(template_setting_id,game_id,game_name,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.templateSettingId},#{entity.gameId},#{entity.gameName},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

