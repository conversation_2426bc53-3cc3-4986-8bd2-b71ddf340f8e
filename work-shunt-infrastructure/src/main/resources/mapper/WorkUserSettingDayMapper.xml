<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingDayMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDay">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="setting_day_id" jdbcType="VARCHAR" property="settingDayId"/>
        <result column="work_user_id" jdbcType="VARCHAR" property="workUserId"/>
        <result column="work_month" jdbcType="VARCHAR" property="month"/>
        <result column="work_day" jdbcType="VARCHAR" property="day"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_user_setting_day(setting_day_id,work_user_id,work_month,work_day,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.settingDayId},#{entity.workUserId},#{entity.workMonth},#{entity.workDay},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

