<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateSettingMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplateSetting">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="template_setting_id" jdbcType="VARCHAR" property="templateSettingId"/>
        <result column="skill_template_id" jdbcType="VARCHAR" property="skillTemplateId"/>
        <result column="work_skill_id" jdbcType="VARCHAR" property="workSkillId"/>
        <result column="rule_max_num" jdbcType="VARCHAR" property="maxLimit"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        work_skill_template_setting(template_setting_id,skill_template_id,work_skill_id,rule_max_num,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.templateSettingId},#{entity.skillTemplateId},#{entity.workSkillId},#{entity.ruleMaxNum},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

