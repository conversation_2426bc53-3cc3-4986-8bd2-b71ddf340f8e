<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkill">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="skill_id" jdbcType="VARCHAR" property="skillId"/>
        <result column="skill_name" jdbcType="VARCHAR" property="skillName"/>
        <result column="skill_code" jdbcType="VARCHAR" property="skillCode"/>
        <result column="skill_type_id" jdbcType="VARCHAR" property="skillTypeId"/>
        <result column="is_skill_relation_game" jdbcType="BOOLEAN" property="skillRelationGame"/>
        <result column="skill_sort" jdbcType="INTEGER" property="skillSort"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="early_start" jdbcType="INTEGER" property="earlyStart"/>
        <result column="early_end" jdbcType="INTEGER" property="earlyEnd"/>
        <result column="middle_start" jdbcType="INTEGER" property="middleStart"/>
        <result column="middle_end" jdbcType="INTEGER" property="middleEnd"/>
        <result column="high_start" jdbcType="INTEGER" property="highStart"/>
        <result column="high_end" jdbcType="INTEGER" property="highEnd"/>
        <result column="expert_start" jdbcType="INTEGER" property="expertStart"/>
        <result column="expert_end" jdbcType="INTEGER" property="expertEnd"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        work_skill(skill_id,skill_name,skill_code,skill_type_id,is_skill_relation_name,skill_sort,create_time,early_start,early_end,middle_start,middle_end,high_start,high_end,expert_start,expert_end)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.skillId},#{entity.skillName},#{entity.skillCode},#{entity.skillTypeId},#{entity.skillRelationName},#{entity.skillSort},#{entity.createTime},#{entity.earlyStart},#{entity.earlyEnd},#{entity.middleStart},#{entity.middleEnd},#{entity.highStart},#{entity.highEnd},#{entity.expertStart},#{entity.expertEnd})
        </foreach>
    </insert>

    <select id="listPage" resultType="com.pxb7.mall.workshunt.infra.model.skill.SkillPageListPO">
        select skill.skill_id    as skillId,
               skill.skill_name  as skillName,
               type.`skill_type_name` as skillTypeName,
               skill.skill_code  as skillCode,
               skill.skill_sort as sortSkill,
               type.`sort` as sortType
        from work_skill as skill
                 left join work_skill_type as type on skill.skill_type_id = type.skill_type_id
        where skill.is_deleted = 0 order by sortType desc, skill.skill_sort asc
    </select>

    <select id="all" resultType="com.pxb7.mall.workshunt.infra.model.skill.UserSkillAllPO">
        select skill.skill_id    as skillId,
               skill.skill_name  as skillName,
               type.`skill_type_name` as skillTypeName,
               skill.skill_code  as skillCode,
               skill.skill_type_id as skillTypeId,
               skill.is_skill_relation_game as skillRelationGame,
               skill.`early_start` as earlyStart,
               skill.`early_end` as earlyEnd,
               skill.middle_start as middleStart,
               skill.middle_end as middleEnd,
               skill.high_start as highStart,
               skill.high_end as highEnd,
               skill.expert_start as expertStart,
               skill.expert_end as expertEnd,
               skill.skill_sort as sort
        from work_skill as skill
                 left join work_skill_type as type on skill.skill_type_id = type.skill_type_id
        where skill.is_deleted = 0 order by skill.skill_sort asc

    </select>


</mapper>

