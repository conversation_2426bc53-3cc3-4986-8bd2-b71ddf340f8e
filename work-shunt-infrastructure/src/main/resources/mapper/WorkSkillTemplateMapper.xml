<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkSkillTemplateMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkSkillTemplate" >
      <result  column="id" jdbcType="INTEGER" property="id" />
       <result  column="skill_template_id" jdbcType="VARCHAR" property="skillTemplateId" />
       <result  column="template_title" jdbcType="VARCHAR" property="templateTitle" />
       <result  column="create_time" jdbcType="TIMESTAMP" property="createTime" />
     </resultMap>
        
    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_skill_template(skill_template_id,template_title,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.skillTemplateId},#{entity.templateTitle},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

