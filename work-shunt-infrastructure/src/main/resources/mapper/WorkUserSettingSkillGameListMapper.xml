<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingSkillGameListMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="settting_skill_id" jdbcType="VARCHAR" property="setttingSkillId"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_user_setting_skill_game_list(settting_skill_id,game_id,game_name,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.setttingSkillId},#{entity.gameId},#{entity.gameName},#{entity.createTime})
        </foreach>
    </insert>

    <select id="queryBySettingSkillId" resultType="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList">
        <foreach collection="ids" item="id" separator="union all">
            SELECT  id,settting_skill_id,game_id,game_name,create_time  FROM work_user_setting_skill_game_list      WHERE  settting_skill_id = #{id}
        </foreach>
    </select>
</mapper>

