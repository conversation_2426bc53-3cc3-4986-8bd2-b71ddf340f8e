<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntRuleMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRule">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="shunt_rule_id" jdbcType="VARCHAR" property="shuntRuleId"/>
        <result column="skill_id" jdbcType="VARCHAR" property="skillId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_shunt_rule(shunt_rule_id,skill_id,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.shuntRuleId},#{entity.skillId},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

