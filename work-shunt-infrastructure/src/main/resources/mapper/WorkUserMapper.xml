<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUser">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_user_id" jdbcType="VARCHAR" property="workUserId"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="dept_type" jdbcType="VARCHAR" property="deptType"/>
        <result column="im_status" jdbcType="SMALLINT" property="imStatus"/>
        <result column="is_active" jdbcType="BOOLEAN" property="isActive"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_user(work_user_id,pk_dept_id,pk_user_id,create_time,dept_type,merchant_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.workUserId},#{entity.pkDeptId},#{entity.pkUserId},#{entity.createTime},#{entity.deptType},#{entity.merchantId})
        </foreach>
    </insert>

    <select id="getWorkUserBySkillNotNUll" resultMap="BaseResultMap">
        select * from work_user wuser join (
            select distinct skill.work_user_id as wUserId from work_user_setting_skill skill
            where 1=1
                <if test="workUserId != null and workUserId != ''">
                   and skill.work_user_id not in (#{workUserId})
                </if>
        ) res1 on wuser.work_user_id = res1.wUserId
    </select>

    <select id="getWorkUserBySkillCode" resultMap="BaseResultMap">
        SELECT wUser.* from work_user wUser
            join work_user_setting_skill usrSkil on wUser.work_user_id = usrSkil.work_user_id
            join work_skill wSkill on usrSkil.work_skill_id = wSkill.skill_id
        where wSkill.skill_code = #{skillCode} and wUser.is_active = 1 and wUser.is_deleted = 0 and usrSkil.is_able = '1'
    </select>

    <select id="getWorkUserBySkillCodeAndGameId" resultMap="BaseResultMap">
        SELECT wUser.*  from work_user wUser
                                 join work_user_setting_skill usrSkil on wUser.work_user_id = usrSkil.work_user_id
                                 join work_user_setting_skill_game_list wussgl on usrSkil.setting_skill_id  = wussgl.settting_skill_id
                                 join work_skill wSkill on usrSkil.work_skill_id = wSkill.skill_id
        where wSkill.skill_code =  #{skillCode} and wUser.is_active = 1 and wUser.is_deleted = 0 and wussgl.game_id =  #{gameId};

    </select>

    <select id="workSkillCodeGroupShunt" resultMap="BaseResultMap">
        SELECT wUser.* from work_user wUser
                                join work_user_setting_skill usrSkil on wUser.work_user_id = usrSkil.work_user_id
                                left join work_user_setting_skill_game_list usrSkil_gameId on usrSkil_gameId.settting_skill_id = usrSkil.setting_skill_id
                                join work_skill wSkill on usrSkil.work_skill_id = wSkill.skill_id
        where wSkill.skill_code =  #{po.skillCode} and wUser.dept_type = #{po.deptType} and usrSkil_gameId.game_id = #{po.gameId} and usrSkil.is_able = '1'
    </select>
</mapper>

