<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkTimeTemplateMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkTimeTemplate">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_template_id" jdbcType="VARCHAR" property="timeTemplateId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_time_template(work_template_id,title,start_time,end_time,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.workTemplateId},#{entity.title},#{entity.startTime},#{entity.endTime},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

