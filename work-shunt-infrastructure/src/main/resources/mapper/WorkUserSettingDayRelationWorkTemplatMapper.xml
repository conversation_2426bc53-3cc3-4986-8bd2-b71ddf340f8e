<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingDayRelationWorkTemplatMapper">
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingDayRelationWorkTemplat">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="setting_day_id" jdbcType="VARCHAR" property="settingDayId"/>
        <result column="work_template_id" jdbcType="VARCHAR" property="workTemplateId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_user_setting_day_relation_work_templat(setting_day_id,work_template_id,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.settingDayId},#{entity.workTemplateId},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

