<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLog">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="shunt_log_id" jdbcType="VARCHAR" property="shuntLogId"/>
        <result column="shunt_type" jdbcType="VARCHAR" property="shuntType"/>
        <result column="work_game_id" jdbcType="VARCHAR" property="workGameId"/>
        <result column="work_game_name" jdbcType="VARCHAR" property="workGameName"/>
        <result column="work_user_num" jdbcType="INTEGER" property="workUserNum"/>
        <result column="work_time" jdbcType="TIMESTAMP" property="workTime"/>
        <result column="work_out_time" jdbcType="TIMESTAMP" property="workOutTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="skill_ids" jdbcType="VARCHAR" property="skillIds"/>
        <result column="pk_user_names" jdbcType="VARCHAR" property="pkUserNames"/>
        <result column="req_type" jdbcType="VARCHAR" property="reqType"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        work_shunt_log(shunt_log_id,shunt_type,work_game_id,work_game_name,work_user_num,work_time,work_out_time,create_time,skill_ids,pk_user_names,req_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.shuntLogId},#{entity.shuntType},#{entity.workGameId},#{entity.workGameName},#{entity.workUserNum},#{entity.workTime},#{entity.workOutTime},#{entity.createTime},#{entity.skillIds},#{entity.pkUserNames},#{entity.reqType})
        </foreach>
    </insert>


    <select id="pages" resultMap="BaseResultMap">

        select wsl.* from work_shunt_log wsl
        join
        work_shunt_log_skill_codes wslsc
        on wsl.shunt_log_id  = wslsc.shunt_log_id
        where
        wsl.is_deleted = false

        <if test="po.shuntType != null and po.shuntType != ''">
            and   wsl.shunt_type = #{po.shuntType}
        </if>

        <if test="po.workGameId != null and po.workGameId != ''">
            and   wsl.work_game_id = #{po.workGameId}
        </if>

        <if test="po.workOutData != null and po.workOutData != ''">
            and   wsl.pk_user_names like #{po.workOutData}
        </if>

        <if test="po.workSkillId != null and po.workSkillId != ''">
            and   wslsc.skill_id = #{po.workSkillId}
        </if>

        <if test="po.workStartTime != null and po.workStartTime != ''">
            and   wsl.work_time >= #{po.workStartTime}
        </if>

        <if test="po.workEndTime != null and po.workEndTime != ''">
            and   #{po.workEndTime} > wsl.work_time
        </if>

        order by  create_time desc

    </select>

</mapper>

