<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogDetailMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogDetail" >
      <result  column="id" jdbcType="INTEGER" property="id" />
       <result  column="shunt_log_id" jdbcType="VARCHAR" property="shuntLogId" />
       <result  column="create_time" jdbcType="TIMESTAMP" property="createTime" />
       <result  column="shunt_log_detail_id" jdbcType="VARCHAR" property="shuntLogDetailId" />
       <result  column="work_user_id" jdbcType="VARCHAR" property="workUserId" />
       <result  column="pk_user_id" jdbcType="VARCHAR" property="pkUserId" />
       <result  column="pk_user_name" jdbcType="VARCHAR" property="pkUserName" />
       <result  column="skill_id" jdbcType="VARCHAR" property="skillId" />
       <result  column="skill_code" jdbcType="VARCHAR" property="skillCode" />
       <result  column="shunt_status" jdbcType="VARCHAR" property="shuntStatus" />
     </resultMap>
        
    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_shunt_log_detail(shunt_log_id,create_time,shunt_log_detail_id,work_user_id,pk_user_id,pk_user_name,skill_id,skill_code,shunt_status)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.shuntLogId},#{entity.createTime},#{entity.shuntLogDetailId},#{entity.workUserId},#{entity.pkUserId},#{entity.pkUserName},#{entity.skillId},#{entity.skillCode},#{entity.shuntStatus})
        </foreach>
    </insert>
</mapper>

