<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkUserSettingSkillMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkill" >
      <result  column="id" jdbcType="INTEGER" property="id" />
       <result  column="setting_skill_id" jdbcType="VARCHAR" property="settingSkillId" />
       <result  column="work_user_id" jdbcType="VARCHAR" property="workUserId" />
       <result  column="rule_max_num" jdbcType="VARCHAR" property="maxLimit" />
       <result  column="create_time" jdbcType="TIMESTAMP" property="createTime" />
       <result  column="work_skill_id" jdbcType="VARCHAR" property="workSkillId" />
     </resultMap>
        
    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_user_setting_skill(setting_skill_id,work_user_id,rule_max_num,create_time,work_skill_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.settingSkillId},#{entity.workUserId},#{entity.ruleMaxNum},#{entity.createTime},#{entity.workSkillId})
        </foreach>
    </insert>


    <select id="queryWorkUserSettingPage"
            resultType="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillBoard">
        select
        wuss.setting_skill_id settingSkillId,
        wuss.work_skill_id as skillId,
        ws.skill_name as skillName,
        wuss.reception_max_limit as maxLimit,
        wuss.reception_session_max_limit as sessionMaxLimit,
        wu.work_user_id as workUserId,
        wu.im_status as imStatus,
        wu.dept_id as deptId,
        wu.user_id as userId
        from
        work_user_setting_skill wuss
        left join work_user wu on
        wuss.work_user_id = wu.work_user_id
        left join work_skill ws on wuss.work_skill_id = ws.skill_id

        where wuss.is_able = '1' and wu.is_active= '1' and ws.is_deleted = '0'
        <if test="deptId != null and deptId != ''">
            AND wu.dept_id = #{deptId}
        </if>
        <if test="userIds != null and userIds.size > 0">
            AND wu.user_id in
            <foreach collection="userIds" item ="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>

        </if>
        <if test="skillId != null and skillId != ''">
            AND wuss.work_skill_id = #{skillId}
        </if>

        <if test="online != null and online == 1">
            AND wu.im_status = #{online}
        </if>

        <if test="online != null and online == 0">
            AND (wu.im_status = 2 or wu.im_status = 3 or wu.im_status = null)
        </if>
    </select>

    <select id="queryWorkUserSettingByGameIdPage" resultType="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillBoard">
        select
        wuss.setting_skill_id settingSkillId,
        wuss.work_skill_id as skillId,
        ws.skill_name as skillName,
        wuss.reception_max_limit as maxLimit,
        wuss.reception_session_max_limit as sessionMaxLimit,
        wu.work_user_id as workUserId,
        wu.im_status as imStatus,
        wu.dept_id as deptId,
        wu.user_id as userId
        from
        work_user_setting_skill wuss
        left join work_user wu on
        wuss.work_user_id = wu.work_user_id
        left join work_skill ws on wuss.work_skill_id = ws.skill_id
        left join work_user_setting_skill_game_list wussgl on wussgl.settting_skill_id  = wuss.setting_skill_id
        where wuss.is_able = '1' and wu.is_active= '1' and ws.is_deleted = '0'
        <if test="deptId != null and deptId != ''">
            AND wu.dept_id = #{deptId}
        </if>
        <if test="userIds != null and userIds.size > 0">
            AND wu.user_id in
            <foreach collection="userIds" item ="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>

        </if>
        <if test="skillId != null and skillId != ''">
            AND wuss.work_skill_id = #{skillId}
        </if>

        <if test="online != null and online == 1">
            AND wu.im_status = #{online}
        </if>

        <if test="online != null and online == 0">
            AND (wu.im_status = 2 or wu.im_status = 3 or wu.im_status = null)
        </if>

        <if test="gameId != null and gameId != ''">
            AND wussgl.game_id = #{gameId}
        </if>
    </select>

    <select id="queryBySKillIdsAndGameId" resultType="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkUserSettingSkillGameList">
        select
        wussgl.id,
        wussgl.settting_skill_id,
        wussgl.game_id,
        wussgl.game_name,
        wussgl.create_time
        from
            work_user_setting_skill wuss ,
            work_user_setting_skill_game_list wussgl
        where
            wuss.setting_skill_id = wussgl.settting_skill_id
          and wuss.work_skill_id in
        <foreach collection="skillIds" item ="skillId" open="(" close=")" separator=",">
            #{skillId,jdbcType=VARCHAR}
        </foreach>
          and wuss.is_able = 1
          and wussgl.game_id =  #{gameId};
    </select>
</mapper>

