<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntLogSkillCodesMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntLogSkillCodes">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="shunt_log_id" jdbcType="VARCHAR" property="shuntLogId"/>
        <result column="skill_id" jdbcType="VARCHAR" property="skillId"/>
        <result column="skill_name" jdbcType="VARCHAR" property="skillName"/>
        <result column="skill_code" jdbcType="VARCHAR" property="skillCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_shunt_log_skill_codes(shunt_log_id,skill_id,skill_name,skill_code,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.shuntLogId},#{entity.skillId},#{entity.skillName},#{entity.skillCode},#{entity.createTime})
        </foreach>
    </insert>
</mapper>

