<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.workshunt.infra.repository.db.mapper.WorkShuntRuleSettingConditionMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.workshunt.infra.repository.db.entity.WorkShuntRuleSettingCondition">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="setting_condition_id" jdbcType="VARCHAR" property="settingConditionId"/>
        <result column="shunt_rule_id" jdbcType="VARCHAR" property="shuntRuleId"/>
        <result column="condition_type" jdbcType="VARCHAR" property="conditionType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="shunt_rule_type" jdbcType="VARCHAR" property="shuntRuleType"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into work_shunt_rule_setting_condition(setting_condition_id,condition_type,create_time,shunt_rule_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.settingConditionId},#{entity.conditionType},#{entity.createTime},#{entity.shuntRuleType})
        </foreach>
    </insert>
</mapper>

