<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false" >
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <!-- 日志存放路径 -->
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <property name="LOG_HOME" value="${user.home}/data/weblog/${APP_NAME}"/>
    <!-- 日志输出格式 -->
    <property name="LOG_PATTERN" value="[%date] [%X{tid:--}] [%thread] [%-40.40logger{39}] [%level] - %msg%n"/>
    <property name="CONSOLE_LOG_PATTERN" value="%clr([%date]){faint} %clr([%X{tid:--}]){yellow} %clr([%thread]){faint} %clr([%-40.40logger{39}]){cyan} %clr([%-5level]) - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <!--skywalking日志拦截 -->
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout" >
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <!--控制台输出过滤条件 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <!--    上报skywalking-->
    <appender name="grpc-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>${LOG_PATTERN}</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- info日志输出 -->
    <appender name="InfoFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/info/info.log</File>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/info/info-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <!--skywalking日志拦截 -->
        <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- error日志输出 -->
    <appender name="ErrorFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/error/error.log</File>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/error/error-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <!--skywalking日志拦截 -->
        <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="async-InfoFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的256/5,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!--  更改默认的队列的深度,该值会影响性能.默认值为256  -->
        <queueSize>512</queueSize>
        <!-- 引用同步，将其包装为异步 -->
        <appender-ref ref="InfoFile"/>
    </appender>

    <appender name="async-ErrorFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的256/5,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!--  更改默认的队列的深度,该值会影响性能.默认值为256  -->
        <queueSize>512</queueSize>
        <!-- 引用同步，将其包装为异步 -->
        <appender-ref ref="ErrorFile"/>
    </appender>

    <!--系统操作日志-->
    <root level="info">
        <appender-ref ref="async-InfoFile"/>
        <appender-ref ref="async-ErrorFile"/>
        <appender-ref ref="grpc-log"/>
    </root>

    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

</configuration>