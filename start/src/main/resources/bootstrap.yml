# test
nacos:
  address: ************:8848
  username: pxb7_test
  password: pxb7_test
  namespace: pxb7_${spring.profiles.active}

# dev
#nacos:
#  address: ************:8848
#  username: develop
#  password: develop2024.
#  namespace: pxb7_dev

spring:
  main:
    allow-circular-references: true
    banner-mode: off
  web:
    resources:
      add-mappings: false
  config:
    import: classpath:sentinel-component.yaml
  mvc:
    servlet:
      load-on-startup: 1
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  dynamic:
    tp:
      enabledBanner: false  # 不打印banner图
  application:
    name: work-shunt
  profiles:
    active: test
  datasource:
    dynamic:
      primary: mysql
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 500
            idle-timeout: 600000
            max-lifetime: 1800000
            connection-timeout: 30000
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      discovery:
        server-addr: ${nacos.address}
        namespace: ${nacos.namespace}
      config:
        server-addr: ${nacos.address}
        # 配置文件格式
        file-extension: yaml
        # 共享配置
        shared-configs:
          - dataId: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true  # 必须配置，否则自动刷新不生效
        extension-configs:
          - dataId: ${spring.application.name}-dtp-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
#            refresh: true  # 必须配置，否则自动刷新不生效
#        refresh-enabled: true
        namespace: ${nacos.namespace}

management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    db:
      enabled: false
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "health"
