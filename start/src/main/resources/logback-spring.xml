<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    
    <!-- 关闭logback启动时打印的无效日志 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
    
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <!-- 日志存放路径 -->
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <property name="LOG_HOME" value="${user.home}/data/weblog/${APP_NAME}"/>
    
    
    <!-- 根据环境区分日志格式 -->
    <springProfile name="prod,uat">
        <!--
             ARMS环境使用的日志格式
            参考: https://help.aliyun.com/zh/arms/application-monitoring/use-cases/associate-trace-ids-with-business-logs?scm=20140722.S_help%40%40%E6%96%87%E6%A1%A3%40%40158582.S_RQW%40ag0%2BBB2%40ag0%2BBB1%40ag0%2Bhot%2Bos0.ID_158582-RL_trace-LOC_doc%7EUND%7Eab-OR_ser-V_4-P0_0&spm=a2c4g.11186623.0.i1
        -->
        <property name="LOG_PATTERN" value="[%date] [TID:%X{EagleEye-TraceID:--}] [%thread] [%-40.40logger{39}] [%level] - %msg%n"/>
        <property name="CONSOLE_LOG_PATTERN" value="%clr([%date]){faint} %clr([TID:%X{EagleEye-TraceID:--}]){yellow} %clr([%thread]){faint} %clr([%-40.40logger{39}]){cyan} %clr([%-5level]) - %msg%n"/>
    </springProfile>
    
    <springProfile name="!(prod | uat)">
        <!-- SkyWalking环境使用的日志格式 -->
        <property name="LOG_PATTERN" value="[%date] [%X{tid:--}] [%thread] [%-40.40logger{39}] [%level] - %msg%n"/>
        <property name="CONSOLE_LOG_PATTERN" value="%clr([%date]){faint} %clr([%X{tid:--}]){yellow} %clr([%thread]){faint} %clr([%-40.40logger{39}]){cyan} %clr([%-5level]) - %msg%n"/>
    </springProfile>
    
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            
            <springProfile name="!(prod | uat)">
                <!--skywalking日志拦截 -->
                <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                    <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                </layout>
            </springProfile>
            
            <springProfile name="prod,uat">
                <layout class="ch.qos.logback.classic.PatternLayout">
                    <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                </layout>
            </springProfile>
            
            <charset>UTF-8</charset>
        </encoder>
    
    </appender>
    
    <!--    &lt;!&ndash;    上报skywalking&ndash;&gt;-->
    <!--    <appender name="grpc-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">-->
    <!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
    <!--            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">-->
    <!--                <Pattern>${LOG_PATTERN}</Pattern>-->
    <!--            </layout>-->
    <!--        </encoder>-->
    <!--    </appender>-->
    
    <!-- info日志输出 -->
    <appender name="InfoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/info/info.log</File>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/info/info-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <!--日志文件保留天数 -->
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
        
        <springProfile name="!(prod | uat)">
            <!--skywalking日志拦截 -->
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
        </springProfile>
        
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <!--  表示不输出ERROR及以上日志-->
            <onMatch>DENY</onMatch>
            <!--  表示输出ERROR以下日志-->
            <onMismatch>ACCEPT</onMismatch>
        </filter>
    </appender>
    
    <!-- error日志输出 -->
    <appender name="ErrorFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/error/error.log</File>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/error/error-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
            <!--日志文件保留天数 -->
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
        
        <springProfile name="!(prod | uat)">
            <!--skywalking日志拦截 -->
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
        </springProfile>
        
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <appender name="async-InfoFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的256/5,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!--  更改默认的队列的深度,该值会影响性能.默认值为256  -->
        <queueSize>512</queueSize>
        <!-- 引用同步，将其包装为异步 -->
        <appender-ref ref="InfoFile"/>
    </appender>
    
    <appender name="async-ErrorFile" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的256/5,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!--  更改默认的队列的深度,该值会影响性能.默认值为256  -->
        <queueSize>512</queueSize>
        <!-- 引用同步，将其包装为异步 -->
        <appender-ref ref="ErrorFile"/>
    </appender>
    
    <logger name="com.pxb7.mall.exception.SysExceptionAdvice" level="DEBUG"/>
    
    <!-- 根据环境配置日志输出 -->
    <springProfile name="!(prod | uat)">
        <root level="info">
            <appender-ref ref="async-InfoFile"/>
            <appender-ref ref="async-ErrorFile"/>
            <!--            <appender-ref ref="grpc-log"/>-->
            <appender-ref ref="console"/>
        </root>
    </springProfile>
    
    <springProfile name="prod,uat">
        <root level="info">
            <appender-ref ref="async-InfoFile"/>
            <appender-ref ref="async-ErrorFile"/>
            <!--            <appender-ref ref="console"/>-->
        </root>
    </springProfile>


</configuration>