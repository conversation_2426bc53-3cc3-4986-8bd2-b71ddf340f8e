package com.pxb7.mall.workshunt;

import lombok.extern.slf4j.Slf4j;
import org.dromara.dynamictp.core.spring.EnableDynamicTp;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {"com.pxb7.mall", "com.alibaba.cola"})
@EnableDiscoveryClient
@EnableAsync
@EnableDubbo
@EnableDynamicTp
@EnableRetry
@Slf4j
public class Application {
    public static void main(String[] args) {

        ConfigurableApplicationContext run = SpringApplication.run(Application.class, args);

        log.info("===> 环境: {} 启动成功 -----------------------------------------------------------------------------",
                run.getEnvironment().getProperty("spring.profiles.active"));

    }
}
