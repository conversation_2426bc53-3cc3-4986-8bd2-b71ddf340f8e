package com.pxb7.mall.workshunt.client.work;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ShuntSkillReqDTO implements Serializable {

    /**
     * ShuntTypeEnums.class IM_REGION(1, "IM域"), product_REGION(2, "商品工单域"), After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    private String domain;
    /**
     * 请求的技能CODE
     */
    private List<String> skillCodes;
    /**
     * 技能等级：1初级2中级3高级4专家
     */
    private int level;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 表达请求调度的人员数量
     */
    private int personCount;

    /**
     * 是否在线
     */
    private boolean online;
}
