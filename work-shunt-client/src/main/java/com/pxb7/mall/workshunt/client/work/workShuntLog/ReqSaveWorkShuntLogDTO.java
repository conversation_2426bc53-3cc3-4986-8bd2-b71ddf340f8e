package com.pxb7.mall.workshunt.client.work.workShuntLog;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


                                            
@Data
public class ReqSaveWorkShuntLogDTO implements Serializable {

    /**
        * 主键
        */
        private Long id ;
        /**
        * 分流日志ID
        */
        private String shuntLogId ;
        /**
        * 请求类型：枚举 参考类
        */
        private String shuntType ;
        /**
        * 请求技能ID
        */
        private String workSkillId ;
        /**
        * 技能名称
        */
        private String workSkillName ;
        /**
        * 请求游戏ID
        */
        private String workGameId ;
        /**
        * 请求游戏名称
        */
        private String workGameName ;
        /**
        * 请求人数
        */
        private Long workUserNum ;
        /**
        * 请求时间
        */
        private LocalDateTime workTime ;
        /**
        * 返回数据
        */
        private String workOutData ;
        /**
        * 返回时间
        */
        private LocalDateTime workOutTime ;
    }