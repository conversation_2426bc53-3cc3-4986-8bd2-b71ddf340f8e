package com.pxb7.mall.workshunt.client.api;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.workshunt.client.dto.request.QuerySkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QuerySkillReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillGameRepDTO;
import com.pxb7.mall.workshunt.client.dto.response.SkillRepDTO;

public interface WorkSkillServiceI {

    /**
     * Queries skills based on the provided criteria.
     *
     * @return a MultiResponse containing a list of SkillRepDTO objects that match the query criteria.
     *
     */
    MultiResponse<SkillRepDTO> querySkill(QuerySkillReqDTO dto);

    /**
     * Queries games bound to a specific skill code.
     *
     * @param dto The request object containing the skill code for which to retrieve the bound games.
     * @return a MultiResponse containing a list of SkillGameRepDTO objects that match the given skill code.
     */
    MultiResponse<SkillGameRepDTO> queryGameByBindSkill(QuerySkillGameReqDTO dto);



}
