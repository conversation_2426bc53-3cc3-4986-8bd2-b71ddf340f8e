package com.pxb7.mall.workshunt.client.work.response.dataBoard;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkUserDataBoardPageListRespDTO implements Serializable {

    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 工作用户ID
     */
    private String workUserId;

    /**
     * 用户名称
     */
    private String userName;
    /**
     * 部门类型1公司部门2号商部门
     */
    private String deptType;
    /**
     * 号商ID
     */
    private String merchantId;
    /**
     *  Im 在线状态 1:在线 0:不在线  -1:用户不是客服
     */
    private int imOnLineState;


    private String skillId;

    /**
     * 技能名称
     */
    private String skillName;
    /**
     * 接待量
     */
    private int orderNum;
    /**
     * 设置接待上线
     */
    private Long ruleMaxNum;

    private List<String> gameName;

    private List<String> gameIds;


    /**
     * 模版：是否覆盖客服最大接待量
     */
    private Boolean isOverrideMaxLimit;


    /**
     * 客服最大会话数
     */
    private Long sessionMaxLimit;

    /**
     * 当前会话并发数
     */
    private Integer currentSessionNum;



    /**
     * 规则设置了房间上限
     */
    private Boolean hasRoomMaxLimit;

    /**
     * 规则设置了并发最大会话数
     */
    private Boolean hasSessionMaxLimit;


    private String settingSkillId;

    private String templateId;

}