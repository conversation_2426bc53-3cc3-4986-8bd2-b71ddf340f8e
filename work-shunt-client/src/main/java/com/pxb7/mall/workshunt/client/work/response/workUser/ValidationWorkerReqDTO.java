package com.pxb7.mall.workshunt.client.work.response.workUser;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationWorkerReqDTO implements Serializable {

    /**
     * 请求所在域
     *  ShuntTypeEnums.class
     *  IM_REGION(1, "IM域"),
     *  product_REGION(2, "商品工单域"),
     *  After_sale_REGION(3, "售后工单域"),
     *  complain_REGION(4, "客诉工单域");
     */
    @NotEmpty(message = "业务域不能为空")
    private String domain;
    /**
     * 号商ID
     * 过滤号商的客服
     */
    private String merchantId;

    /**
     * 客服ID
     */
    @NotEmpty(message = "客服id")
    private List<String> userIds;

    /**
     * 技能编码
     */
    @NotEmpty(message = "技能编码不能为空")
    private String skillCode;

    /**
     * 需要分配客服的人数
     */
    private int count = 1;

    private String gameId;
}
