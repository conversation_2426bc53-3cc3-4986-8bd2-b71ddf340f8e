package com.pxb7.mall.workshunt.client.work;

import lombok.Data;

import java.io.Serializable;

@Data
public class ShuntSkillRespDTO implements Serializable {

    /**
     * ShuntTypeEnums.class IM_REGION(1, "IM域"), product_REGION(2, "商品工单域"), After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    private String domain;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 技能CODES
     */
    private String skillCodes;
    /**
     * 分流用户ID
     */
    private String userID;
    /**
     * 分流用户名称
     */
    private String userName;
    /**
     * 请求排班调度 会话ID 用于用于确认 排版调度
     */
    private String shuntLogId;
}
