package com.pxb7.mall.workshunt.client.api;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserByNoOverFlowReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.QueryWorkUserRepDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;

public interface  WorkUserServiceI {


    /**
     * 查询号商工作客服列表
     * @param dto
     * @return
     */
    MultiResponse<QueryWorkUserRepDTO> queryMerchantWorkUser(QueryWorkUserReqDTO dto);

    /**
     * 根据非溢出条件查询人员
     * @param dto
     * @return
     */
    MultiResponse<GetWorkShuntRespDTO> queryWorkUserByNoOverflowCondition(QueryWorkUserByNoOverFlowReqDTO dto);

}
