package com.pxb7.mall.workshunt.client.dto.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryWorkUserReqDTO implements Serializable {


    /**
     * 查询的技能编码
     */
    private String skillCode;

    /**
     * 号商Id
     */
    @NotEmpty(message = "号商ID")
    private String merchantId;
}
