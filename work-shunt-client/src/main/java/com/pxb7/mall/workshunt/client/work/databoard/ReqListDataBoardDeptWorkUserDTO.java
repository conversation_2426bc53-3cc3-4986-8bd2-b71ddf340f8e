package com.pxb7.mall.workshunt.client.work.databoard;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReqListDataBoardDeptWorkUserDTO implements Serializable {

    /**
     * 第几页
     */
    @NotNull(message = "分页参数不能为空")
    private Integer pageIndex;
    /**
     * 每页条数
     */
    @NotNull(message = "分页参数不能为空")
    @Min(value = 1, message = "pageSize不能小于1")
    @Max(value = 100, message = "pageSize不能大于100")
    private Integer pageSize;


    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 用户IDS
     */
     private List<String> userIds;


     private String skillId;

     private String gameId;

     //0 离线  1 在线
     private Integer imStatus;
}