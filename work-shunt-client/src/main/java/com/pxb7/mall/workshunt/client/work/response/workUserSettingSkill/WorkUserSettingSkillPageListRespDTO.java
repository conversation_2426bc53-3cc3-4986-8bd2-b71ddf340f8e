package com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill;

import com.pxb7.mall.workshunt.client.work.response.GameDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkUserSettingSkillPageListRespDTO implements Serializable {
    /**
     * 技能ID
     */
    private String settingSkillId;
    /**
     * 用户ID
     */
    private String workUserId;
    /**
     * 技能ID
     */
    private String workSkillId;
    /**
     * 技能名称
     */
    private String workSkillName;
    /**
     * 技能分类ID
     */
    private String skillTypeId;
    /**
     * 类型名称
     */
    private String skillTypeName;

    /**
     * 技能编码
     */
    private String skillCode;

    /**
     * 是否关联游戏1：是0否
     */
    private Boolean skillRelationName;

    /**
     * 设置接待上线
     */
    private String ruleMaxNum;
    /**
     * 设置游戏列表
     */
    private List<GameDTO> gameDTOList;
}