package com.pxb7.mall.workshunt.client.work.workShuntLog;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReqVerifyLogIdDTO implements Serializable {
    /**
     *  ShuntTypeEnums.class
     *  IM_REGION(1, "IM域"),
     *  product_REGION(2, "商品工单域"),
     *  After_sale_REGION(3, "售后工单域"),
     *  complain_REGION(4, "客诉工单域");
     */
    @NotEmpty(message = "请求的业务域不能为空")
    private String domain;
    /**
     * 请求的技能CODE
     */
    @NotEmpty(message = "客服技能不能为空")
    private List<String> skillCodes;
    /**
     * 用户ID
     */
    @NotEmpty(message = "客服ID")
    private String userId;

    /**
     * 请求ID(预留标识)
     */
    private String requestId;

}