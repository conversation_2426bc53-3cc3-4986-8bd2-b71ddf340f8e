package com.pxb7.mall.workshunt.client.work.response.workUser;

import lombok.Data;

import java.io.Serializable;

@Data
public class WorkUserDeptUserRespDTO implements Serializable {
    /**
     * 部门类型 1公司部门 2号商部门
     */
    private String deptType;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门ID
     */
    private String deptName;
    /**
     * 用户名称
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userTelPhone;
    /**
     * 用户头像
     */
    private String avatar ;

    /**
     *  Im 在线状态  1:在线，2：离线，3:暂离
     */
    private Integer imOnLineState;
}