package com.pxb7.mall.workshunt.client.work.response.workUser;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqWorkShuntGroupDTO implements Serializable {
    /**
     * 部门类型 1博淳 2意竞 3号商
     */
    @NotNull(message = "部门类型不能为空")
    private String deptType;
    /**
     * 技能ID
     */
    @NotNull(message = "技能编码不能为空")
    private String skillCode;
    /**
     * 游戏ID
     */
    @NotNull(message = "游戏ID不能为空")
    private String gameId;

    /**
     * 0:默认后台   1：IM
     */
    @NotNull(message = "业务不能为空")
    private String sourceType;
}
