package com.pxb7.mall.workshunt.client.work.workShuntLog;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReqGetWorkShuntDTO implements Serializable {
    /**
     * ShuntTypeEnums.class IM_REGION(1, "IM域"), product_REGION(2, "商品工单域"), After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    @NotNull(message = "请求的业务域")
    private String domain;
    /**
     * 请求的技能CODE，用逗号分隔，
     */
    @NotNull(message = "客服技能")
    private Set<String> skillCodes;
    /**
     * 技能等级：1初级2中级3高级4专家
     */
    private int level;

    /**
     * 号商ID
     * 需要分配号商的客服
     */
    private String merchantId;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 表达请求调度的人员数量， Req num
     */
    private int personCount = 1;

    /**
     * 请求ID(预留标识)
     */
    private String requestId;

    /**
     * 过滤掉的用户ID
     */
    private Set<String> filterUserIds;
}