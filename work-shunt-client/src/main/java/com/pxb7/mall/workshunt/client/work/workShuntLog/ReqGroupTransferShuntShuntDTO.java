package com.pxb7.mall.workshunt.client.work.workShuntLog;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqGroupTransferShuntShuntDTO implements Serializable {
    /**
     * ShuntTypeEnums.class IM_REGION(1, "IM域"), product_REGION(2, "商品工单域"), After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    @NotEmpty(message = "业务域")
    private String domain;
    /**
     * 目标 -> 用户ID
     */
    @NotEmpty(message = "目标客服ID")
    private String targetUserId;
    /**
     * 技能CODE
     */
    @NotEmpty(message = "技能编码")
    private String skillCode;

    /**
     * 用户 A
     */
    @NotEmpty(message = "客服ID")
    private String userId;

    /**
     * 用户 A --> 业务会话ID
     */
    @NotEmpty(message = "会话ID")
    private String shuntLogId;

    /**
     * 请求ID(预留标识)
     */
    private String requestId;

}