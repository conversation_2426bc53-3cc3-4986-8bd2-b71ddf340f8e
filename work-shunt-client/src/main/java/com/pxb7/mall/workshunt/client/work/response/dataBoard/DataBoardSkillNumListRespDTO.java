package com.pxb7.mall.workshunt.client.work.response.dataBoard;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(exclude = {"orderNum", "ruleMaxNum"})
public class DataBoardSkillNumListRespDTO implements Serializable {


    /**
     * 客服ID(set中过滤重复的数据)
     */
    private String workerUserId;

    /**
     * 技能ID(set中过滤重复的数据)
     */
    private String skillId;

    /**
     * 技能名称
     */
    private String skillName;
    /**
     * 接待上限数
     */
    private int orderNum;
    /**
     * 设置接待上线
     */
    private int ruleMaxNum;
}