package com.pxb7.mall.workshunt.client.work;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetWorkVerifyLogIdDubboDTO implements Serializable {
    /**
     * ShuntTypeEnums.class IM_REGION(1, "IM域"), product_REGION(2, "商品工单域"), After_sale_REGION(3, "售后工单域"),
     * complain_REGION(4, "客诉工单域");
     */
    @NotEmpty(message = "业务域名称")
    private String domain;

    @NotEmpty(message = "客服技能")
    private List<String> skillCodes;
    /**
     * 用户ID
     */
    @NotEmpty(message = "客服ID")
    private String userId;

    /**
     * 请求ID(预留标识)
     */
    private String requestId;

}
