package com.pxb7.mall.workshunt.client.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryWorkUserByNoOverFlowReqDTO implements Serializable {

    @NotNull(message = "请求的技能不为空")
    private String skillCode;
    private String gameId;
    private String merchantId;
    @NotNull(message = "请求的业务域不为空")
    private String domain;
    private int personCount = 1;
}
