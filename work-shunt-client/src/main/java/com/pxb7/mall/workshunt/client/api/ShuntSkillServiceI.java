package com.pxb7.mall.workshunt.client.api;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.workshunt.client.dto.request.GetSkillGameReqDTO;
import com.pxb7.mall.workshunt.client.dto.request.QueryOnlineWorkUserReqDTO;
import com.pxb7.mall.workshunt.client.dto.response.WorkUserOnlineRespDTO;
import com.pxb7.mall.workshunt.client.work.BatchDeduceOrderVolumeReqDTo;
import com.pxb7.mall.workshunt.client.work.GetWorkVerifyLogIdDubboDTO;
import com.pxb7.mall.workshunt.client.work.response.GetSkillGameRespDTO;
import com.pxb7.mall.workshunt.client.work.response.WorkUserSelectDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workShuntLog.GetWorkShuntRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ReqWorkShuntGroupDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.ValidationWorkerReqDTO;
import com.pxb7.mall.workshunt.client.work.response.workUser.WorkUserDeptUserRespDTO;
import com.pxb7.mall.workshunt.client.work.response.workUserSettingSkill.WorkUserSettingSkillPageListRespDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqAddGroupWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGetWorkShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqGroupTransferShuntShuntDTO;
import com.pxb7.mall.workshunt.client.work.workShuntLog.ReqVerifyLogIdDTO;
import jakarta.validation.constraints.NotNull;

public interface ShuntSkillServiceI {

    /**
     * 通过排班规则筛选客服
     * @param reqDTO
     * @return
     */
    MultiResponse<GetWorkShuntRespDTO> shunt(ReqGetWorkShuntDTO reqDTO);
    /**
     * 确认 排班任务 ->
     */
    SingleResponse<Boolean> verifyDetailsId(ReqVerifyLogIdDTO dto);
    /**
     * 结束 客服排单任务 ->
     */
    SingleResponse<Boolean> closeDetailsLogId(GetWorkVerifyLogIdDubboDTO dto);

    /**
     * 批量减少接单量
     * @param dto
     * @return
     */
    SingleResponse<Boolean> batchDeduceOrderVolume(BatchDeduceOrderVolumeReqDTo dto);


    /**
     * 通知类型： 指定固定用户技能  工作量+1
     * 验证是否在规则范围内
     * 结束会话的时候，还是需要调用 closeDetailsId 来结束会话
     */
    SingleResponse<GetWorkShuntRespDTO> addWorkloadToUser(ReqAddGroupWorkShuntDTO dto);


    /**
     * 通知类型：A转B -> A退出任务 B增加任务
     *  B 结束任务 closeDetailsId 结束任务
     *  A 需要调用 closeDetailsId 来结束会话
     */
    SingleResponse<GetWorkShuntRespDTO> groupTransferShunt(ReqGroupTransferShuntShuntDTO dto);


    /**
     * 获取指定技能+指定游戏的客服 -- 所有客服
     */
    MultiResponse<WorkUserDeptUserRespDTO> getSkillWorkUserByCodeAndGameId(ReqWorkShuntGroupDTO reqWorkShuntGroupDTO);


    /**
     * 获取技能下下有所有用户 -- 不限制游戏条件
     */
    MultiResponse<WorkUserSelectDeptUserRespDTO> getSkillWorkUserByCode(@NotNull String skillCode);

    /**
     * 获取技能下的所有用户 -- 不限制游戏
     * @param dto
     * @return
     */
    MultiResponse<WorkUserOnlineRespDTO> getOnlineWorkUserBy(@NotNull QueryOnlineWorkUserReqDTO dto);


    /**
     * 获取指定用户  - 所有技能
     */
    MultiResponse<WorkUserSettingSkillPageListRespDTO> getWorkUserSettingSkillByPkUserId(@NotNull String pkUserId);

    /**
     * 验证业务域提交的技能和客服是否匹配
     * 如果不匹配，按照指定技能编码就重新生分配客服
     * @param dto
     * @return
     */
    MultiResponse<GetWorkShuntRespDTO> validationWorker(ValidationWorkerReqDTO dto);

    /**
     * 获取指定技能+客服的 游戏列表
     * @param dto
     * @return
     */
    MultiResponse<GetSkillGameRespDTO> querySkillGame(GetSkillGameReqDTO dto);

}
