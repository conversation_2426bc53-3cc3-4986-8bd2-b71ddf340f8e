package com.pxb7.mall.workshunt.client.work.workShuntLog;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqAddGroupWorkShuntDTO implements Serializable {
    /**
     *  ShuntTypeEnums.class
     *  IM_REGION(1, "IM域"),
     *  product_REGION(2, "商品工单域"),
     *  After_sale_REGION(3, "售后工单域"),
     *  complain_REGION(4, "客诉工单域");
     */
    @NotEmpty(message = "业务域")
    private String domain;
    /**
     * 技能CODE
     */
    @NotEmpty(message = "客服技能")
    private String skillCode;
    /**
     * 用户ID
     */
    @NotEmpty(message = "客服ID")
    private String userId;

    /**
     * 请求ID(预留标识)
     */
    private String requestId;

    /**
     *  true: 校验人员信息 不添加交接量数据
     *  false: 添加交接量数据，不校验人员信息
     */
    private Boolean filterAddWorkload = false;

}