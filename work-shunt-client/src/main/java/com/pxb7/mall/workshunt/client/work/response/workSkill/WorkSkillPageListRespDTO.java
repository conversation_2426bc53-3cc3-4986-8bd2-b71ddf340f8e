package com.pxb7.mall.workshunt.client.work.response.workSkill;

import lombok.Data;

import java.io.Serializable;

@Data
public class WorkSkillPageListRespDTO implements Serializable {

    /**
     * 技能ID
     */
    private String skillId;
    /**
     * 技能名称
     */
    private String skillName;
    /**
     * 技能code
     */
    private String skillCode;
    /**
     * 技能类型ID
     */
    private String skillTypeId;


    /**
     * 技能类型名称
     */
    private String skillTypeName;

    /**
     * 是否关联游戏1：是0否
     */
    private String isRelationGame;
    /**
     * 排序值
     */
    private int skillSort;


    private int skillNum;
    /**
     * 初级数量
     */
   private int  earlyCount;
    /**
     * 中级数量
     */
   private int  middleCount;
    /**
     * 高级数量
     */
   private int  highCount;
    /**
     * 专家数量
     */
   private int  expertCount;
}