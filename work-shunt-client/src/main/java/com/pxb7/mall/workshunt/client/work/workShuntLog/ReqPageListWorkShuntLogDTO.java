package com.pxb7.mall.workshunt.client.work.workShuntLog;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReqPageListWorkShuntLogDTO implements Serializable {
    /**
     * 第几页
     */
    @NotNull(message = "pageIndex必填")
    private Integer pageIndex;
    /**
     * 每页条数
     */
    @NotNull(message = "pageSize必填")
    @Min(value = 1, message = "pageSize不能小于1")
    @Max(value = 100, message = "pageSize不能大于100")
    private Integer pageSize;
    /**
     *      *  ShuntTypeEnums.class
     *      *  IM_REGION(1, "IM域"),
     *      *  product_REGION(2, "商品工单域"),
     *      *  After_sale_REGION(3, "售后工单域"),
     *      *  complain_REGION(4, "客诉工单域");
     */
    private String shuntType;
    /**
     * 请求技能ID
     */
    private String workSkillId;
    /**
     * 请求游戏ID
     */
    private String workGameId;
    /**
     * 请求时间
     */
    private String workStartTime;
    /**
     * 请求时间
     */
    private String workEndTime;
    /**
     * 返回数据
     */
    private String workOutData;
}